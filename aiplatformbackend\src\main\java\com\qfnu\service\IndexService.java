package com.qfnu.service;

import com.qfnu.model.vo.DashboardStatsVO;
import com.qfnu.model.vo.TopRankingsVO;
import com.qfnu.model.vo.ToolDistributionVO;

public interface IndexService {

    /**
     * 获取首页仪表盘统计数据
     *
     * @return 统计数据VO
     */
    DashboardStatsVO getDashboardStats();

    /**
     * 获取排行榜数据
     *
     * @return 排行榜数据VO
     */
    TopRankingsVO getTopRankings();

    /**
     * 获取工具类别分布数据
     *
     * @return 工具类别分布数据VO
     */
    ToolDistributionVO getToolDistribution();
}
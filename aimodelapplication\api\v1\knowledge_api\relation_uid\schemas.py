# -*- coding: utf-8 -*-

"""
知识库相关API的数据模型定义
"""

from pydantic import BaseModel
from typing import List, Optional


# ==================== 提示词管理相关数据模型 ====================
class TermDefinition(BaseModel):
    """名词定义模型"""
    name: str
    definition: str


class BusinessPrompt(BaseModel):
    """业务提示模型"""
    content: str


class ExtraEvidence(BaseModel):
    """额外依据模型"""
    name: str
    description: str


class PromptManagementRequest(BaseModel):
    """提示词管理请求模型"""
    businessIntroduction: str
    termDefinitions: List[TermDefinition]
    businessPrompts: List[BusinessPrompt]
    extraEvidence: List[ExtraEvidence]
    knowledgeBaseId: Optional[str] = None  # 可选的知识库ID字段


class PromptManagementResponse(BaseModel):
    """提示词管理响应模型"""
    businessIntroduction: str
    termDefinitions: List[TermDefinition]
    businessPrompts: List[BusinessPrompt]
    extraEvidence: List[ExtraEvidence]

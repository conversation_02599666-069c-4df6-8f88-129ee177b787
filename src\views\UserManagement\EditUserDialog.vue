<template>
  <a-modal
    :visible="visible"
    @update:visible="(val) => emit('update:visible', val)"
    title="编辑用户"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :confirmLoading="loading"
  >
    <a-form
      :model="formState"
      :rules="rules"
      ref="formRef"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item label="用户名" name="username">
        <a-input v-model:value="formState.username" placeholder="请输入用户名" readonly/>
      </a-form-item>
      <a-form-item label="类型" name="type">
        <a-select
          v-model:value="formState.type"
          placeholder="请选择类型"
          :options="typeOptions"
        />
      </a-form-item>
      <a-form-item label="产品组" name="products">
        <a-select
          v-model:value="formState.products"
          mode="multiple"
          placeholder="请选择产品组"
          :options="productOptions"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import { updateUser } from '@/utils/api';

const props = defineProps<{
  visible: boolean;
  typeOptions: { value: string; label: string }[];
  productOptions: { value: string; label: string }[];
  userData: {
    id: string;
    username: string;
    type: string;
    products: string;
  };
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}>();

const formRef = ref<FormInstance>();
const loading = ref(false);

const formState = reactive({
  username: '',
  type: '',
  products: [] as string[],
});

// 监听userData变化，更新表单数据
watch(() => props.userData, (newData) => {
  if (newData) {
    formState.username = newData.username;
    formState.type = newData.type;
    formState.products = newData.products ? newData.products.split(',').map(p => p.trim()) : [];
  }
}, { immediate: true });

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' },
  ],
  products: [
    { required: true, type: 'array', message: '请选择产品组', trigger: 'change' },
  ],
};

const resetForm = () => {
  formRef.value?.resetFields();
};

const handleSubmit = () => {
  formRef.value?.validate().then(() => {
    loading.value = true;
    console.log("id", props.userData.id)
    updateUser({
      managementId: props.userData.id,
      // realName: formState.username,
      userType: formState.type,
      product: formState.products.join(','),
    }).then(() => {
      message.success('更新用户成功');
      emit('success');
      emit('update:visible', false);
    }).catch((error) => {
      message.error(error.message || '更新用户失败');
    }).finally(() => {
      loading.value = false;
    });
  }).catch(error => {
    console.log('验证失败:', error);
  });
};

const handleCancel = () => {
  emit('update:visible', false);
  resetForm();
};
</script>
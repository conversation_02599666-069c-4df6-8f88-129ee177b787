package com.qfnu.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@ApiModel(value = "AuthUserDto", description = "AuthUser传输对象")
public class AuthUserDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户唯一标识，自增主键
     */
    @ApiModelProperty("用户唯一标识，自增主键")
    private String userId;

    /**
     * 用户名，唯一
     */
    @ApiModelProperty("用户名，唯一")
    private String username;

    /**
     * 用户密码
     */
    @ApiModelProperty("用户密码")
    private String password;

    /**
     * 用户邮箱
     */
    @ApiModelProperty("用户邮箱")
    private String email;

    /**
     * 工号
     */
    @ApiModelProperty("工号")
    private String jobNo;

    /**
     * 团队名称（JSON格式存储多个团队）
     */
    @ApiModelProperty("团队名称（JSON格式存储多个团队）")
    private String teamNames;

    /**
     * 用户名字
     */
    @ApiModelProperty("用户名字")
    private String firstName;

    /**
     * 用户姓氏
     */
    @ApiModelProperty("用户姓氏")
    private String lastName;

    /**
     * 是否为超级用户，0 表示否，1 表示是
     */
    @ApiModelProperty("是否为超级用户，0 表示否，1 表示是")
    private String isSuperuser;

    /**
     * 是否为工作人员，0 表示否，1 表示是
     */
    @ApiModelProperty("是否为工作人员，0 表示否，1 表示是")
    private String isStaff;

    /**
     * 用户是否活跃，0 表示禁用，1 表示活跃
     */
    @ApiModelProperty("用户是否活跃，0 表示禁用，1 表示活跃")
    private String isActive;

    /**
     * 用户注册时间
     */
    @ApiModelProperty("用户注册时间")
    private Date dateJoined;

    /**
     * 用户最后登录时间
     */
    @ApiModelProperty("用户最后登录时间")
    private Date lastLogin;


    @ApiModelProperty("用户角色")
    private String role;

    /**
     * 用户真实姓名
     */
    @ApiModelProperty("用户真实姓名")
    private String realName;

    /**
     * 用户类型
     */
    @ApiModelProperty("用户类型")
    private String userType;

    /**
     * 所属产品线或项目
     */
    @ApiModelProperty("所属产品线或项目")
    private String product;

}
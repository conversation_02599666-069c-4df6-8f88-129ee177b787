import { defAxios as axios } from "./http";

export function getApi(url: string, parameter?: any, option?: any) {
  return axios({
    url,
    method: "get",
    params: parameter,
    ...option,
  });
}

export function createApi(url: string, parameter?: any, config: any = {}) {
  return axios({
    url,
    method: "post",
    data: parameter,
    ...config,
  });
}

export function updateApi(url: string, data?: any, parameter?: any) {
  return axios({
    url,
    method: "put",
    params: parameter,
    data: data,
  });
}

export function deleteApi(url: string, data?: any, parameter?: any) {
  return axios({
    url,
    method: "delete",
    params: parameter,
    data: data,
  });
}

export function getDownLoadFileBlob(url: string, parameter?: any) {
  return axios({
    url,
    method: "get",
    params: parameter,
    responseType: "blob",
  });
} 
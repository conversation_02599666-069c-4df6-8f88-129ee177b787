from typing import Dict, List, Any, Optional

from db.mysql.mysql_pool import MySQLPool
from logger import logging
import json


class ApiRelationsModel:
    """API关系模型类 - 专门处理 api_relations 表"""

    def __init__(self):
        self.table = "api_relations"
        self.primary_key = "id"
        self.logger = logging()
        self.db_pool = MySQLPool()

    async def initialize(self):
        """初始化连接池"""
        await self.db_pool.initialize()

    async def close(self):
        """关闭连接池，释放资源"""
        await self.db_pool.close()

    async def create_relation(
            self,
            relation_uid: str,
            task_id: str,
            api_name: str,
            api_type: str,
            data_model: Optional[str] = None,
            relation_type: Optional[str] = None,
            details: Optional[Dict] = None,  # Dict 类型
            status : int = 0,
            api_path: Optional[str] = None,  # 新增字段
            api_method: Optional[str] = None  # 新增字段
    ):
        try:
            sql = f"""
                INSERT INTO {self.table} (
                    relation_uid, task_id, api_name, api_type,
                    data_model, relation_type, details,status,
                    api_path, api_method
                ) VALUES (%s, %s, %s, %s, %s, %s, %s,%s,%s,%s)
            """
            params = (
                relation_uid,
                task_id,
                api_name,
                api_type,
                data_model,
                relation_type,
                json.dumps(details) if details else None,
                0, # 默认状态为0 待确认
                api_path,
                api_method
            )

            return await self.db_pool.execute(sql, params)

        except Exception as e:
            self.logger.error(f"创建API关系记录失败: {str(e)}")
            raise

    async def get_relations_by_api_name(self, api_name: str) -> List[Dict[str, Any]]:
        """
        根据API名称查询所有关联记录

        Args:
            api_name: API名称

        Returns:
            List[Dict]: 查询结果列表（包含解析后的JSON字段）
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE api_name = %s
            """
            results = await self.db_pool.fetch_all(sql, (api_name,))

            for result in results:
                if result.get("details") and isinstance(result["details"], str):
                    result["details"] = json.loads(result["details"])

            return results

        except Exception as e:
            self.logger.error(f"查询API关系记录失败: {str(e)}")
            raise

    async def get_relations_by_relation_uid(self, relation_uid: str) -> List[Dict[str, Any]]:
        """
        根据relation_uid查询关联记录

        Args:
            relation_uid: 关系唯一标识

        Returns:
            List[Dict]: 查询结果列表
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE relation_uid = %s
            """
            results = await self.db_pool.fetch_all(sql, (relation_uid,))

            for result in results:
                if result.get("details") and isinstance(result["details"], str):
                    result["details"] = json.loads(result["details"])

            return results

        except Exception as e:
            self.logger.error(f"查询API关系记录失败: {str(e)}")
            raise

    async def delete_relation_by_uid(self, relation_uid: str) -> bool:
        """
        根据relation_uid删除关系记录

        Args:
            relation_uid: 关系唯一标识

        Returns:
            bool: 是否删除成功
        """
        try:
            sql = f"""
                DELETE FROM {self.table}
                WHERE id = %s
            """
            affected_rows = await self.db_pool.execute(sql, (relation_uid,))
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"删除API关系记录失败: {str(e)}")
            raise

    async def update_relation_details(self, relation_uid: str, details: Dict) -> bool:
        """
        更新指定relation_uid的关系详情

        Args:
            relation_uid: 关系唯一标识
            details: 新的详情内容（字典）

        Returns:
            bool: 是否更新成功
        """
        try:
            sql = f"""
                UPDATE {self.table}
                SET details = %s
                WHERE relation_uid = %s
            """
            affected_rows = await self.db_pool.execute(sql, (json.dumps(details), relation_uid))
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"更新API关系详情失败: {str(e)}")
            raise

    async def get_all_relations(self, relation_uid: str = None) -> List[Dict[str, Any]]:
        """
        获取所有API关系记录（可选按知识库ID过滤）

        Args:
            relation_uid: 知识库ID（可选）

        Returns:
            List[Dict]: 所有记录列表（包含解析后的JSON字段）
        """
        try:
            if relation_uid:
                sql = f"""
                    SELECT * FROM {self.table}
                    WHERE relation_uid = %s
                """
                params = (relation_uid,)
            else:
                sql = f"SELECT * FROM {self.table}"
                params = ()

            results = await self.db_pool.fetch_all(sql, params)

            # 解析 JSON 字段
            for result in results:
                if result.get("details") and isinstance(result["details"], str):
                    try:
                        result["details"] = json.loads(result["details"])
                    except json.JSONDecodeError:
                        result["details"] = None
                        self.logger.warning(f"JSON 解析失败: {result['details']}")

            return results

        except Exception as e:
            self.logger.error(f"获取所有API关系记录失败: {str(e)}")
            raise

    async def count_relations_by_api_name(self, api_name: str) -> int:
        """
        统计某个API名称对应的关系数量

        Args:
            api_name: API名称

        Returns:
            int: 关系数量
        """
        try:
            sql = f"""
                SELECT COUNT(*) AS count 
                FROM {self.table}
                WHERE api_name = %s
            """
            result = await self.db_pool.fetch_one(sql, (api_name,))
            return result.get("count", 0)

        except Exception as e:
            self.logger.error(f"统计API关系数量失败: {str(e)}")
            raise

    async def get_relations_by_task_id(self, task_id: str, relation_uid: str = None) -> List[Dict[str, Any]]:
        """
        根据任务ID（和可选的知识库ID）查询关联记录

        Args:
            task_id: 任务唯一标识
            relation_uid: 知识库ID（可选）

        Returns:
            List[Dict]: 查询结果列表（包含解析后的JSON字段）
        """
        try:
            if relation_uid:
                sql = f"""
                    SELECT * FROM {self.table}
                    WHERE task_id = %s AND relation_uid = %s
                """
                params = (task_id, relation_uid)
            else:
                sql = f"""
                    SELECT * FROM {self.table}
                    WHERE task_id = %s
                """
                params = (task_id,)

            results = await self.db_pool.fetch_all(sql, params)

            # 解析 JSON 字段
            for result in results:
                if result.get("details") and isinstance(result["details"], str):
                    try:
                        result["details"] = json.loads(result["details"])
                    except json.JSONDecodeError:
                        result["details"] = None
                        self.logger.warning(f"JSON 解析失败: {result['details']}")

            return results

        except Exception as e:
            self.logger.error(f"查询API关系记录失败: {str(e)}")
            raise

    async def update_relation_status(self, relation_uid: str, status: int) -> bool:
        """
        更新指定relation_uid的关系状态

        Args:
            relation_uid: 关系唯一标识
            status: 新的状态值

        Returns:
            bool: 是否更新成功
        """
        try:
            sql = f"""
                UPDATE {self.table}
                SET status = %s
                WHERE id = %s
            """
            affected_rows = await self.db_pool.execute(sql, (status, relation_uid))
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"更新关系状态失败: {str(e)}")
            raise

    async def get_relation_by_id(self, relation_id: str) -> Optional[Dict[str, Any]]:
        """
        根据主键ID查询一条API关系记录

        Args:
            relation_id: 关系记录的主键ID

        Returns:
            Optional[Dict]: 查询结果字典，如果没有找到返回 None
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE id = %s
            """
            result = await self.db_pool.fetch_one(sql, (relation_id,))

            if result and result.get("details") and isinstance(result["details"], str):
                try:
                    result["details"] = json.loads(result["details"])
                except json.JSONDecodeError:
                    result["details"] = None  # 或者保留原始字符串
                    self.logger.warning(f"JSON 解析失败: {result['details']}")

            return result

        except Exception as e:
            self.logger.error(f"查询API关系记录失败: {str(e)}")
            raise
    async def delete_relations_by_task_id(self, task_id: str) -> bool:
        """
        根据 task_id 删除 api_relations 表中的记录

        Args:
            task_id: 任务唯一标识

        Returns:
            bool: 是否删除成功
        """
        try:
            sql = f"""
                DELETE FROM {self.table}
                WHERE task_id = %s
            """
            affected_rows = await self.db_pool.execute(sql, (task_id,))
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"根据 task_id 删除关系记录失败: {str(e)}")
            raise

    async def get_relations_count_by_status(self, status: int) -> int:
        """
        根据状态查询关系数量

        Args:
            status: 状态值

        Returns:
            int: 满足条件的关系数量
        """
        try:
            sql = f"""
                SELECT COUNT(*) AS count 
                FROM {self.table}
                WHERE status = %s
            """
            result = await self.db_pool.fetch_one(sql, (status,))
            return result.get("count", 0)

        except Exception as e:
            self.logger.error(f"根据状态查询关系数量失败: {str(e)}")
            raise


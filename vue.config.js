const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
  transpileDependencies: true,
  lintOnSave: false,
  devServer: {
    proxy: {
      '/api': {
        target: 'http://10.113.7.86:8083',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'
        }
      },
    },
    client: {
      overlay: {
        // 只关掉 ResizeObserver 相关错误
        runtimeErrors: (error) => {
          // error.message 精确匹配
          if (error?.message === 'ResizeObserver loop completed with undelivered notifications.') {
            console.error(error);
            return false;
          }
          return true;
        }
      }
    }
  }
})

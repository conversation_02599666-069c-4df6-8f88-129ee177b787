<template>
  <a-modal
    :visible="visible"
    @update:visible="(val) => emit('update:visible', val)"
    title="提交记录"
    @cancel="handleCancel"
    width="800px"
    class="submission-history-modal"
    :footer="null"
  >
    <a-table
      :dataSource="submissions"
      :columns="columns"
      :pagination="pagination"
      @change="handleTableChange"
      class="submission-table"
    >
      <template #reviewStatus="{ text }">
        <a-tag :color="getStatusColor(text)">{{ text }}</a-tag>
      </template>
      <template #tags="{ text }">
        <a-tag v-for="tag in text" :key="tag" class="mr-1">{{ tag }}</a-tag>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import type { TablePaginationConfig } from 'ant-design-vue'
import { getMyToolList } from '@/utils/api'

// 定义数据接口
interface ToolRecord {
  toolName: string
  category: string
  tags: string
  submissionTime: number
  reviewStatus: string | number
}

// 定义审核状态类型
type ReviewStatusType = '审核中' | '已通过' | '已拒绝' | '未知状态'

// 定义状态到颜色的映射
const statusColorMap: Record<ReviewStatusType, string> = {
  '审核中': 'processing',
  '已通过': 'success',
  '已拒绝': 'error',
  '未知状态': 'default'
}

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
}>()

const submissions = ref<ToolRecord[]>([])
const loading = ref(false)

// 获取提交记录数据
const fetchSubmissions = async () => {
  loading.value = true
  try {
    const response = await getMyToolList({
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    })
    
    // 处理时间戳和状态映射
    submissions.value = response.records.map((record: ToolRecord) => {
      // 将数字状态码转换为文字描述
      const statusMap: Record<string, ReviewStatusType> = {
        '0': '审核中',
        '1': '已通过',
        '2': '已拒绝'
      }
      
      // 确保索引类型安全
      const statusCode = String(record.reviewStatus)
      const statusText = statusMap[statusCode] || '未知状态'
      
      return {
        ...record,
        submissionTime: new Date(record.submissionTime).toLocaleString(),
        reviewStatus: statusText,
        tags: record.tags ? record.tags.split(',') : []
      }
    })
    
    pagination.total = response.total
  } catch (error) {
    console.error('获取提交记录失败:', error)
  } finally {
    loading.value = false
  }
}

const columns = [
  {
    title: '工具名称',
    dataIndex: 'toolName',
    key: 'toolName'
  },
  {
    title: '工具类型',
    dataIndex: 'category',
    key: 'category'
  },
  {
    title: '标签',
    dataIndex: 'tags',
    key: 'tags',
    slots: { customRender: 'tags' }
  },
  {
    title: '提交时间',
    dataIndex: 'submissionTime',
    key: 'submissionTime',
    sorter: true
  },
  {
    title: '状态',
    dataIndex: 'reviewStatus',
    key: 'reviewStatus',
    slots: { customRender: 'reviewStatus' }
  }
]

const pagination = reactive({
  total: 50,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true
})

const handleTableChange = (pag: TablePaginationConfig) => {
  pagination.current = pag.current || 1
  pagination.pageSize = pag.pageSize || 10
  fetchSubmissions()
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    fetchSubmissions()
  }
})

// 组件挂载时获取数据
onMounted(() => {
  if (props.visible) {
    fetchSubmissions()
  }
})

// 使用严格类型定义的状态到颜色映射
const getStatusColor = (status: ReviewStatusType) => {
  return statusColorMap[status]
}

const handleCancel = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.submission-history-modal :deep(.ant-modal-content) {
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
}

.submission-history-modal :deep(.ant-modal-header) {
  padding: 24px 24px 0;
  border-bottom: none;
}

.submission-history-modal :deep(.ant-modal-title) {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.submission-history-modal :deep(.ant-modal-body) {
  padding: 24px;
}

.submission-table :deep(.ant-table-thead > tr > th) {
  background-color: #f9fafb;
  color: #374151;
  font-weight: 500;
}

.submission-table :deep(.ant-table-tbody > tr > td) {
  color: #4b5563;
}

.submission-table :deep(.ant-pagination) {
  margin-top: 16px;
}
</style>
// utils/aiModel.ts（命名导出）
import { getAIModelResponse } from './api';

export async function queryModel(
    modelUrl: string,
    message: string,
    max_tokens: number,
    temperature: number,
    model: string
) {
    try {
        const response = await getAIModelResponse({
            messages: [{ role: "user", content: message }],
            model: model,
            max_tokens: max_tokens,
            temperature: temperature
        });

        console.log(response);

        return {
            type: 'ai',
            content: response || '',
            time: new Date().toLocaleTimeString()
        };
    } catch (e) {
        return {
            type: 'ai',
            content: `⚠️ 请求失败：${(e as Error).message}`,
            time: new Date().toLocaleTimeString()
        };
    }
}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试rerank问题
"""

import asyncio
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def debug_import_issue():
    """调试导入问题"""
    
    print("=== 调试导入问题 ===")
    
    try:
        print("1. 测试导入PrerequisiteMatchingService...")
        from aimodelapplication.service.rerank_service import PrerequisiteMatchingService
        print("✓ 成功导入PrerequisiteMatchingService")
        
        print("2. 测试初始化...")
        api_key = "sk-fdfd70db7f9b48b4b8cad01c56c3fe99"
        matching_service = PrerequisiteMatchingService(api_key)
        print("✓ 成功初始化PrerequisiteMatchingService")
        
        print("3. 测试简单匹配...")
        prerequisites = ["创建项目"]
        api_summaries = [
            {"id": "1", "summary": "项目创建", "method": "POST", "path": "/api/1"}
        ]
        
        results = await matching_service.process_multiple_prerequisites(
            prerequisites, api_summaries
        )
        
        print(f"✓ 匹配成功，结果数量: {len(results)}")
        
        if results:
            result = results[0]
            print(f"  前置依赖: {result.get('prerequisite')}")
            print(f"  匹配API: {result.get('summary')}")
            print(f"  综合分数: {result.get('final_score', 0):.4f}")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {str(e)}")
        return False
        
    except Exception as e:
        print(f"✗ 其他错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def debug_parse_function():
    """调试解析函数"""
    
    print("\n=== 调试解析函数 ===")
    
    import re
    
    def parse_prerequisites_from_llm_result(llm_result: str):
        """解析前置依赖（独立版本）"""
        try:
            prerequisites = []
            
            # 首先尝试按常见分隔符分割
            parts = re.split(r'[,，;；、\n]', llm_result.strip())
            
            for part in parts:
                part = part.strip()
                if not part:
                    continue
                
                # 移除序号前缀
                part = re.sub(r'^[0-9]+\.?\s*', '', part)
                part = re.sub(r'^[-•]\s*', '', part)
                
                # 移除括号内容
                part = re.sub(r'\([^)]*\)', '', part)
                part = re.sub(r'（[^）]*）', '', part)
                part = part.strip()
                
                # 移除其他标点符号
                part = re.sub(r'[。！？.!?]$', '', part).strip()
                
                if part and len(part) > 1:
                    prerequisites.append(part)
            
            # 去重
            unique_prerequisites = []
            for prereq in prerequisites:
                if prereq not in unique_prerequisites and len(prereq.strip()) > 1:
                    unique_prerequisites.append(prereq.strip())
            
            return unique_prerequisites
            
        except Exception as e:
            print(f"解析失败: {str(e)}")
            return []
    
    # 测试用例
    test_cases = [
        "创建项目（projectId）",
        "创建项目（projectId）、创建动态用户组（dynamicUserObjectId）"
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {test_case}")
        result = parse_prerequisites_from_llm_result(test_case)
        print(f"解析结果: {result}")


async def simulate_views_function():
    """模拟views.py中的函数调用"""
    
    print("\n=== 模拟views.py函数调用 ===")
    
    # 模拟数据
    llm_result = "创建项目（projectId）"
    api_nodes = [
        {
            "id": "api_001",
            "method": "POST",
            "path": "/api/projects",
            "summary": "项目创建",
            "name": "createProject"
        },
        {
            "id": "api_002",
            "method": "POST",
            "path": "/api/assets",
            "summary": "资产创建",
            "name": "createAsset"
        }
    ]
    
    print(f"LLM结果: {llm_result}")
    print(f"API节点数量: {len(api_nodes)}")
    
    try:
        # 模拟find_related_apis_with_rerank函数的核心逻辑
        logger.info("开始使用rerank+向量化方法进行API匹配")
        
        # 导入服务
        from aimodelapplication.service.rerank_service import PrerequisiteMatchingService
        logger.info("成功导入PrerequisiteMatchingService")
        
        # 初始化匹配服务
        api_key = "sk-fdfd70db7f9b48b4b8cad01c56c3fe99"
        matching_service = PrerequisiteMatchingService(api_key)
        logger.info("成功初始化PrerequisiteMatchingService")
        
        # 解析前置依赖
        import re
        
        prerequisites = []
        parts = re.split(r'[,，;；、\n]', llm_result.strip())
        
        for part in parts:
            part = part.strip()
            if not part:
                continue
            
            part = re.sub(r'^[0-9]+\.?\s*', '', part)
            part = re.sub(r'^[-•]\s*', '', part)
            part = re.sub(r'\([^)]*\)', '', part)
            part = re.sub(r'（[^）]*）', '', part)
            part = part.strip()
            part = re.sub(r'[。！？.!?]$', '', part).strip()
            
            if part and len(part) > 1:
                prerequisites.append(part)
        
        unique_prerequisites = []
        for prereq in prerequisites:
            if prereq not in unique_prerequisites and len(prereq.strip()) > 1:
                unique_prerequisites.append(prereq.strip())
        
        logger.info(f"解析出 {len(unique_prerequisites)} 个前置依赖: {unique_prerequisites}")
        
        if not unique_prerequisites:
            logger.warning("未能从LLM结果中解析出前置依赖")
            return []
        
        # 准备API summaries数据
        api_summaries = []
        for node in api_nodes:
            summary = node.get("summary", "").strip()
            if summary:
                api_summaries.append({
                    "id": node.get("id"),
                    "method": node.get("method"),
                    "path": node.get("path"),
                    "summary": summary,
                    "name": node.get("name", "")
                })
        
        logger.info(f"准备了 {len(api_summaries)} 个有效的API summaries")
        
        if not api_summaries:
            logger.warning("没有找到有效的API summaries")
            return []
        
        # 并行处理多个前置依赖
        logger.info(f"开始并行处理 {len(unique_prerequisites)} 个前置依赖")
        matching_results = await matching_service.process_multiple_prerequisites(
            unique_prerequisites, api_summaries
        )
        
        logger.info(f"rerank匹配完成，找到 {len(matching_results)} 个相关API")
        
        if matching_results:
            logger.info("匹配结果详情:")
            for i, result in enumerate(matching_results, 1):
                logger.info(f"  {i}. 前置依赖: {result.get('prerequisite')} → API: {result.get('summary')} "
                           f"(综合分数: {result.get('final_score', 0):.4f}, 风险: {result.get('risk_level')})")
        else:
            logger.warning("rerank匹配未找到任何结果")
        
        return matching_results
        
    except Exception as e:
        logger.error(f"模拟测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return []


if __name__ == "__main__":
    # 运行调试测试
    debug_parse_function()
    
    # 运行异步测试
    success = asyncio.run(debug_import_issue())
    
    if success:
        print("\n基础功能正常，继续测试完整流程...")
        asyncio.run(simulate_views_function())
    else:
        print("\n基础功能有问题，请检查环境配置")

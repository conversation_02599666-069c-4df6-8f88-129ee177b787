package ${package}.service.impl;

import ${package}.model.po.${className}Po;
import ${package}.mapper.${className}Mapper;
import ${package}.service.${className}Service;
import ${package}.model.dto.${className}Dto;
import ${package}.model.vo.${className}Vo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ${className}ServiceImpl implements ${className}Service {
    
    @Resource
    private ${className}Mapper mapper;
    
    @Override
    public int save(${className}Dto dto) {
        ${className}Po po = new ${className}Po();
        BeanUtils.copyProperties(dto, po);
        return mapper.insert(po);
    }
    
    @Override
    public int saveBatch(List<${className}Dto> dtoList) {
        List<${className}Po> poList = dtoList.stream()
            .map(dto -> {
                ${className}Po po = new ${className}Po();
                BeanUtils.copyProperties(dto, po);
                return po;
            })
            .collect(Collectors.toList());
        return mapper.batchInsert(poList);
    }
    
    @Override
    public int update(${className}Dto dto) {
        ${className}Po po = new ${className}Po();
        BeanUtils.copyProperties(dto, po);
        return mapper.update(po);
    }
    
    @Override
    public int delete(Long id) {
        return mapper.delete(id);
    }
    
    @Override
    public ${className}Vo getById(Long id) {
        ${className}Po po = mapper.selectById(id);
        if (po == null) {
            return null;
        }
        ${className}Vo vo = new ${className}Vo();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }
    
    @Override
    public List<${className}Vo> getList(${className}Dto dto) {
        ${className}Po po = new ${className}Po();
        BeanUtils.copyProperties(dto, po);
        return mapper.selectList(po).stream()
            .map(item -> {
                ${className}Vo vo = new ${className}Vo();
                BeanUtils.copyProperties(item, vo);
                return vo;
            })
            .collect(Collectors.toList());
    }
    
    @Override
    public List<${className}Dto> batchAddOrUpdate(List<${className}Dto> dtoList) {
        List<${className}Po> poList = dtoList.stream()
                .map(dto -> {
                    ${className}Po po = new ${className}Po();
                    BeanUtils.copyProperties(dto, po);
                    return po;
                })
                .collect(Collectors.toList());
        poList = mapper.batchAddOrUpdate(poList);
        return poList.stream()
                .map(po -> {
                    ${className}Dto dto = new ${className}Dto();
                    BeanUtils.copyProperties(po, dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }
} 
<template>
  <a-modal
    :visible="visible"
    @update:visible="(val) => emit('update:visible', val)"
    title="添加用户"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :confirmLoading="loading"
  >
    <a-form
      :model="formState"
      :rules="rules"
      ref="formRef"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item label="账号" name="account">
        <a-input v-model:value="formState.account" placeholder="请输入账号(示例：san.zhang)" />
      </a-form-item>
      <a-form-item label="用户名" name="username">
        <a-input v-model:value="formState.username" placeholder="请输入用户名(示例：zhangsan)" />
      </a-form-item>
      <a-form-item label="姓名" name="realName">
        <a-input v-model:value="formState.realName" placeholder="请输入姓名(示例：张三)" />
      </a-form-item>
      <a-form-item label="所属组别" name="type">
        <a-select
          v-model:value="formState.type"
          placeholder="请选择所属组别"
          :options="typeOptions"
        />
      </a-form-item>
      <a-form-item label="产品组" name="products">
        <a-select
          v-model:value="formState.products"
          mode="multiple"
          placeholder="请选择产品组"
          :options="productOptions"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';

const props = defineProps<{
  visible: boolean;
  typeOptions: { value: string; label: string }[];
  productOptions: { value: string; label: string }[];
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}>();

const formRef = ref<FormInstance>();
const loading = ref(false);

const formState = reactive({
  account: '',
  username: '',
  realName: '',
  type: '',
  products: [],
});

const rules = {
  account: [
    { required: true, message: '请输入账号', trigger: 'blur' },
    { min: 3, max: 20, message: '账号长度应在3-20个字符之间', trigger: 'blur' },
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
  ],
  realName: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' },
  ],
  products: [
    { required: true, type: 'array', message: '请选择产品组', trigger: 'change' },
  ],
};

const resetForm = () => {
  formRef.value?.resetFields();
};

import { saveUser } from '@/utils/api';

const handleSubmit = () => {
  formRef.value?.validate().then(() => {
    loading.value = true;
    saveUser({
      accountName: formState.account,
      username: formState.username,
      realName: formState.realName,
      userType: formState.type,
      product: formState.products.join(','),
    }).then(() => {
      message.success('添加用户成功');
      emit('success');
      emit('update:visible', false);
      resetForm();
    }).catch((error) => {
      message.error(error.message || '添加用户失败');
    }).finally(() => {
      loading.value = false;
    });
  }).catch(error => {
    console.log('验证失败:', error);
  });
};

const handleCancel = () => {
  emit('update:visible', false);
  resetForm();
};
</script>
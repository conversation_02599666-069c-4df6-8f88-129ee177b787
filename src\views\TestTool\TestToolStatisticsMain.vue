<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 搜索栏 -->
    <div class="mb-6">
      <div class="flex items-center">
        <div class="relative w-80">
          <a-input-search v-model:value="searchQuery" placeholder="搜索数据统计" class="w-full" :style="{ borderRadius: '9999px' }" />
        </div>
      </div>
    </div>

    <!-- 卡片列表 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- 总测试数据卡片 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">总测试数据</h3>
          <i class="fas fa-chart-line text-blue-500 text-xl"></i>
        </div>
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-gray-600">测试用例总数</span>
            <span class="text-2xl font-semibold text-gray-900">1,234</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600">执行成功率</span>
            <span class="text-2xl font-semibold text-green-500">95%</span>
          </div>
        </div>
      </div>

      <!-- 本周数据卡片 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">本周数据</h3>
          <i class="fas fa-calendar-week text-purple-500 text-xl"></i>
        </div>
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-gray-600">新增用例</span>
            <span class="text-2xl font-semibold text-gray-900">45</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600">执行次数</span>
            <span class="text-2xl font-semibold text-blue-500">156</span>
          </div>
        </div>
      </div>

      <!-- 故障分析卡片 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">故障分析</h3>
          <i class="fas fa-exclamation-triangle text-yellow-500 text-xl"></i>
        </div>
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-gray-600">待修复故障</span>
            <span class="text-2xl font-semibold text-red-500">8</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600">平均修复时间</span>
            <span class="text-2xl font-semibold text-gray-900">2.5h</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const searchQuery = ref('');
</script> 
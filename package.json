{"name": "ah_web", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@arco-design/web-vue": "^2.57.0", "@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-free": "^6.7.2", "@types/axios": "^0.14.4", "ant-design-vue": "^4.2.6", "axios": "^1.8.4", "chart.js": "^4.5.0", "core-js": "^3.8.3", "dhtmlx-gantt": "^9.0.13", "echarts": "^5.6.0", "element-plus": "^2.10.4", "gantt-elastic": "^1.0.12", "gantt-schedule-timeline-calendar": "^3.40.7", "openai": "^4.95.1", "vue": "^3.2.13", "vue-chartjs": "^5.3.2", "vue-gantt-schedule-timeline-calendar": "^3.0.44", "vue-router": "^4.5.0", "winston": "^3.17.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@typescript-eslint/eslint-plugin": "8.29.0", "@typescript-eslint/parser": "8.29.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-typescript": "14.5.0", "eslint": "9.23.0", "eslint-plugin-vue": "9.28.0", "tailwindcss": "^3.4.17", "typescript": "5.8.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended", "@vue/eslint-config-typescript"], "parserOptions": {"parser": "@typescript-eslint/parser", "ecmaVersion": 2020, "sourceType": "module"}, "rules": {}, "overrides": [{"files": ["*.ts", "*.tsx", "*.vue"], "rules": {}}]}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}
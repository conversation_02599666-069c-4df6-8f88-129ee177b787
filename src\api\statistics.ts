import { get<PERSON><PERSON>, create<PERSON><PERSON>, delete<PERSON>pi, update<PERSON>pi } from './request';

export interface MetricsData {
    bugs: number;
    itr: number;
    itrEscapeRate: number;
    confirmedBugs: number;
    bugEscapeRate: number;
    missed: number;
    missRate: number;
    unit: string;
  }
  
  export interface StatisticsResponse {
    data: {
      chartData: ChartDataItem[];
      itrMonthlyTrend: Record<string, MonthlyTrendItem[]>;
      bugMonthlyTrend: Record<string, MonthlyTrendItem[]>;
      missedMonthlyTrend: Record<string, MonthlyTrendItem[]>;
    };
  }
  
  export interface BugItem {
    id: number;
    title: string;
    product: number;
    product_name: string;
    severity: number;
    pri: number;
    type: string;
    status: string;
    origin: string;
    assignedto: string;
    is_bug?: string | null;
    analysis_status?: string | null;
    rectification_status?: string | null;
    openedby: string;
    openeddate: string;
    resolvedby: string;
    resolution: string;
    resolveddate: string;
    closedby?: string;
    closeddate?: string;
    feature?: string | null;
    device_form?: string | null;
    is_analyzed?: string | null;
    root_cause?: string | null;
    is_dev_introduced?: string | null;
    history_version?: string | null;
    is_test_missed?: string | null;
    test_missed_phase?: string | null;
    test_missed_analysis?: string | null;
    improvement_measures?: string | null;
    analysis_owner?: string | null;
    is_case_added?: string | null;
    case_number?: string | null;
    rectification_owner?: string | null;
    rectification_date?: string | null;
    module?: number;
    branch?: number;
    execution?: number;
    plan?: number;
    story?: number;
    task?: number;
    confirmed: number;
    activatedcount: number;
    linkbug?: string;
    case?: number;
    result?: number;
    testtask?: number;
    deleted: string;
    plantestversion?: string;
    openedbuild: string;
    assigneddate: string;
    resolvedbuild: string;
    close_type?: string;
    iscasediscovery?: string;
    xiugaiyinru?: string;
    Regressiontester?: string;
  }
  
  export interface BugListResponse {
    code: number;
    message: string;
    data: {
      list: BugItem[];
      total: number;
    };
  }
  
  export function getBugList(params: {
    analyst?: string;
    severity?: string;
    product?: string;
    bug_status?: string;
    startDate: string;
    endDate: string;
    pageSize: number;
    current: number;
  
  
    feature?:string;
    is_bug?: string,
    analysis_status?: string,
    rectification_status?: string
    responsible_persons?: string
    bug_owner?:string[]
    type?:string
    is_analyzed?:string
  
  }) {
    return getApi('/itrs', params);
  }
  
  
  
  export function updateStatistics(params: {
    feature?: string | null;
    device_form?: string | null;
    is_analyzed?: string | null;
    is_bug?: string | null;
    root_cause?: string | null;
    is_dev_introduced?: string | null;
    history_version?: string | null;
    is_test_missed?: string | null;
    bug_owner?:string |null;
    test_missed_phase?: string | null;
    test_missed_analysis?: string | null;
    improvement_measures?: string | null;
    analysis_owner?: string | null;
    is_admin?: boolean | null;
    analysis_status?: string | null;
    is_case_added?: string | null;
    case_number?: string | null;
    rectification_owner?: string | null;
    rectification_status?: string | null;
    rectification_date?: string | null;
  }) {
    return createApi('update_bug_info/', params);
  }
  
  
  export interface UserItem {
    id: number;
    user_name: string;
    account_name: string;
    real_name: string;
    user_type: string;
    super: string;
    product: string;
  }
  
  export interface UserListResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: UserItem[];
  }
  
  // 获取用户列表接口
  export function getUserList(userType?: string) {
    return getApi('users_msg/', {
      user_type: userType || 'usm_test,tgfw_test,waf_test,cloud_test,trainee_test'
    });
  }
  
  export function getBugCount(params: {
    analyst?: string;
    severity?: string;
    product?: string;
    startDate: string;
    endDate: string;
  
  }) {
    return getApi('/count_unreviewed_bugs/', params);
  }
  
  export function getProductCases(product: string, params: {
    page: number
    pageSize: number
    search?: string
  }) {
    return getApi('/product_case_list/', {
      product,
      ...params
    });
  }
  
  export function getProductStatistics(params: {
    start_year_month: string;
    end_year_month: string;
    product?: string;
  }) {
    return getApi('/product-statistics/', params);
  }
  
  // 统一参数结构
  export function getStatisticsData(params: {
    start_year_month: string;
    end_year_month: string;
    product?: string;
  }) {
    return getApi('/api/statistics/itr/', params);
  }
  
  
  
  export interface MonthlyTrendItem {
    month: string
    value: number
  }
  
  export function exportBugList(params: {
    analyst?: string;
    severity?: string;
    product?: string;
    startDate: string;
    endDate: string;
  }) {
    return getApi('/export_bug_list/', {
      ...params,
      export: 'excel' // 添加导出标识参数
    }, {
      responseType: 'blob'
    });
  }
  
  
  
  // ... 已有接口定义 ...
  
  export interface FeatureItem {
    id: number;
    product: string;
    product_id: number;
    module_name: string;
    description: string | null;
    person_responsible: string;
    group_responsible: string | null;
    level: number;
    parent_id: number;
    parent_name: string;
    create_datetime: string;
    update_datetime: string;
  }
  
  export interface FeatureListResponse {
    status: string;
    data: FeatureItem[];
    total: number;
    page: number;
    page_size: number;
    total_pages: number;
  }
  
  // ChartDataItem 类型定义，迁移自 ITRAnalysis.vue
  export interface ChartDataItem {
    name: string;
    metrics: {
      totalBugs: number;
      testerBugs: number;
      itr: number;
      itrEscapeRate: number;
      confirmedBugs: number;
      bugEscapeRate: number;
      missed: number;
      missRate: number;
    };
  }
  
  // 新增特性列表请求函数
  export function getFeatureList(params: {
    product?: string;
    module_name?: string;
    person_responsible?: string;
    level?: string;
  
    page: number;
    pageSize: number;
  }) {
    // 兼容 AxiosResponse 类型，避免类型断言报错
    return getApi('/features/', params) as Promise<any>;
  }
  
  export function deleteFeature(id: number) {
    // console.log('delete feature', id);
    return deleteApi(`/features/delete/${id}/`, {}, { method: 'DELETE' });
  }
  
  
  export interface FeatureTreeNode {
    id: number;
    module_name: string;
    children?: FeatureTreeNode[];
    product_name?: string;
  
  }
  
  // 新增接口请求函数
  export const fetchFeatureTree = (params?: { product_id?: string }): Promise<{ data: FeatureTreeNode[] }> => {
    return getApi('feature_tree/', params) as Promise<{ data: FeatureTreeNode[] }>;
  };
  
  export function createFeature(params: {
    product_id: string;
    module_name: string;
    person_responsible?: string;
    group_responsible?: string;
    parent_id?: number;
  }) {
    return createApi('/features/add/', params);
  }
  
  
  export const getFeatureResponsible = (productId: string, featureId: string) => 
    getApi(`/get_feature_responsible/?product_id=${productId}&feature_id=${featureId}`) as Promise<{
      data: { results: Array<{ user_name: string; real_name: string }> }
    }>;
  
  
    export interface AssignFeatureParams {
      bug_id: number;
      feature: string;
      analysis_owner: string;
      tag?: number; 
    }
    
    export const assignFeatures = (params: AssignFeatureParams[]) => {
      return createApi(
        '/bug/batch_update_analysis_owner/',  // URL
        params,                              // 请求体数据
        { method: 'POST' }                   // 配置项
      );
    };
  
  
  export const lockBugItems = (params: AssignFeatureParams[]) => {
    return createApi(
      '/bug/batch_lock_bugs/',  // URL
      params,                              // 请求体数据
      { method: 'POST' }                   // 配置项
    );
  };
  
  
  export function updateFeature(params: {
    feature_id: number;
    product_id: string;
    module_name: string;
    description?: string;
    person_responsible: string;
    group_responsible: string;
    parent_id?: number;
  }) {
    // 使用解构分离feature_id和其他参数
    const { feature_id, ...requestData } = params;
    return updateApi(
      `/features/edit/${feature_id}/`,  // URL包含特征ID
      requestData,                          // 请求体数据
      {}                                    // 空参数对象
    );
  }
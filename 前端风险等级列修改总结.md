# 前端风险等级列修改总结

## 修改概述

在关系确认弹窗`ConfirmWindow.vue`中新增了风险等级列，用于显示从数据库表`api_data_model_relations`中获取的`risk_level`字段数据。

## 具体修改内容

### 1. 新增风险等级列

#### 修改位置
`src/views/AIEfficiency/KnowledgeRelation/Management/ConfirmWindow.vue`

#### 表格列配置修改
在`apiModelColumns`计算属性中新增风险等级列：

```javascript
{
  title: '风险等级',
  dataIndex: 'risk_level',
  key: 'risk_level',
  width: 90,
  ellipsis: true,
  customRender: ({ text }) => {
    if (!text) return '-';
    return h(ATag, { color: getRiskLevelColor(text) }, () => text);
  }
},
```

#### 列位置
风险等级列位于状态列之前，显示顺序为：
1. ID
2. 任务ID  
3. 上游接口描述
4. 上游接口
5. 关系
6. 下游接口描述
7. 下游接口
8. **风险等级** ← 新增
9. 状态
10. 更新时间

### 2. 新增风险等级颜色函数

```javascript
// 获取风险等级颜色
const getRiskLevelColor = (riskLevel) => {
  const colorMap = {
    '低': 'success',    // 绿色
    '中': 'warning',    // 橙色  
    '高': 'error'       // 红色
  };
  return colorMap[riskLevel] || 'default';
};
```

#### 颜色映射
- **低风险**: 绿色标签 (`success`)
- **中风险**: 橙色标签 (`warning`)
- **高风险**: 红色标签 (`error`)
- **未知风险**: 默认灰色标签 (`default`)

### 3. 更新列配置选项

#### 可选列列表更新
在`apiModelAllColumns`数组中添加风险等级选项：

```javascript
const apiModelAllColumns = [
  { title: 'ID', key: 'id' },
  { title: '任务ID', key: 'taskId' },
  // ... 其他列
  { title: '风险等级', key: 'risk_level' }, // 新增
  { title: '状态', key: 'status' },
  // ... 其他列
];
```

#### 默认显示列更新
在`watch(activeTabKey)`中更新默认选中列：

```javascript
selectedColumns.value = [
  'id', 'taskId', 'api_tags', 'apiName', 'apiType', 
  'modelName', 'relationType', 'path', 
  'risk_level', // 新增
  'status', 'details'
];
```

## 数据来源

### 后端数据字段
风险等级数据来自数据库表`api_data_model_relations`的`risk_level`字段：

- **字段名**: `risk_level`
- **数据类型**: `VARCHAR(10)`
- **可能值**: `'低'`, `'中'`, `'高'`
- **默认值**: `NULL`

### 数据流程
1. **数据生成**: rerank+向量化匹配服务根据相似度分数计算风险等级
2. **数据存储**: 保存到`api_data_model_relations.risk_level`字段
3. **数据查询**: 后端API通过`get_relations_with_pagination`方法查询
4. **数据序列化**: 已修复Decimal序列化问题，确保JSON正常返回
5. **前端显示**: 在关系确认弹窗表格中显示为彩色标签

## 风险等级计算逻辑

根据rerank服务的实现，风险等级按以下规则计算：

```python
def calculate_risk_level(semantic_score, verb_score, final_score):
    if final_score >= 0.8:
        return "低"
    elif final_score >= 0.5:
        return "中"  
    else:
        return "高"
```

### 风险等级含义
- **低风险**: 综合相似度≥0.8，匹配度很高，可信度强
- **中风险**: 综合相似度0.5-0.8，匹配度中等，需要人工确认
- **高风险**: 综合相似度<0.5，匹配度较低，需要仔细审查

## 显示效果

### 表格显示
```
| ID | 任务ID | 上游接口描述 | ... | 风险等级 | 状态 |
|----|--------|-------------|-----|----------|------|
| 1  | task1  | 项目创建    | ... | [低]     | 待确认 |
| 2  | task1  | 账号创建    | ... | [中]     | 待确认 |
| 3  | task1  | 资产删除    | ... | [高]     | 待确认 |
```

### 标签颜色
- 低风险: 🟢 绿色标签
- 中风险: 🟠 橙色标签  
- 高风险: 🔴 红色标签

## 用户体验改进

### 1. 可视化风险识别
用户可以通过颜色快速识别关系的风险等级：
- 绿色标签表示高可信度的匹配
- 橙色标签表示需要注意的匹配
- 红色标签表示需要仔细审查的匹配

### 2. 批量操作优化
用户可以根据风险等级进行批量操作：
- 优先确认低风险的关系
- 重点审查高风险的关系
- 批量忽略明显错误的高风险关系

### 3. 筛选和排序
未来可以基于风险等级添加：
- 风险等级筛选器
- 按风险等级排序
- 风险等级统计

## 兼容性说明

### 1. 向后兼容
- 对于没有`risk_level`字段的历史数据，显示为"-"
- 不影响现有的确认、忽略、删除功能

### 2. 数据库兼容
- 新字段为可选字段，不影响现有数据
- 已有的关系记录`risk_level`为NULL，前端正常处理

## 测试建议

### 1. 功能测试
1. 打开关系确认弹窗
2. 验证风险等级列是否正确显示
3. 检查不同风险等级的颜色是否正确
4. 测试列设置中是否包含风险等级选项

### 2. 数据测试
1. 创建包含不同风险等级的测试数据
2. 验证前端是否正确显示各种风险等级
3. 测试NULL值的处理是否正确

### 3. 交互测试
1. 测试表格排序功能
2. 测试搜索功能是否包含风险等级
3. 测试批量操作是否正常工作

## 总结

通过添加风险等级列，用户现在可以：

1. ✅ **快速识别风险**: 通过颜色标签快速识别关系的可信度
2. ✅ **优化工作流程**: 优先处理低风险关系，重点审查高风险关系
3. ✅ **提高效率**: 减少人工审查时间，提高关系确认的准确性
4. ✅ **增强可视化**: 更直观的风险展示，改善用户体验

这个修改与后端的rerank+向量化匹配服务完美集成，为用户提供了更智能的关系确认体验。

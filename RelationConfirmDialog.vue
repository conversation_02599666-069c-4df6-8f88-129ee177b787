<template>
  <el-dialog
    title="关系确认"
    :visible.sync="dialogVisible"
    width="95%"
    :before-close="handleClose"
    class="relation-confirm-dialog"
  >
    <div class="dialog-content">
      <!-- 筛选条件 -->
      <div class="filter-bar">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-select v-model="statusFilter" placeholder="选择状态" clearable @change="handleFilterChange">
              <el-option label="待确认" value="0"></el-option>
              <el-option label="已确认" value="1"></el-option>
              <el-option label="已忽略" value="2"></el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-input 
              v-model="taskIdFilter" 
              placeholder="输入任务ID筛选" 
              clearable
              @clear="handleFilterChange"
              @keyup.enter.native="handleFilterChange"
            >
              <el-button slot="append" icon="el-icon-search" @click="handleFilterChange"></el-button>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="loadRelations">刷新</el-button>
          </el-col>
          <el-col :span="12" class="text-right">
            <span class="selected-info">已选择 {{ selectedRelations.length }} 项</span>
          </el-col>
        </el-row>
      </div>
      
      <!-- 关系列表表格 -->
      <el-table
        ref="relationTable"
        :data="relations"
        @selection-change="handleSelectionChange"
        v-loading="loading"
        style="width: 100%; margin-top: 20px;"
        max-height="500"
        stripe
        border
      >
        <!-- 选择列 -->
        <el-table-column type="selection" width="55" fixed="left"></el-table-column>
        
        <!-- ID列 -->
        <el-table-column prop="id" label="ID" width="80" fixed="left" sortable></el-table-column>
        
        <!-- 任务ID列 - 放在ID列后面 -->
        <el-table-column prop="task_id" label="任务ID" width="120" sortable>
          <template slot-scope="scope">
            <el-tag v-if="scope.row.task_id" size="mini" type="info">
              {{ scope.row.task_id }}
            </el-tag>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        
        <!-- 上游API列 -->
        <el-table-column label="上游API" width="220" show-overflow-tooltip>
          <template slot-scope="scope">
            <div v-if="scope.row.upstream_api" class="api-info">
              <el-tag :type="getMethodType(scope.row.upstream_api.method)" size="mini">
                {{ scope.row.upstream_api.method }}
              </el-tag>
              <span class="api-path">{{ scope.row.upstream_api.path }}</span>
            </div>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        
        <!-- 数据模型列 -->
        <el-table-column label="数据模型" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag v-if="scope.row.data_model?.name" type="success" size="mini">
              {{ scope.row.data_model.name }}
            </el-tag>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        
        <!-- 下游API列 -->
        <el-table-column label="下游API" width="220" show-overflow-tooltip>
          <template slot-scope="scope">
            <div v-if="scope.row.downstream_api" class="api-info">
              <el-tag :type="getMethodType(scope.row.downstream_api.method)" size="mini">
                {{ scope.row.downstream_api.method }}
              </el-tag>
              <span class="api-path">{{ scope.row.downstream_api.path }}</span>
            </div>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        
        <!-- 关系描述列 -->
        <el-table-column prop="relation_description" label="关系描述" min-width="300" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.relation_description || '-' }}</span>
          </template>
        </el-table-column>
        
        <!-- 状态列 -->
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="mini">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <!-- 创建时间列 -->
        <el-table-column prop="created_at" label="创建时间" width="160" sortable>
          <template slot-scope="scope">
            <span>{{ formatTime(scope.row.created_at) }}</span>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        style="margin-top: 20px; text-align: right;"
      >
      </el-pagination>
    </div>
    
    <!-- 底部按钮 -->
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        type="primary" 
        @click="confirmRelations" 
        :disabled="selectedRelations.length === 0"
        :loading="confirming"
      >
        确认选中关系 ({{ selectedRelations.length }})
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'RelationConfirmDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    knowledgeBaseId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      relations: [],
      selectedRelations: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
      statusFilter: '',
      taskIdFilter: '',
      loading: false,
      confirming: false
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadRelations();
      } else {
        this.resetData();
      }
    }
  },
  methods: {
    async loadRelations() {
      this.loading = true;
      try {
        let url = `/api_data_model_relations/${this.knowledgeBaseId}?page=${this.currentPage}&page_size=${this.pageSize}`;
        
        if (this.statusFilter) {
          url += `&status=${this.statusFilter}`;
        }
        
        const response = await this.$http.get(url);
        
        if (response.data.code === 200) {
          let relationsList = response.data.data.list;
          
          // 如果有任务ID筛选，进行前端过滤
          if (this.taskIdFilter) {
            relationsList = relationsList.filter(item => 
              item.task_id && item.task_id.toLowerCase().includes(this.taskIdFilter.toLowerCase())
            );
          }
          
          this.relations = relationsList;
          this.total = response.data.data.total;
        } else {
          this.$message.error(response.data.message || '加载关系列表失败');
        }
      } catch (error) {
        console.error('加载关系列表失败:', error);
        this.$message.error('加载关系列表失败');
      } finally {
        this.loading = false;
      }
    },
    
    handleFilterChange() {
      this.currentPage = 1;
      this.loadRelations();
    },
    
    handleSelectionChange(selection) {
      this.selectedRelations = selection;
    },
    
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.loadRelations();
    },
    
    handleCurrentChange(val) {
      this.currentPage = val;
      this.loadRelations();
    },
    
    getMethodType(method) {
      const typeMap = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'info'
      };
      return typeMap[method?.toUpperCase()] || 'info';
    },
    
    getStatusType(status) {
      const typeMap = {
        0: 'warning',  // 待确认
        1: 'success',  // 已确认
        2: 'info'      // 已忽略
      };
      return typeMap[status] || 'info';
    },
    
    getStatusText(status) {
      const textMap = {
        0: '待确认',
        1: '已确认',
        2: '已忽略'
      };
      return textMap[status] || '未知';
    },
    
    formatTime(timeStr) {
      if (!timeStr) return '-';
      return new Date(timeStr).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    
    async confirmRelations() {
      if (this.selectedRelations.length === 0) {
        this.$message.warning('请选择要确认的关系');
        return;
      }
      
      this.confirming = true;
      try {
        const relationIds = this.selectedRelations.map(item => item.id);
        
        const response = await this.$http.post(`/confirm_api_data_model_relations/${this.knowledgeBaseId}`, {
          relation_ids: relationIds
        });
        
        if (response.data.code === 200) {
          const { confirmed_count, failed_relations } = response.data.data;
          
          if (failed_relations && failed_relations.length > 0) {
            this.$message.warning(`成功确认 ${confirmed_count} 个关系，${failed_relations.length} 个关系确认失败`);
          } else {
            this.$message.success(`成功确认 ${confirmed_count} 个关系`);
          }
          
          // 重新加载列表
          this.loadRelations();
          
          // 清空选择
          this.$refs.relationTable.clearSelection();
          
          // 触发确认完成事件
          this.$emit('confirmed', response.data.data);
        } else {
          this.$message.error(response.data.message || '确认关系失败');
        }
      } catch (error) {
        console.error('确认关系失败:', error);
        this.$message.error('确认关系失败');
      } finally {
        this.confirming = false;
      }
    },
    
    resetData() {
      this.relations = [];
      this.selectedRelations = [];
      this.currentPage = 1;
      this.statusFilter = '';
      this.taskIdFilter = '';
    },
    
    handleClose() {
      this.dialogVisible = false;
    }
  }
};
</script>

<style scoped>
.relation-confirm-dialog {
  .dialog-content {
    padding: 0;
  }
  
  .filter-bar {
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
    margin-bottom: 16px;
  }
  
  .selected-info {
    color: #606266;
    font-size: 14px;
  }
  
  .text-right {
    text-align: right;
  }
  
  .text-muted {
    color: #909399;
  }
  
  .api-info {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .api-path {
      font-size: 12px;
      color: #606266;
      word-break: break-all;
    }
  }
  
  .dialog-footer {
    text-align: right;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
  }
}

/* 深度选择器，用于修改Element UI组件样式 */
::v-deep .el-table {
  .el-table__header th {
    background-color: #fafafa;
    color: #606266;
    font-weight: 600;
  }
  
  .el-table__row:hover > td {
    background-color: #f5f7fa;
  }
}

::v-deep .el-pagination {
  .el-pagination__total {
    color: #606266;
  }
}
</style>

<template>
  <div>
    <div v-for="item in items" :key="item.path" class="mb-1">
      <div v-if="item.children && item.children.length" class="flex flex-col">
        <div class="flex items-center cursor-pointer px-6 py-2 text-gray-700 font-medium">
          <i :class="[item.icon, 'mr-2']"></i>
          <span v-if="!isCollapsed">{{ item.name }}</span>
        </div>
        <div class="ml-6 border-l border-gray-100 pl-2">
          <MenuList :items="item.children" :is-collapsed="isCollapsed" />
        </div>
      </div>
      <router-link v-else :to="item.path" class="flex items-center px-6 py-2 text-gray-600 hover:bg-gray-50 rounded transition-all">
        <i :class="[item.icon, 'mr-2']"></i>
        <span v-if="!isCollapsed">{{ item.name }}</span>
      </router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{ items: any[], isCollapsed: boolean }>()
import MenuList from './MenuList.vue'
</script> 
package com.qfnu.common.enums;

import lombok.Getter;

/**
 * 审核状态枚举
 */
@Getter
public enum ReviewStatusEnum {
    
    PENDING(0, "待审核"),
    APPROVED(1, "审核通过"),
    REJECTED(2, "审核拒绝");

    private final Integer code;
    private final String desc;

    ReviewStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ReviewStatusEnum getByCode(Integer code) {
        for (ReviewStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
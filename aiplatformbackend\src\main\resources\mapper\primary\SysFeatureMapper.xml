<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qfnu.mapper.primary.SysFeatureMapper">

    <resultMap id="BaseResultMap" type="com.qfnu.model.po.SysFeaturePO">
                <id column="feature_id" property="featureId"/>
        <result column="description" property="description"/>
        <result column="creator" property="creator"/>
        <result column="modifier" property="modifier"/>
        <result column="dept_belong_id" property="deptBelongId"/>
        <result column="update_datetime" property="updateDatetime"/>
        <result column="create_datetime" property="createDatetime"/>
        <result column="module_name" property="moduleName"/>
        <result column="person_responsible" property="personResponsible"/>
        <result column="group_responsible" property="groupResponsible"/>
        <result column="level" property="level"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="parent_id" property="parentId"/>
        <result column="parent_name" property="parentName"/>
        <result column="product_id" property="productId"/>

    </resultMap>

    <sql id="Base_Column_List">
        feature_id, description, creator, modifier, dept_belong_id, update_datetime, create_datetime, module_name, person_responsible, group_responsible, `level`, is_deleted, parent_id, product_id
    </sql>


    <select id="getPageList" resultMap="BaseResultMap" parameterType="com.qfnu.model.param.SysFeatureParam">
        SELECT
        f.*,
        p.module_name AS parent_name
        FROM sys_feature f
        LEFT JOIN sys_feature p ON f.parent_id = p.feature_id
        <where>
            1 = 1
            <if test="param.productId != null">
                AND f.product_id = #{param.productId}
            </if>
            <if test="param.moduleName != null">
                AND f.module_name LIKE CONCAT('%', #{param.moduleName}, '%')
            </if>
            <if test="param.level != null">
                AND f.`level` = #{param.level}
            </if>
            <if test="param.personResponsible != null">
                AND f.person_responsible LIKE CONCAT('%', #{param.personResponsible}, '%')
            </if>
            <if test="param.groupResponsible != null">
                AND f.group_responsible LIKE CONCAT('%', #{param.groupResponsible}, '%')
            </if>
               AND f.is_deleted = 0
             LIMIT #{param.pageSize}  OFFSET #{param.offset}

        </where>
    </select>

    <select id="getPageListCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        from sys_feature
        <where>
            1 = 1
            <if test="param.productId != null">
                and product_id = #{param.productId}
            </if>
            <if test="param.moduleName != null">
                and module_name like concat('%', #{param.moduleName}, '%')
            </if>
            <if test="param.level != null">
                and `level`  = #{param.level}
            </if>
            <if test="param.personResponsible != null">
                and person_responsible like concat('%', #{param.personResponsible}, '%')
            </if>
            <if test="param.groupResponsible != null">
                and group_responsible like concat('%', #{param.groupResponsible}, '%')
            </if>
                and is_deleted = 0
        </where>
    </select>

    <select id="getFeaturesByProductId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_feature
        <where>
            1 = 1
        <if test="param.productId != null">
          AND product_id = #{param.productId}
        </if>
        </where>
        AND is_deleted = 0
    </select>

    <update id="myUpdate" parameterType="com.qfnu.model.param.SysFeatureParam">
        update sys_feature
        <set>
            <if test="param.modifier != null">
                modifier = #{param.modifier},
            </if>
            <if test="param.updateDatetime != null">
                update_datetime = #{param.updateDatetime},
            </if>
            <if test="param.moduleName != null">
                module_name = #{param.moduleName},
            </if>
            <if test="param.personResponsible != null">
                person_responsible = #{param.personResponsible},
            </if>
            <if test="param.groupResponsible != null">
                group_responsible = #{param.groupResponsible},
            </if>
            <if test="param.level != null">
                `level` = #{param.level},
            </if>
            <if test="param.isDeleted != null">
                is_deleted = #{param.isDeleted},
            </if>
            <if test="param.parentId != null">
                parent_id = #{param.parentId},
            </if>
            <if test="param.productId != null">
                product_id = #{param.productId}
            </if>
        </set>
        where feature_id = #{param.featureId}
    </update>


</mapper>
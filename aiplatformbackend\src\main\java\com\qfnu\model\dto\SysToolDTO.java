package com.qfnu.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@ApiModel(value = "SysToolDTO", description = "SysTool传输对象")
public class SysToolDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 工具ID（主键）
     */
    @ApiModelProperty("工具ID（主键）")
    private String toolId;

    /**
     * 工具名称
     */
    @ApiModelProperty("工具名称")
    private String toolName;

    /**
     * 链接地址
     */
    @ApiModelProperty("链接地址")
    private String linkAddress;

    /**
     * 功能介绍
     */
    @ApiModelProperty("功能介绍")
    private String functionDescription;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 类别
     */
    @ApiModelProperty("类别")
    private String category;

    /**
     * 标签
     */
    @ApiModelProperty("标签")
    private String tags;

    /**
     * 创建人（多人，JSON数组存储）
     */
    @ApiModelProperty("创建人（多人，JSON数组存储）")
    private String creators;

    /**
     * 关联用户ID（多个，JSON数组存储）
     */
    @ApiModelProperty("关联用户ID（多个，JSON数组存储）")
    private String userIds;

    /**
     * 评分（范围0.00-5.00）
     */
    @ApiModelProperty("评分（范围0.00-5.00）")
    private BigDecimal rating;

    /**
     * 使用次数
     */
    @ApiModelProperty("使用次数")
    private Long usageCount;

    /**
     * 审核状态（0=未通过, 1=通过, 2=待审核）
     */
    @ApiModelProperty("审核状态（0=未通过, 1=通过, 2=待审核）")
    private String reviewStatus;

    /**
     * 是否上架（1=上架, 0=下架）
     */
    @ApiModelProperty("是否上架（1=上架, 0=下架）")
    private String isPublished;

    /**
     * 是否删除（1=删除, 0=未删除）
     */
    @ApiModelProperty("是否删除（1=删除, 0=未删除）")
    private String isDeleted;

    /**
     * 删除时间
     */
    @ApiModelProperty("删除时间")
    private Date deleteTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 更新者
     */
    @ApiModelProperty("更新者")
    private String updateBy;


}
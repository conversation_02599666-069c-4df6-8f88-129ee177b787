package com.qfnu.mapper.primary;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qfnu.model.dto.SysReviewDTO;
import com.qfnu.model.po.SysReviewPO;
import com.qfnu.model.param.SysReviewParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysReviewMapper extends BaseMapper<SysReviewPO> {


    /**
     * 分页查询审核记录，包含工具信息
     * @param page 分页参数
     * @param param 查询条件
     * @return 分页结果
     */
    IPage<SysReviewDTO> getPageList(IPage<SysReviewDTO> page, @Param("param") SysReviewParam param);


    Integer countTotal(IPage<SysReviewDTO> page,@Param("param") SysReviewParam param);


}
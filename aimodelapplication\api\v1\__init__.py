from fastapi import APIRouter, Request, Query, Body, HTTPException
from typing import List
from .test import router as test
from .dify_mapping import router as dify_mapping
from .knowledge_base import router as knowledge_base
from .knowledge_api import router as knowledge_api
from .redoc import router as doc
from template.response_temp import BaseResponse
from logger import connection_logging
from db.mysql.models.prompt_management import PromptManagementModel
from .knowledge_api.relation_uid.schemas import PromptManagementRequest

# 创建提示词管理路由
prompt_router = APIRouter(prefix="/prompt_management")
logger = connection_logging()

@prompt_router.post("/save", summary="保存提示词配置")
async def save_prompt_config(
    request: PromptManagementRequest,
    http_request: Request,
    knowledge_base_id: str = Query(None, description="知识库ID")
):
    """保存提示词管理配置"""
    prompt_model = PromptManagementModel()
    try:
        # 尝试从多个来源获取知识库ID
        logger.info(f"原始查询参数 knowledge_base_id: {knowledge_base_id}")

        if not knowledge_base_id:
            # 从请求体获取（如果前端传递了）
            if hasattr(request, 'knowledgeBaseId') and request.knowledgeBaseId:
                knowledge_base_id = request.knowledgeBaseId
                logger.info(f"从请求体获取到 knowledge_base_id: {knowledge_base_id}")

        if not knowledge_base_id:
            # 从请求头获取
            knowledge_base_id = http_request.headers.get("X-Knowledge-Base-ID")
            if knowledge_base_id:
                logger.info(f"从请求头获取到 knowledge_base_id: {knowledge_base_id}")

        if not knowledge_base_id:
            # 从Referer头中提取
            referer = http_request.headers.get("referer", "")
            logger.info(f"Referer头: {referer}")

            # 尝试多种URL模式提取知识库ID
            if "/relation/" in referer:
                # 模式1: /relation/{知识库ID}
                knowledge_base_id = referer.split("/relation/")[-1].split("/")[0].split("?")[0]
                logger.info(f"从Referer /relation/ 提取到 knowledge_base_id: {knowledge_base_id}")
            elif "/knowledge_api/" in referer:
                # 模式2: /knowledge_api/{知识库ID}/
                parts = referer.split("/knowledge_api/")
                if len(parts) > 1:
                    knowledge_base_id = parts[1].split("/")[0].split("?")[0]
                    logger.info(f"从Referer /knowledge_api/ 提取到 knowledge_base_id: {knowledge_base_id}")
            else:
                # 尝试从URL路径中提取UUID格式的字符串
                import re
                uuid_pattern = r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
                matches = re.findall(uuid_pattern, referer, re.IGNORECASE)
                if matches:
                    knowledge_base_id = matches[-1]  # 取最后一个匹配的UUID
                    logger.info(f"从Referer UUID模式提取到 knowledge_base_id: {knowledge_base_id}")
                else:
                    logger.info("Referer中没有找到知识库ID")

        if not knowledge_base_id:
            knowledge_base_id = "default"
            logger.info("使用默认 knowledge_base_id: default")

        logger.info(f"开始保存提示词配置，knowledge_base_id: {knowledge_base_id}")
        await prompt_model.initialize()

        # 准备数据
        data = {
            'business_introduction': request.businessIntroduction,
            'term_definitions': [term.dict() for term in request.termDefinitions],
            'business_prompts': [prompt.dict() for prompt in request.businessPrompts],
            'extra_evidence': [evidence.dict() for evidence in request.extraEvidence]
        }

        logger.info(f"保存提示词配置: {data}")

        # 使用upsert方法，如果存在则更新，不存在则插入
        result = await prompt_model.upsert_by_knowledge_base_id(knowledge_base_id, data)
        logger.info(f"数据库upsert操作结果: {result}")

        if result > 0:
            logger.info(f"提示词配置保存成功，knowledge_base_id: {knowledge_base_id}")
            return BaseResponse(
                code=200,
                message="提示词配置保存成功",
                data={"saved": True, "knowledge_base_id": knowledge_base_id}
            )
        else:
            logger.error(f"数据库操作返回结果为0，保存失败，knowledge_base_id: {knowledge_base_id}")
            return BaseResponse(
                code=500,
                message="保存失败，数据库操作未成功",
                data={}
            )

    except Exception as e:
        logger.error(f"保存提示词配置失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return BaseResponse(
            code=500,
            message=f"保存失败: {str(e)}",
            data={}
        )
    finally:
        await prompt_model.close()

@prompt_router.get("/load", summary="加载提示词配置")
async def load_prompt_config(
    http_request: Request,
    knowledge_base_id: str = Query(None, description="知识库ID")
):
    """加载提示词管理配置"""
    prompt_model = PromptManagementModel()
    try:
        # 尝试从多个来源获取知识库ID
        logger.info(f"原始查询参数 knowledge_base_id: {knowledge_base_id}")

        if not knowledge_base_id:
            # 从请求头获取
            knowledge_base_id = http_request.headers.get("X-Knowledge-Base-ID")
            if knowledge_base_id:
                logger.info(f"从请求头获取到 knowledge_base_id: {knowledge_base_id}")

        if not knowledge_base_id:
            # 从Referer头中提取
            referer = http_request.headers.get("referer", "")
            logger.info(f"Referer头: {referer}")

            # 尝试多种URL模式提取知识库ID
            if "/relation/" in referer:
                # 模式1: /relation/{知识库ID}
                knowledge_base_id = referer.split("/relation/")[-1].split("/")[0].split("?")[0]
                logger.info(f"从Referer /relation/ 提取到 knowledge_base_id: {knowledge_base_id}")
            elif "/knowledge_api/" in referer:
                # 模式2: /knowledge_api/{知识库ID}/
                parts = referer.split("/knowledge_api/")
                if len(parts) > 1:
                    knowledge_base_id = parts[1].split("/")[0].split("?")[0]
                    logger.info(f"从Referer /knowledge_api/ 提取到 knowledge_base_id: {knowledge_base_id}")
            else:
                # 尝试从URL路径中提取UUID格式的字符串
                import re
                uuid_pattern = r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
                matches = re.findall(uuid_pattern, referer, re.IGNORECASE)
                if matches:
                    knowledge_base_id = matches[-1]  # 取最后一个匹配的UUID
                    logger.info(f"从Referer UUID模式提取到 knowledge_base_id: {knowledge_base_id}")
                else:
                    logger.info("Referer中没有找到知识库ID")

        if not knowledge_base_id:
            knowledge_base_id = "default"
            logger.info("使用默认 knowledge_base_id: default")

        logger.info(f"开始加载提示词配置，knowledge_base_id: {knowledge_base_id}")
        await prompt_model.initialize()

        # 从数据库加载配置
        config = await prompt_model.find_by_knowledge_base_id(knowledge_base_id)

        if config:
            # 转换数据格式以匹配前端期望的格式
            response_data = {
                "businessIntroduction": config.get('business_introduction', ''),
                "termDefinitions": config.get('term_definitions', []),
                "businessPrompts": config.get('business_prompts', []),
                "extraEvidence": config.get('extra_evidence', [])
            }

            logger.info(f"加载提示词配置成功: {knowledge_base_id}")
        else:
            # 如果没有找到配置，返回默认的空配置
            response_data = {
                "businessIntroduction": "",
                "termDefinitions": [
                    {"name": "", "definition": ""}
                ],
                "businessPrompts": [
                    {"content": ""}
                ],
                "extraEvidence": [
                    {"name": "", "description": ""}
                ]
            }
            logger.info(f"未找到提示词配置，返回默认配置: {knowledge_base_id}")

        return BaseResponse(
            code=200,
            message="加载提示词配置成功",
            data=response_data
        )
    except Exception as e:
        logger.error(f"加载提示词配置失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return BaseResponse(
            code=500,
            message=f"加载失败: {str(e)}",
            data={}
        )
    finally:
        await prompt_model.close()

router = APIRouter(prefix="/ai/v1")
router.include_router(test)
router.include_router(dify_mapping)
router.include_router(knowledge_base)
router.include_router(knowledge_api)
router.include_router(doc)
router.include_router(prompt_router)
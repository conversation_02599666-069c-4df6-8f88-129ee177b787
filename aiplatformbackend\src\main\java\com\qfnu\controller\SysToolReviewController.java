package com.qfnu.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qfnu.common.ResponseVO;
import com.qfnu.common.annotation.RequiredPermission;
import com.qfnu.model.param.SysReviewParam;
import com.qfnu.model.vo.SysReviewVO;
import com.qfnu.service.SysToolReviewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Api(tags = "工具审核接口")
@RestController
@RequestMapping("/api/tool/review")
public class SysToolReviewController {

    @Resource
    private SysToolReviewService sysToolReviewService;

    @RequiredPermission({"super,admin"})
    @ApiOperation("审核工具")
    @PostMapping("/review")
    public ResponseVO<Boolean> reviewTool(@RequestBody SysReviewParam param, HttpServletRequest request) {
        param.setReviewer(request.getAttribute("id").toString());
        return ResponseVO.success(sysToolReviewService.reviewTool(param),200);
    }

    @ApiOperation("分页查询审核记录")
    @PostMapping("/page")
    public ResponseVO<IPage<SysReviewVO>> getReviewPage(@RequestBody SysReviewParam param) {

        return ResponseVO.success(sysToolReviewService.getReviewPage(param),200);
    }

    @ApiOperation("获取审核详情")
    @GetMapping("/detail/{reviewId}")
    public ResponseVO<SysReviewVO> getReviewDetail(@PathVariable String reviewId) {
        return ResponseVO.success(sysToolReviewService.getReviewDetail(reviewId),200);
    }
}
/**
 * 从assets/avatar文件夹中随机获取一张头像图片的路径
 * @returns {string} 随机头像的路径
 */
export function getRandomAvatar(): string {
  // 默认头像列表
  const defaultAvatars = [
    '/avatar/head1.png',
    '/avatar/head2.png',
    '/avatar/head3.png',
    '/avatar/head4.png',
    '/avatar/head5.png',
    '/avatar/head6.png',
    '/avatar/head7.png',
    '/avatar/head8.png',
    '/avatar/head9.png',
  ];

  // 随机选择一个头像
  const randomIndex = Math.floor(Math.random() * defaultAvatars.length);
  return defaultAvatars[randomIndex];
}

/**
 * 从assets/avatar文件夹中随机获取一张头像图片的路径
 * @returns {string} 随机头像的路径
 */
export function getRandomTitle(): string {
  // 默认头像列表
  const defaultAvatars = [
    '/icon/1.jpg',
    '/icon/2.jpg',
    '/icon/3.png',
    '/icon/4.png',
    '/icon/5.png',
    '/icon/6.png',
    '/icon/7.png',
    '/icon/8.png',
    '/icon/9.png',
    '/icon/10.png',
  ];

  // 随机选择一个头像
  const randomIndex = Math.floor(Math.random() * defaultAvatars.length);
  return defaultAvatars[randomIndex];
}
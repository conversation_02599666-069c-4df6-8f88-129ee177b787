import { ref, computed } from 'vue';
import * as echarts from 'echarts';

// 图谱配置管理
export const useGraphConfig = () => {
  // 图谱基础配置
  const getBaseConfig = () => ({
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        if (params.dataType === 'node') {
          return `实体: ${params.data.name}<br/>类型: ${params.data.category}`;
        } else {
          return `关系: ${params.data.type}<br/>描述: ${params.data.description}`;
        }
      }
    },
    legend: {
      data: []
    },
    animationDuration: 1500,
    animationEasingUpdate: 'quinticInOut'
  });

  // 力导向布局配置
  const getForceConfig = () => ({
    repulsion: 100,
    gravity: 0.1,
    friction: 0.6,
    layoutAnimation: true,
    edgeLength: 150
  });

  // 节点样式配置
  const getNodeStyleConfig = () => ({
    label: {
      show: true,
      position: 'right',
      formatter: '{b}',
      fontSize: 12,
      color: '#333'
    },
    itemStyle: {
      opacity: 0.9,
      borderWidth: 2,
      borderColor: '#fff'
    },
    symbolSize: 40 // 稍微减小节点大小，让布局更紧凑
  });

  // 边样式配置
  const getEdgeStyleConfig = () => ({
    lineStyle: {
      color: 'source',
      curveness: 0.2,
      opacity: 0.6,
      width: 2
    }
  });

  return {
    getBaseConfig,
    getForceConfig,
    getNodeStyleConfig,
    getEdgeStyleConfig
  };
};

// 颜色管理
export const useColorManager = () => {
  const colorList = [
    '#1890ff', '#52c41a', '#722ed1', '#faad14', '#13c2c2',
    '#eb2f96', '#fa8c16', '#a0d911', '#2f54eb'
  ];

  const relationColors = {
    'HAS_PARAM': '#722ed1',
    '其他': '#bfbfbf'
  };

  const getCategoryColor = (type, categories) => {
    const categoryIndex = categories.findIndex(
      cat => cat.name.toLowerCase() === type.toLowerCase()
    );
    
    if (categoryIndex !== -1) {
      return colorList[categoryIndex % colorList.length];
    }
    
    const hash = type.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);
    return colorList[Math.abs(hash) % colorList.length];
  };

  const getRelationColor = (name) => {
    return relationColors[name] || '#bfbfbf';
  };

  return {
    getCategoryColor,
    getRelationColor
  };
};

// 数据转换管理
export const useDataTransformer = () => {
  const { getCategoryColor, getRelationColor } = useColorManager();

  const transformNode = (node, categories) => ({
    id: node.id,
    name: node.name,
    category: node.type,
    description: node.fields?.description || '',
    symbolSize: 40,
    itemStyle: {
      color: getCategoryColor(node.type, categories)
    }
  });

  const transformLink = (link) => ({
    id: link.id,
    source: link.source,
    target: link.target,
    type: link.type,
    description: link.description || '',
    lineStyle: {
      color: getRelationColor(link.type)
    }
  });

  const transformCategory = (category) => ({
    name: category.name
  });

  const transformGraphData = (mapInfo) => {
    if (!mapInfo?.data) return null;
    
    const { nodes, links, categories, version } = mapInfo.data;
    
    if (!nodes || !links || !categories) return null;

    return {
      nodes: nodes.map(node => transformNode(node, categories)),
      links: links.map(transformLink),
      categories: categories.map(transformCategory),
      version
    };
  };

  return {
    transformNode,
    transformLink,
    transformCategory,
    transformGraphData
  };
};

// 事件管理
export const useEventManager = (chart, hoverTimer, highlightedDataIndex, highlightedDataType) => {
  const handleMouseOver = (params) => {
    if (params.componentType !== 'series' || 
        (params.dataType !== 'node' && params.dataType !== 'edge')) {
      return;
    }

    if (hoverTimer.value) {
      clearTimeout(hoverTimer.value);
      hoverTimer.value = null;
    }

    if (highlightedDataIndex.value !== null && 
        (highlightedDataIndex.value !== params.dataIndex || 
         highlightedDataType.value !== params.dataType)) {
      if (chart.value) {
        chart.value.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataType: highlightedDataType.value,
          dataIndex: highlightedDataIndex.value
        });
      }
      highlightedDataIndex.value = null;
      highlightedDataType.value = null;
    }

    hoverTimer.value = setTimeout(() => {
      if (chart.value) {
        chart.value.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataType: params.dataType,
          dataIndex: params.dataIndex
        });
      }
      highlightedDataIndex.value = params.dataIndex;
      highlightedDataType.value = params.dataType;
      hoverTimer.value = null;
    }, 300);
  };

  const handleMouseOut = (params) => {
    if (params.componentType !== 'series' || 
        (params.dataType !== 'node' && params.dataType !== 'edge')) {
      return;
    }

    if (hoverTimer.value) {
      clearTimeout(hoverTimer.value);
      hoverTimer.value = null;
    }
    
    if (highlightedDataIndex.value !== null && 
        highlightedDataIndex.value === params.dataIndex && 
        highlightedDataType.value === params.dataType) {
      if (chart.value) {
        chart.value.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataType: params.dataType,
          dataIndex: params.dataIndex
        });
      }
      highlightedDataIndex.value = null;
      highlightedDataType.value = null;
    }
  };

  return {
    handleMouseOver,
    handleMouseOut
  };
};

// 透明度管理
export const useOpacityManager = (chart) => {
  const updateNodeOpacity = (selectedNodeId) => {
    if (!chart.value) return;
    
    const option = chart.value.getOption();
    if (!option || !option.series || !option.series[0]) return;
    
    const nodes = option.series[0].data;
    const links = option.series[0].links;
    
    const relatedNodeIds = new Set([selectedNodeId]);
    
    links.forEach(link => {
      if (link.source === selectedNodeId) {
        relatedNodeIds.add(link.target);
      } else if (link.target === selectedNodeId) {
        relatedNodeIds.add(link.source);
      }
    });
    
    const updatedNodes = nodes.map(node => ({
      ...node,
      itemStyle: {
        ...node.itemStyle,
        opacity: relatedNodeIds.has(node.id) ? 1 : 0.3
      }
    }));
    
    const updatedLinks = links.map(link => ({
      ...link,
      lineStyle: {
        ...link.lineStyle,
        opacity: (relatedNodeIds.has(link.source) && relatedNodeIds.has(link.target)) ? 1 : 0.3
      }
    }));
    
    chart.value.setOption({
      series: [{
        data: updatedNodes,
        links: updatedLinks
      }]
    });
  };

  const resetNodeOpacity = () => {
    if (!chart.value) return;
    
    const option = chart.value.getOption();
    if (!option || !option.series || !option.series[0]) return;
    
    const nodes = option.series[0].data;
    const links = option.series[0].links;
    
    const updatedNodes = nodes.map(node => ({
      ...node,
      itemStyle: {
        ...node.itemStyle,
        opacity: 1
      }
    }));
    
    const updatedLinks = links.map(link => ({
      ...link,
      lineStyle: {
        ...link.lineStyle,
        opacity: 1
      }
    }));
    
    chart.value.setOption({
      series: [{
        data: updatedNodes,
        links: updatedLinks
      }]
    });
  };

  return {
    updateNodeOpacity,
    resetNodeOpacity
  };
};

// 图谱管理器
export const useGraphManager = () => {
  const chart = ref(null);
  const graphRef = ref(null);
  const hoverTimer = ref(null);
  const highlightedDataIndex = ref(null);
  const highlightedDataType = ref(null);

  const { getBaseConfig, getForceConfig, getNodeStyleConfig, getEdgeStyleConfig } = useGraphConfig();
  const { transformGraphData } = useDataTransformer();

  // 初始化图谱
  const initGraph = (graphData, categories, onNodeClick, onEdgeClick) => {
    if (!graphRef.value) return;
    
    if (chart.value) {
      chart.value.dispose();
    }
    
    chart.value = echarts.init(graphRef.value);
    
    const { handleMouseOver, handleMouseOut } = useEventManager(
      chart, hoverTimer, highlightedDataIndex, highlightedDataType
    );
    
    // 为节点设置初始位置，避免挤在一起
    const nodesWithPosition = graphData.nodes.map((node, index) => {
      const angle = (index / graphData.nodes.length) * 2 * Math.PI;
      const radius = 200; // 初始分布半径
      return {
        ...node,
        x: Math.cos(angle) * radius,
        y: Math.sin(angle) * radius,
        fixed: false // 初始不固定，让力导向布局发挥作用
      };
    });
    
    const option = {
      ...getBaseConfig(),
      series: [{
        type: 'graph',
        layout: 'force',
        data: nodesWithPosition,
        links: graphData.links,
        categories: graphData.categories,
        roam: true,
        draggable: true,
        ...getNodeStyleConfig(),
        force: getForceConfig(),
        ...getEdgeStyleConfig(),
        emphasis: {
          disabled: true
        }
      }]
    };
    
    chart.value.setOption(option);
    
    // 绑定事件
    chart.value.on('mouseover', handleMouseOver);
    chart.value.on('mouseout', handleMouseOut);
    chart.value.on('click', (params) => {
      if (params.dataType === 'node') {
        onNodeClick(params.data);
      } else if (params.dataType === 'edge') {
        onEdgeClick(params.data);
      }
    });

    chart.value.on('dragend', (params) => {
      if (params.dataType === 'node') {
        // 获取当前option
        const option = chart.value.getOption();
        if (option && option.series && option.series[0] && option.series[0].data) {
          // 找到被拖动的节点
          const node = option.series[0].data.find(n => n.id === params.data.id);
          if (node) {
            node.fixed = true; // 拖动后固定
            // 更新节点坐标
            node.x = params.event.offsetX;
            node.y = params.event.offsetY;
            chart.value.setOption({ series: [{ data: option.series[0].data }] });
          }
        }
      }
    });
  };

  // 更新图谱数据
  const updateGraphData = (mapInfo, onNodeClick) => {
    const transformedData = transformGraphData(mapInfo);
    
    if (!transformedData) {
      console.warn('数据转换失败');
      return null;
    }

    if (chart.value) {
      // 为更新的节点设置更好的初始位置
      const nodesWithPosition = transformedData.nodes.map((node, index) => {
        const angle = (index / transformedData.nodes.length) * 2 * Math.PI;
        const radius = 250; // 稍微增加分布半径
        return {
          ...node,
          x: Math.cos(angle) * radius,
          y: Math.sin(angle) * radius,
          fixed: false
        };
      });

      chart.value.setOption({
        series: [{
          data: nodesWithPosition,
          links: transformedData.links,
          categories: transformedData.categories
        }]
      });

      // 延长布局动画时间，让节点有更多时间找到合适位置
      setTimeout(() => {
        if (chart.value) {
          const currentOption = chart.value.getOption();
          if (currentOption && currentOption.series && currentOption.series[0]) {
            currentOption.series[0].data.forEach(node => {
              node.fixed = true;
            });
            chart.value.setOption({
              series: currentOption.series
            });
          }
        }
      }, 3000); // 增加到3秒，给更多时间让布局稳定
    }

    return transformedData;
  };

  // 处理窗口大小变化
  const handleResize = () => {
    if (chart.value) {
      chart.value.resize();
    }
  };

  // 销毁图表
  const disposeChart = () => {
    if (chart.value) {
      chart.value.dispose();
      chart.value = null;
    }
  };

  return {
    chart,
    graphRef,
    hoverTimer,
    highlightedDataIndex,
    highlightedDataType,
    initGraph,
    updateGraphData,
    handleResize,
    disposeChart
  };
}; 
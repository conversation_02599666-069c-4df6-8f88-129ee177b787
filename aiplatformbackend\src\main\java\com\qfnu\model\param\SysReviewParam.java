package com.qfnu.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.qfnu.model.param.BasePage;



@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "SysReviewParam", description = "SysReview参数对象")
public class SysReviewParam extends BasePage implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 审核ID（主键）
     */
    @ApiModelProperty("审核ID（主键）")
    private String reviewId;

    /**
     * 关联工具ID
     */
    @ApiModelProperty("关联工具ID")
    private String relatedId;

    /**
     * 提交时间
     */
    @ApiModelProperty("提交时间")
    private Date submissionTime;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    private Date approvalTime;

    /**
     * 审批状态（0=未通过, 1=通过, 2=待审核）
     */
    @ApiModelProperty("审批状态（0=未通过, 1=通过, 2=待审核）")
    private Integer approvalStatus;

    /**
     * 审批状态列表，用于多状态查询
     */
    @ApiModelProperty("审批状态列表，用于多状态查询")
    private List<Integer> approvalStatusList;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    private String reviewer;


}
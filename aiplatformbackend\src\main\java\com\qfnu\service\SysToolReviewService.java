package com.qfnu.service;

import com.qfnu.model.param.SysReviewParam;
import com.qfnu.model.vo.SysReviewVO;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 工具审核服务接口
 */
public interface SysToolReviewService {

    /**
     * 提交工具审核
     * @param param 审核参数
     * @return 审核记录ID
     */
    String submitToolReview(SysReviewParam param);

    /**
     * 审核工具
     * @param param 审核参数
     * @return 是否成功
     */
    boolean reviewTool(SysReviewParam param);

    /**
     * 分页查询审核记录
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<SysReviewVO> getReviewPage(SysReviewParam param);

    /**
     * 获取审核详情
     * @param reviewId 审核ID
     * @return 审核详情
     */
    SysReviewVO getReviewDetail(String reviewId);
}
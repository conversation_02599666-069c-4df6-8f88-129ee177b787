package com.qfnu.converter;

import com.qfnu.model.po.SysToolPO;
import com.qfnu.model.dto.SysToolDTO;
import com.qfnu.model.vo.SysToolVO;
import com.qfnu.model.param.SysToolParam;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;


import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SysToolConverter {


    @Mappings({
        @Mapping(target = "toolId", source = "toolId"),
        @Mapping(target = "toolName", source = "toolName"),
        @Mapping(target = "linkAddress", source = "linkAddress"),
        @Mapping(target = "functionDescription", source = "functionDescription"),
        @Mapping(target = "category", source = "category"),
        @Mapping(target = "isPublished", source = "isPublished"),
        @Mapping(target = "reviewStatus", source = "reviewStatus"),
        @Mapping(target = "createTime", source = "createTime"),
        @Mapping(target = "updateTime", source = "updateTime"),
        @Mapping(target = "createBy", source = "createBy"),
        @Mapping(target = "updateBy", source = "updateBy")
    })
    SysToolPO toSysToolPO(SysToolParam param);


    List<SysToolPO> toSysToolPOList(List<SysToolParam> paramList);



    @Mappings({
        @Mapping(target = "toolId", source = "toolId"),
        @Mapping(target = "toolName", source = "toolName"),
        @Mapping(target = "linkAddress", source = "linkAddress"),
        @Mapping(target = "functionDescription", source = "functionDescription"),
        @Mapping(target = "category", source = "category"),
        @Mapping(target = "isPublished", source = "isPublished"),
        @Mapping(target = "reviewStatus", source = "reviewStatus"),
        @Mapping(target = "createTime", source = "createTime"),
        @Mapping(target = "updateTime", source = "updateTime"),
        @Mapping(target = "createBy", source = "createBy"),
        @Mapping(target = "updateBy", source = "updateBy")
    })
    SysToolVO toSysToolVO(SysToolPO po);


    List<SysToolVO> toSysToolVOList(List<SysToolPO> poList);
    
    @Mappings({
        @Mapping(target = "toolId", source = "toolId"),
        @Mapping(target = "toolName", source = "toolName"),
        @Mapping(target = "linkAddress", source = "linkAddress"),
        @Mapping(target = "functionDescription", source = "functionDescription"),
        @Mapping(target = "category", source = "category"),
        @Mapping(target = "isPublished", source = "isPublished"),
        @Mapping(target = "reviewStatus", source = "reviewStatus"),
        @Mapping(target = "createTime", source = "createTime"),
        @Mapping(target = "updateTime", source = "updateTime"),
        @Mapping(target = "createBy", source = "createBy"),
        @Mapping(target = "updateBy", source = "updateBy")
    })
    SysToolDTO toSysToolDTO(SysToolPO po);
    
    List<SysToolDTO> toSysToolDTOList(List<SysToolPO> poList);
    
    @Mappings({
        @Mapping(target = "toolId", source = "toolId"),
        @Mapping(target = "toolName", source = "toolName"),
        @Mapping(target = "linkAddress", source = "linkAddress"),
        @Mapping(target = "functionDescription", source = "functionDescription"),
        @Mapping(target = "category", source = "category"),
        @Mapping(target = "isPublished", source = "isPublished"),
        @Mapping(target = "reviewStatus", source = "reviewStatus"),
        @Mapping(target = "createTime", source = "createTime"),
        @Mapping(target = "updateTime", source = "updateTime"),
        @Mapping(target = "createBy", source = "createBy"),
        @Mapping(target = "updateBy", source = "updateBy")
    })
    SysToolVO toSysToolVOFromDTO(SysToolDTO dto);
    
    List<SysToolVO> toSysToolVOListFromDTO(List<SysToolDTO> dtoList);
    
    @Mappings({
        @Mapping(target = "toolId", source = "toolId"),
        @Mapping(target = "toolName", source = "toolName"),
        @Mapping(target = "linkAddress", source = "linkAddress"),
        @Mapping(target = "functionDescription", source = "functionDescription"),
        @Mapping(target = "category", source = "category"),
        @Mapping(target = "isPublished", source = "isPublished"),
        @Mapping(target = "reviewStatus", source = "reviewStatus"),
        @Mapping(target = "createTime", source = "createTime"),
        @Mapping(target = "updateTime", source = "updateTime"),
        @Mapping(target = "createBy", source = "createBy"),
        @Mapping(target = "updateBy", source = "updateBy")
    })
    SysToolPO toSysToolPOFromDTO(SysToolDTO dto);
    
    List<SysToolPO> toSysToolPOListFromDTO(List<SysToolDTO> dtoList);
}
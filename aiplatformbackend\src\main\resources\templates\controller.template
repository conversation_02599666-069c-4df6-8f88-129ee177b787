package ${package}.controller;

import ${package}.service.${className}Service;
import ${package}.model.vo.${className}VO;
import ${package}.model.dto.${className}DTO;
import ${package}.model.param.${className}Param;
import ${package}.util.common.ResponseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

@Api(tags = "${className}接口")
@RestController
@RequestMapping("/${className}")
public class ${className}Controller {
    
    @Resource
    private ${className}Service ${serviceVarName};
    
    @ApiOperation("新增")
    @PostMapping("/save")
    public ResponseVO<Integer> save(@RequestBody ${className}Param param) {
        return ResponseVO.success(${serviceVarName}.save(param));
    }
    
    @ApiOperation("批量新增")
    @PostMapping("/saveBatch")
    public ResponseVO<Integer> saveBatch(@RequestBody List<${className}Param> paramList) {
        return ResponseVO.success(${serviceVarName}.saveBatch(paramList));
    }
    
    @ApiOperation("更新")
    @PostMapping("/update")
    public ResponseVO<Integer> update(@RequestBody ${className}Param param) {
        return ResponseVO.success(${serviceVarName}.update(param));
    }
    
    @ApiOperation("删除")
    @PostMapping("/delete")
    public ResponseVO<Integer> delete(@RequestBody ${className}Param param) {
        return ResponseVO.success(${serviceVarName}.delete(param.getId()));
    }
    
    @ApiOperation("根据ID查询")
    @PostMapping("/getById")
    public ResponseVO<${className}VO> getById(@RequestBody ${className}Param param) {
        return ResponseVO.success(${serviceVarName}.getById(param.getId()));
    }
    
    @ApiOperation("列表查询")
    @PostMapping("/list")
    public ResponseVO<List<${className}VO>> getList(@RequestBody ${className}Param param) {
        return ResponseVO.success(${serviceVarName}.getList(param));
    }
    
    @ApiOperation("批量新增或更新")
    @PostMapping("/batchAddOrUpdate")
    public ResponseVO<List<${className}VO>> batchAddOrUpdate(@RequestBody List<${className}Param> paramList) {
        return ResponseVO.success(${serviceVarName}.batchAddOrUpdate(paramList));
    }
} 
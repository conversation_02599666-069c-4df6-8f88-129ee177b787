<template>
  <div class="min-h-screen bg-gray-50 p-4">
    <router-view v-if="$route.name === 'userManagement' || $route.name === 'featureManagement'" />
    <div v-else>
      <div class="mb-6 flex justify-start items-center">
        <a-input-search
          v-model:value="searchQuery"
          placeholder="搜索设置项"
          class="w-64"
        />
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div v-for="card in filteredCards" :key="card.title" class="bg-white rounded-lg shadow-sm p-6 flex flex-col h-full">
          <div class="flex items-center mb-4">
            <div :class="[`p-3 rounded-lg ${card.iconBgColor}`]">
              <svg class="w-6 h-6" :class="card.iconColor" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="card.icon" />
              </svg>
            </div>
            <h3 class="text-lg font-semibold ml-4">{{ card.title }}</h3>
          </div>
          <p class="text-gray-600 mb-4">{{ card.description }}</p>
          <div class="flex space-x-2 mt-auto">
            <template v-for="(button, index) in card.buttons" :key="index">
              <a-button :type="button.type" class="flex items-center" :ghost="button.ghost" @click="button.onClick && button.onClick()">
                <template #icon>
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="button.icon" />
                  </svg>
                </template>
                {{ button.text }}
              </a-button>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


  
  <script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { useRouter } from 'vue-router';
  

  
  const router = useRouter();
  const searchQuery = ref('');
  
  const settingsCards = [
    {
      title: '用户管理',
      description: '管理系统用户，包括添加、编辑、删除用户，以及设置用户权限等操作。',
      icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z',
      iconBgColor: 'bg-blue-100',
      iconColor: 'text-blue-500',
      buttons: [
        {
          text: '查看详情',
          type: 'primary',
          ghost: true,
          icon: 'M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z',
          onClick: () => router.push('/system/basicSettings/user-management')
        }
      ]
    },
    {
      title: '特性管理',
      description: '管理系统特性，包括添加、编辑、删除特性，以及设置特性相关的配置等操作。',
      icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2',
      iconBgColor: 'bg-green-100',
      iconColor: 'text-green-500',
      buttons: [
        {
          text: '查看详情',
          type: 'primary',
          ghost: true,
          icon: 'M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z',
          onClick: () => router.push('/system/basicSettings/feature-management')
        }
      ]
    }
  ];
  
  const filteredCards = computed(() => {
    if (!searchQuery.value) return settingsCards;
    const query = searchQuery.value.toLowerCase();
    return settingsCards.filter(card =>
      card.title.toLowerCase().includes(query) ||
      card.description.toLowerCase().includes(query)
    );
  });
  </script>
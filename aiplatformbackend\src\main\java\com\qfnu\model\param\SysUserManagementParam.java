package com.qfnu.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@ApiModel(value = "SysUserManagementParam", description = "SysUserManagement参数对象")
public class SysUserManagementParam  implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 管理记录唯一标识，自增主键
     */
    @ApiModelProperty("管理记录唯一标识，自增主键")
    private String managementId;

    /**
     * 关联的用户 ID，与 sys_users 表中的 user_id 关联
     */
    @ApiModelProperty("关联的用户 ID，与 sys_users 表中的 user_id 关联")
    private String userId;

    /**
     * 关联的用户名，与 sys_users 表中的 username 关联
     */
    @ApiModelProperty("关联的用户名，与 sys_users 表中的 username 关联")
    private String username;

    /**
     * 用户账户名
     */
    @ApiModelProperty("用户账户名")
    private String accountName;

    /**
     * 用户真实姓名
     */
    @ApiModelProperty("用户真实姓名")
    private String realName;

    /**
     * 用户类型
     */
    @ApiModelProperty("用户类型")
    private String userType;

    /**
     * 权限，super:超级管理员 admin 管理员 common 普通用户
     */
    @ApiModelProperty("权限，super:超级管理员 admin 管理员 common 普通用户")
    private String role;

    /**
     * 用户关联的产品信息
     */
    @ApiModelProperty("用户关联的产品信息")
    private String product;


} 
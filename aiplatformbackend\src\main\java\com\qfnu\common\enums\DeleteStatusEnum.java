package com.qfnu.common.enums;

import lombok.Getter;

/**
 * 删除状态枚举
 */
@Getter
public enum DeleteStatusEnum {
    
    NOT_DELETED(0, "未删除"),
    DELETED(1, "已删除");

    private final Integer code;
    private final String desc;

    DeleteStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DeleteStatusEnum getByCode(Integer code) {
        for (DeleteStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
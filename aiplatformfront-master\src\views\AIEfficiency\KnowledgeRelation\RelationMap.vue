<template>
  <div class="relation-map-container">
    <!-- 加载状态提示 -->
    <div v-if="isLoading" class="loading-overlay">
      <a-spin size="large" />
      <div class="loading-text">正在加载图谱数据...</div>
          </div>
    
    <!-- 遮罩层 -->
    <div 
      v-if="selectedItem" 
      class="mask" 
      @click="handleMaskClick"
    ></div>
    
    <!-- 左侧操作面板 -->
    <div class="left-panel">
      <!-- 实体关系预览卡片 -->
      <div class="panel-card">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ stats?.entityCount || 0 }}</div>
            <div class="stat-label">实体数量</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats?.relationCount || 0 }}</div>
            <div class="stat-label">关系数量</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats?.categoryCount || 0 }}</div>
            <div class="stat-label">实体类型</div>
          </div>
        </div>
      </div>

      <!-- 操作卡片 -->
      <div class="panel-card">
        <div class="search-section">
              <a-input-search
                v-model:value="searchText"
            placeholder="搜索实体或关系"
                @search="handleSearch"
              />
            </div>
        <div class="action-section">
          <a-button type="primary" @click="showAddEntityModal">
            新增实体
          </a-button>
          <a-button type="primary" @click="showAddRelationModal">
            新增关系
          </a-button>
          </div>
        </div>

      <!-- 详情卡片 -->
      <div v-if="selectedItem" class="panel-card detail-card">
        <div class="detail-list">
          <template v-if="selectedItem.type === 'node'">
            <div class="detail-item">
              <div class="detail-label">NAME</div>
              <div class="detail-value">{{ selectedItem.name }}</div>
          </div>
            <div class="detail-item">
              <div class="detail-label">TYPE</div>
              <div class="detail-value">{{ selectedItem.category || selectedItem.relationType }}</div>
                  </div>
            <template v-if="selectedItem.fields">
              <div v-for="(value, key) in selectedItem.fields" :key="key" class="detail-item">
                <div class="detail-label">{{ key.toUpperCase() }}</div>
                <div class="detail-value">{{ value }}</div>
                      </div>
            </template>
          </template>
          <template v-else-if="selectedItem.type === 'edge'">
            <div class="detail-item">
              <div class="detail-label">TYPE</div>
              <div class="detail-value">{{ selectedItem.fields.relationType }}</div>
                    </div>
            <div class="detail-item">
              <div class="detail-label">DESCRIPTION</div>
              <div class="detail-value">{{ selectedItem.description || '暂无描述' }}</div>
                </div>
              </template>
            </div>
          </div>
        </div>

    <!-- 中间图谱区域 -->
    <div class="graph-container">
      <div ref="graphRef" class="graph-content"></div>
      </div>

    <!-- 右侧编辑卡片 -->
    <div class="right-panel" :class="{ 'show': selectedItem }" v-if="selectedItem">
      <div class="panel-header">
        <h3>{{ selectedItem.type === 'node' ? '实体编辑' : '关系编辑' }}</h3>
        <a-button type="text" class="close-btn" @click="selectedItem = null">
          <CloseOutlined />
            </a-button>
      </div>
      
      <div class="panel-content">
        <!-- 实体详情 -->
        <template v-if="selectedItem.type === 'node'">
          <div class="entity-preview">
            <div class="entity-relations">
              <div class="relation-list">
                <!-- 上游关系 -->
                <div class="relation-group">
                  <div v-for="relation in getUpstreamRelations(selectedItem.id)" :key="relation.id" class="relation-item">
                    <div class="related-entity">
                      <a-tag color="blue" class="entity-tag">

                        <span class="entity-name">{{ getEntityName(relation.source) }}</span>
                      </a-tag>
                    </div>
                    <div class="relation-arrow upstream">
                      <ArrowLeftOutlined />
                    </div>
                    <div class="relation-type upstream">{{ relation.type }}</div>
        </div>
      </div>

                <!-- 下游关系 -->
                <div class="relation-group">
                  <div v-for="relation in getDownstreamRelations(selectedItem.id)" :key="relation.id" class="relation-item">
                    <div class="relation-type downstream">{{ relation.type }}</div>
                    <div class="relation-arrow downstream">
                      <ArrowRightOutlined />
        </div>
                    <div class="related-entity">
                      <a-tag color="blue" class="entity-tag">
  
                        <span class="entity-name">{{ getEntityName(relation.target) }}</span>
                  </a-tag>
                </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="delete-section">
            <a-alert
                description="将删除当前实体和实体相关联的关系,请谨慎操作!"
                  type="warning"
                  show-icon
                banner
                style="margin-bottom: 16px"
              />
              <a-button type="primary" danger block @click="handleDelete">
                删除实体
              </a-button>
            </div>
            </div>
          </template>
        
        <!-- 关系详情 -->
        <template v-else-if="selectedItem.type === 'edge'">
          <div class="relation-preview">
            <div class="relation-container">
              <div class="entity-box source-entity">
                <a-tag color="blue" class="entity-tag">
                  <span class="entity-name">{{ getEntityName(selectedItem.source) }}</span>
                </a-tag>
              </div>
              <div class="relation-arrow">
                <ArrowRightOutlined />
            </div>
              <div class="entity-box target-entity">
                <a-tag color="blue" class="entity-tag">
                  <span class="entity-name">{{ getEntityName(selectedItem.target) }}</span>
                </a-tag>
              </div>
            </div>
            <div class="relation-edit">
              <div class="section-title">关系类型</div>
              <a-input
                v-model:value="selectedItem.fields.relationType"
                placeholder="输入关系类型"
                class="relation-input"
              />
            </div>
            <div class="relation-description">
              <div class="section-title">描述</div>
                <a-textarea
                  v-model:value="selectedItem.description"
                placeholder="在此处编辑关系描述"
                  :rows="3"
                class="description-input"
                />
              </div>
            </div>
          <div class="delete-section">
            <a-alert
              description="删除将无法恢复,请谨慎操作!"
              type="warning"
              show-icon
              banner
              style="margin-bottom: 16px"
            />
            <a-button type="primary" danger block @click="handleDelete">
              删除关系
            </a-button>
            </div>
          </template>
        
        <!-- 操作按钮 -->
          <div class="form-actions">
          <a-button type="primary" @click="handleSave">保存</a-button>
          <a-button @click="handleCancel">取消</a-button>
          </div>
        </div>
      </div>
    
    <!-- 新增实体弹窗 -->
    <a-modal
      v-model:visible="addEntityModalVisible"
      title="新增实体"
      @ok="handleAddEntity"
      @cancel="addEntityModalVisible = false"
      :width="600"
    >
      <a-form :model="newEntity" layout="vertical">
        <div class="form-row">
          <a-form-item label="实体名称" required class="form-item-name">
            <a-input v-model:value="newEntity.name" placeholder="请输入实体名称" />
          </a-form-item>
          <a-form-item label="实体类型" required class="form-item-type">
            <a-input v-model:value="newEntity.category" placeholder="请输入实体类型" />
          </a-form-item>
    </div>
        <a-form-item label="实体描述">
          <a-textarea v-model:value="newEntity.description" :rows="3" placeholder="请输入实体描述" />
        </a-form-item>
        
        <!-- 自定义字段 -->
        <div class="custom-fields">
          <div class="fields-header">
            <span class="fields-title">自定义字段</span>
            <a-button type="link" @click="addCustomField">
              <PlusOutlined />
              添加字段
            </a-button>
  </div>
          <div class="fields-list">
            <div v-for="(field, index) in newEntity.customFields" :key="index" class="field-item">
              <a-input
                v-model:value="field.key"
                placeholder="字段名"
                style="width: 120px"
              />
              <span class="field-separator">:</span>
              <a-input
                v-model:value="field.value"
                placeholder="字段值"
                style="flex: 1"
              />
              <a-button type="link" danger @click="removeCustomField(index)">
                <CloseOutlined />
              </a-button>
            </div>
          </div>
        </div>
      </a-form>
    </a-modal>
    
    <!-- 新增关系弹窗 -->
    <a-modal
      v-model:visible="addRelationModalVisible"
      title="新增关系"
      @ok="handleAddRelation"
      @cancel="addRelationModalVisible = false"
      :width="800"
    >
      <a-form :model="newRelation" layout="vertical">
        <div class="relation-form">
          <!-- 关系主体部分 -->
          <a-row class="relation-main" align="middle">
            <!-- 源实体 -->
            <a-col :flex="4">
              <a-form-item>
                <a-select
                  v-model:value="newRelation.source"
                  placeholder="请选择源实体"
                  class="entity-select"
                  show-search
                  :filter-option="filterOption"
                >
                  <a-select-option
                    v-for="entity in graphData.nodes"
                    :key="entity.id"
                    :value="entity.id"
                    :label="entity.name"
                  >
                    {{ entity.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <!-- 箭头1 -->
            <a-col :flex="1" class="relation-arrow-col">
              <ArrowRightOutlined />
            </a-col>

            <!-- 关系类型 -->
            <a-col :flex="4">
              <a-form-item>
                <a-input v-model:value="newRelation.relationType" placeholder="请输入关系类型" class="relation-type-input" />
              </a-form-item>
            </a-col>

            <!-- 箭头2 -->
            <a-col :flex="1" class="relation-arrow-col">
              <ArrowRightOutlined />
            </a-col>

            <!-- 目标实体 -->
            <a-col :flex="2">
              <a-form-item>
                <a-select v-model:value="newRelation.target" placeholder="请选择目标实体" class="entity-select" show-search :filter-option="filterOption">
                  <a-select-option 
                    v-for="entity in graphData.nodes.filter(node => node.id !== newRelation.source)" 
                    :key="entity.id" 
                    :value="entity.id"
                    :label="entity.name"
                  >
                    {{ entity.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 关系描述 -->
          <a-form-item label="关系描述">
            <a-textarea v-model:value="newRelation.description" :rows="3" placeholder="请输入关系描述" />
          </a-form-item>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onUnmounted } from 'vue';
import { PlusOutlined, LinkOutlined, CloseOutlined, ArrowRightOutlined, ApiOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';
import * as echarts from 'echarts';
import { message } from 'ant-design-vue';
import http from '@/utils/ai/http';

const props = defineProps({
  mapInfo: {
    type: Object,
    default: null
  }
});

// 定义emit事件
const emit = defineEmits(['refreshMap']);

// 图表实例
const chart = ref(null);
const graphRef = ref(null);

// 数据状态
const graphData = ref({
  nodes: [],
  links: [],
  categories: []
});

// 统计数据
const stats = ref({
  entityCount: 0,
  relationCount: 0,
  categoryCount: 0
});

// 搜索相关
const searchText = ref('');
const searchResults = ref([]);

// 选中的节点或边
const selectedItem = ref(null);

// 实体和关系类型
const categories = ref([]);
const relationTypes = ref([]);

// 弹窗状态
const addEntityModalVisible = ref(false);
const addRelationModalVisible = ref(false);

// 新增实体和关系的数据
const newEntity = ref({
  name: '',
  category: '',
  description: '',
  customFields: []
});

const newRelation = ref({
  relationType: '',
  source: '',
  target: '',
  description: ''
});

// 添加加载状态
const isLoading = ref(true);

// 初始化图谱
const initGraph = () => {
  if (!graphRef.value) return;
  
  // 销毁旧实例
  if (chart.value) {
    chart.value.dispose();
  }
  
  // 创建新实例
  chart.value = echarts.init(graphRef.value);
  
  // 设置图表配置
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        if (params.dataType === 'node') {
          return `实体: ${params.data.name}<br/>类型: ${params.data.category}`;
        } else {
          return `关系: ${params.data.type}<br/>描述: ${params.data.description}`;
        }
      }
    },
    legend: {
      data: categories.value.map(category => ({
        name: category,
        icon: 'circle'
      }))
    },
    animationDuration: 1500,
    animationEasingUpdate: 'quinticInOut',
    series: [{
      type: 'graph',
      layout: 'force',
      data: graphData.value.nodes,
      links: graphData.value.links,
      categories: graphData.value.categories,
      roam: true,
      draggable: true,
      label: {
        show: true,
        position: 'right',
        formatter: '{b}'
      },
      force: {
        repulsion: 100,
        edgeLength: 100,
        gravity: 0.1,
        friction: 0.6
      },
      lineStyle: {
        color: 'source',
        curveness: 0.3,
        opacity: 0.5,
        width: 3
      },
      emphasis: {
        focus: 'adjacency',
        lineStyle: {
          width: 8,
          opacity: 1
        }
      },
      select: {
        lineStyle: {
          width: 8,
          opacity: 1
        }
      },
      itemStyle: {
        opacity: 0.8
      }
    }]
  };
  
  // 设置图表配置
  chart.value.setOption(option);
  
  // 添加点击事件
  chart.value.on('click', (params) => {
    if (params.dataType === 'node') {
      handleNodeClick(params.data);
      updateNodeOpacity(params.data.id);
    } else if (params.dataType === 'edge') {
      handleEdgeClick(params.data);
    }
  });
};

// 更新节点透明度
const updateNodeOpacity = (selectedNodeId) => {
  const option = chart.value.getOption();
  const nodes = option.series[0].data;
  const links = option.series[0].links;
  
  // 找出与选中节点直接相连的节点ID
      const relatedNodeIds = new Set();
  relatedNodeIds.add(selectedNodeId);
  
  // 遍历所有边，找出与选中节点相连的节点
  links.forEach(link => {
    if (link.source === selectedNodeId) {
        relatedNodeIds.add(link.target);
    } else if (link.target === selectedNodeId) {
      relatedNodeIds.add(link.source);
    }
  });
  
  // 更新所有节点的透明度
  nodes.forEach(node => {
    if (relatedNodeIds.has(node.id)) {
      // 选中节点及其相关节点保持正常显示
      node.itemStyle = {
        ...node.itemStyle,
        opacity: 1
      };
    } else {
      // 其他节点虚化
      node.itemStyle = {
        ...node.itemStyle,
        opacity: 0.3
      };
    }
  });
  
  // 更新所有边的透明度
  const updatedLinks = links.map(link => {
    if (relatedNodeIds.has(link.source) && relatedNodeIds.has(link.target)) {
      // 相关节点之间的边保持正常显示
      return {
        ...link,
        lineStyle: {
          ...link.lineStyle,
          opacity: 1
        }
      };
    } else {
      // 其他边虚化
      return {
        ...link,
        lineStyle: {
          ...link.lineStyle,
          opacity: 0.3
        }
      };
    }
  });
  
  // 更新图表
  chart.value.setOption({
    series: [{
      data: nodes,
      links: updatedLinks
    }]
  });
};

// 处理节点点击
const handleNodeClick = (node) => {
  // 从原始数据中查找完整的节点信息
  const fullNodeData = props.mapInfo?.data?.nodes?.find(n => n.id === node.id);
  console.log('选中的节点完整数据:', {
    ...node,
    fields: fullNodeData?.fields || {}
  });
  
      selectedItem.value = {
    type: 'node',
    ...node,
    fields: fullNodeData?.fields || {}
  };
};

// 处理边点击
const handleEdgeClick = (edge) => {
  // 从原始数据中查找完整的边信息
  const fullEdgeData = props.mapInfo?.data?.links?.find(l => 
    l.source === edge.source && l.target === edge.target
  );
  console.log('selectedItem', selectedItem);
  console.log('选中的边完整信息:', {
    id: edge.id,
    source: edge.source,
    target: edge.target,
    type: edge.type,
    description: edge.description,
    fields: fullEdgeData?.fields || {},
    lineStyle: edge.lineStyle,
    ...fullEdgeData
  });
  
  selectedItem.value = {
    ...edge,
    type: 'edge',
    fields: {
      relationType: edge.type,
    }
  };
};

// 处理搜索
const handleSearch = (value) => {
  if (!value) {
    searchResults.value = [];
    resetNodeOpacity();
    return;
  }
  
  const results = [];
  const matchedNodeIds = new Set();
  
  // 搜索实体
  graphData.value.nodes.forEach(node => {
    if (node.name.toLowerCase().includes(value.toLowerCase())) {
      results.push({
        type: 'node',
        ...node
      });
      matchedNodeIds.add(node.id);
    }
  });
  
  // 更新节点透明度
  const option = chart.value.getOption();
  const nodes = option.series[0].data;
  const links = option.series[0].links;
  
  // 更新所有节点的透明度
  nodes.forEach(node => {
    if (matchedNodeIds.has(node.id)) {
      // 匹配的节点保持正常显示
      node.itemStyle = {
        ...node.itemStyle,
          opacity: 1
      };
    } else {
      // 其他节点虚化
      node.itemStyle = {
        ...node.itemStyle,
        opacity: 0.3
      };
    }
  });
  
  // 更新所有边的透明度
  const updatedLinks = links.map(link => {
    if (matchedNodeIds.has(link.source) || matchedNodeIds.has(link.target)) {
      // 与匹配节点相连的边保持正常显示
      return {
        ...link,
        lineStyle: {
          ...link.lineStyle,
          opacity: 1
        }
      };
    } else {
      // 其他边虚化
      return {
        ...link,
        lineStyle: {
          ...link.lineStyle,
          opacity: 0.3
        }
      };
    }
  });
  
  // 更新图表
  chart.value.setOption({
    series: [{
      data: nodes,
      links: updatedLinks
    }]
  });
  
  searchResults.value = results;
};

// 显示新增实体弹窗
const showAddEntityModal = () => {
  newEntity.value = {
    name: '',
    category: '',
    description: '',
    customFields: []
  };
  addEntityModalVisible.value = true;
};

// 显示新增关系弹窗
const showAddRelationModal = () => {
  newRelation.value = {
    relationType: '',
    source: '',
    target: '',
    description: ''
  };
  addRelationModalVisible.value = true;
};

// 添加自定义字段
const addCustomField = () => {
  newEntity.value.customFields.push({
    key: '',
    value: ''
  });
};

// 删除自定义字段
const removeCustomField = (index) => {
  // newEntity.value.customFields.splice(index, 1);
  // 验证实体名称格式
  const nameRegex = /^[a-zA-Z\u4e00-\u9fa5/][a-zA-Z0-9\u4e00-\u9fa5_/]*$/;
  if (!nameRegex.test(newEntity.value.name)) {
    message.error('实体名称不能以数字开头，且只能包含字母a-zA-Z、数字0-9、下划线_、斜杠/');
    return;
  }
};

// 验证字段名称
const validateFieldName = (name) => {
  const nameRegex = /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5_]*$/;
  return nameRegex.test(name);
};

// 处理新增实体
const handleAddEntity = async () => {
  // 验证实体名称
  if (!newEntity.value.name) {
    message.error('请输入实体名称');
    return;
  }
  
  // 验证实体名称格式
  const nameRegex = /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5_]*$/;
  if (!nameRegex.test(newEntity.value.name)) {
    message.error('实体名称不能以数字开头，且不能包含. : -等特殊字符');
    return;
  }

  // 验证实体类型
  if (!newEntity.value.category) {
    message.error('请输入实体类型');
    return;
  }

  // 验证实体类型格式
  if (!nameRegex.test(newEntity.value.category)) {
    message.error('实体类型不能以数字开头，且不能包含. : -等特殊字符');
    return;
  }

  // 验证自定义字段名称
  for (const field of newEntity.value.customFields) {
    if (field.key && !validateFieldName(field.key)) {
      message.error(`自定义字段"${field.key}"不能以数字开头，且不能包含. : -等特殊字符`);
      return;
    }
  }

  try {
    // 从当前URL中获取relation_id
    const currentPath = window.location.pathname;
    const relationId = currentPath.split('/relation/')[1];
    
    if (!relationId) {
      message.error('无法获取知识库ID');
      return;
    }

    // 获取用户信息
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    if (!userInfo.userId || !userInfo.username) {
      message.error('用户信息获取失败');
      return;
    }

    // 处理自定义字段
    const fields = {
      description: newEntity.value.description || ''
    };
    newEntity.value.customFields.forEach(field => {
      if (field.key && field.value) {
        fields[field.key] = field.value;
      }
    });

    // 构建请求数据
    const entityData = {
      name: newEntity.value.name,
      type: newEntity.value.category,
      fields: fields
    };

    // 调用新增实体接口
    const res = await http.post(`/knowledge_api/${relationId}/entity`, {
      entity_data: entityData
    }, {
      params: {
        username: userInfo.username,
        user_id: userInfo.userId
      }
    });

    if (res.data.code === 200) {
      // 生成唯一ID
      const newId = `entity_${Date.now()}`;
      
      // 添加新实体到图谱数据
      const entity = {
        id: newId,
        name: newEntity.value.name,
        category: newEntity.value.category,
        description: newEntity.value.description,
        fields: fields,
        symbolSize: 50
      };
      
      graphData.value.nodes.push(entity);
      
      // 更新图表
      chart.value.setOption({
        series: [{
          data: graphData.value.nodes
        }]
      });
      
      // 关闭弹窗
      addEntityModalVisible.value = false;
      
      // 重置表单
      newEntity.value = {
        name: '',
        category: '',
        description: '',
        customFields: []
      };

      message.success('实体添加成功');
      
      // 通知父组件刷新数据
      emit('refreshMap');
      
      // 直接从接口获取最新数据
      fetchMapData();
    } else {
      message.error(res.data.message || '添加实体失败');
    }
  } catch (error) {
    console.error('添加实体失败:', error);
    message.error('添加实体失败');
  }
};

// 处理新增关系
const handleAddRelation = async () => {
  if (!newRelation.value.relationType || !newRelation.value.source || !newRelation.value.target) {
    message.error('请填写必填项');
    return;
  }

  // 验证关系类型格式
  const nameRegex = /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5_]*$/;
  if (!nameRegex.test(newRelation.value.relationType)) {
    message.error('关系类型不能以数字开头，且不能包含. : -等特殊字符');
    return;
  }

  try {
    // 从当前URL中获取relation_id
    const currentPath = window.location.pathname;
    const relationId = currentPath.split('/relation/')[1];
    
    if (!relationId) {
      message.error('无法获取知识库ID');
      return;
    }

    // 获取用户信息
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    if (!userInfo.userId || !userInfo.username) {
      message.error('用户信息获取失败');
      return;
    }

    // 构建请求数据
    const relationData = {
      source_id: newRelation.value.source,
      target_id: newRelation.value.target,
      type: newRelation.value.relationType,
      fields: {
        description: newRelation.value.description || ''
      }
    };

    // 调用新增关系接口
    const res = await http.post(`/knowledge_api/${relationId}/relation`, relationData, {
      params: {
        username: userInfo.username,
        user_id: userInfo.userId
      }
    });

    if (res.data.code === 200) {
      // 添加新关系到图谱数据
      const relation = {
        source: newRelation.value.source,
        target: newRelation.value.target,
        relationType: newRelation.value.relationType,
        description: newRelation.value.description
      };
      
      graphData.value.links.push(relation);
      
      // 更新图表
      chart.value.setOption({
        series: [{
          links: graphData.value.links
        }]
      });
      
      // 关闭弹窗
      addRelationModalVisible.value = false;
      
      // 重置表单
      newRelation.value = {
        relationType: '',
        source: '',
        target: '',
        description: ''
      };

      message.success('关系添加成功');
      
      // 通知父组件刷新数据
      emit('refreshMap');
      
      // 直接从接口获取最新数据
      fetchMapData();
    } else {
      message.error(res.data.message || '添加关系失败');
    }
  } catch (error) {
    console.error('添加关系失败:', error);
    message.error('添加关系失败');
  }
};

// 处理保存
const handleSave = () => {
  if (!selectedItem.value) return;
  
  // 更新图谱数据
  if (selectedItem.value.type === 'node') {
    const nodeIndex = graphData.value.nodes.findIndex(node => node.id === selectedItem.value.id);
    if (nodeIndex !== -1) {
      graphData.value.nodes[nodeIndex] = { ...selectedItem.value };
    }
  } else {
    const edgeIndex = graphData.value.links.findIndex(link => 
      link.source === selectedItem.value.source && link.target === selectedItem.value.target
    );
    if (edgeIndex !== -1) {
      graphData.value.links[edgeIndex] = { ...selectedItem.value };
    }
  }
  
  // 更新图表
  chart.value.setOption({
    series: [{
      data: graphData.value.nodes,
      links: graphData.value.links
    }]
  });
  
  // 关闭编辑卡片
  selectedItem.value = null;
  
  // 通知父组件刷新数据
  emit('refreshMap');
  
  // 直接从接口获取最新数据
  fetchMapData();
};

// 监听 selectedItem 变化
watch(selectedItem, (newVal) => {
  if (!newVal) {
    resetNodeOpacity();
  }
});

// 重置所有节点和边的透明度
const resetNodeOpacity = () => {
  const option = chart.value.getOption();
  const nodes = option.series[0].data;
  const links = option.series[0].links;
  
  // 重置所有节点的透明度
  const updatedNodes = nodes.map(node => ({
      ...node,
      itemStyle: {
      ...node.itemStyle,
      opacity: 1
      }
    }));
    
  // 重置所有边的透明度
  const updatedLinks = links.map(link => ({
        ...link,
        lineStyle: {
      ...link.lineStyle,
      opacity: 1
    }
  }));
  
  // 更新图表
  chart.value.setOption({
    series: [{
      data: updatedNodes,
      links: updatedLinks
    }]
  });
};

// 处理取消
const handleCancel = () => {
  selectedItem.value = null;
  resetNodeOpacity();
};

// 获取实体名称
const getEntityName = (entityId) => {
  const entity = graphData.value.nodes.find(node => node.id === entityId);
  return entity ? entity.name : '';
};

// 获取实体类型颜色
const getCategoryColor = (type) => {
  // 定义颜色列表
  const colorList = [
    '#1890ff', // 蓝色
    '#52c41a', // 绿色
    '#722ed1', // 紫色
    '#faad14', // 金色
    '#13c2c2', // 青色
    '#eb2f96', // 粉色
    '#fa8c16', // 橙色
    '#a0d911', // 浅绿
    '#2f54eb'  // 深蓝
  ];

  // 从categories中找到对应的索引
  const categoryIndex = graphData.value.categories.findIndex(cat => cat.name.toLowerCase() === type.toLowerCase());
  
  // 如果找到对应的类型，返回对应的颜色
  if (categoryIndex !== -1) {
    return colorList[categoryIndex % colorList.length];
  }
  
  // 如果没找到对应的类型，也返回一个颜色（使用类型名称的哈希值来确定颜色）
  const hash = type.split('').reduce((acc, char) => {
    return char.charCodeAt(0) + ((acc << 5) - acc);
  }, 0);
  return colorList[Math.abs(hash) % colorList.length];
};

// 获取关系类型颜色
const getRelationColor = (name) => {
  const colors = {
    'HAS_PARAM': '#722ed1',
    '其他': '#bfbfbf'
  };
  return colors[name] || '#bfbfbf';
};

// 更新图谱数据
const updateGraphData = (mapInfo) => {
  console.log('收到的地图信息:', mapInfo);
  
  if (!mapInfo || !mapInfo.data) {
    console.warn('地图信息数据不完整');
    isLoading.value = false;
    return;
  }
  
  const { nodes, links, categories, version } = mapInfo.data;
  
  if (!nodes || !links || !categories) {
    console.warn('地图信息缺少必要数据');
    isLoading.value = false;
        return;
      }
      
  // 提取实体类型
  categories.value = categories.map(cat => cat.name);
  
  // 提取关系类型
  relationTypes.value = [...new Set(links.map(link => link.type))];
  
  // 转换数据格式
  graphData.value = {
    nodes: nodes.map(node => ({
      id: node.id,
      name: node.name,
      category: node.type,
      description: node.fields?.description || '',
      symbolSize: 50,
      itemStyle: {
        color: getCategoryColor(node.type)
      }
    })),
    links: links.map(link => ({
      id: link.id,
      source: link.source,
      target: link.target,
      type: link.type,
      description: link.description || '',
      lineStyle: {
        color: getRelationColor(link.type)
      }
    })),
    categories: categories.map(category => ({
      name: category.name
    })),
    version: version
  };
  
  // 更新统计数据
  stats.value = {
    entityCount: nodes.length,
    relationCount: links.length,
    categoryCount: categories.length
  };
  
  // 更新图表
  if (chart.value) {
    chart.value.setOption({
      series: [{
        data: graphData.value.nodes,
        links: graphData.value.links,
        categories: graphData.value.categories
      }]
    });
  }
  
  // 数据加载完成后关闭加载状态
  isLoading.value = false;
};

// 监听 mapInfo 变化
watch(() => props.mapInfo, (newVal) => {
  if (newVal) {
    updateGraphData(newVal);
  }
}, { deep: true });

// 刷新图谱数据
const refreshGraphData = () => {
  // 显示加载状态
  isLoading.value = true;
  
  // 清空选中状态
  selectedItem.value = null;
  
  // 重置图谱数据
  graphData.value = {
    nodes: [],
    links: [],
    categories: []
  };
  
  // 重置统计数据
  stats.value = {
    entityCount: 0,
    relationCount: 0,
    categoryCount: 0
  };
  
  // 使用 nextTick 确保 DOM 更新后再重新初始化图谱
  nextTick(() => {
    // 销毁旧实例
    if (chart.value) {
      chart.value.dispose();
    }
    
    // 重新初始化图谱
    initGraph();
    
    // 如果有新的地图信息，则更新数据
    if (props.mapInfo) {
      updateGraphData(props.mapInfo);
    } else {
      isLoading.value = false;
      }
    });
  };
  
// 直接从接口获取最新地图数据
const fetchMapData = async () => {
  try {
    // 显示加载状态
    isLoading.value = true;
    
    // 从当前URL中获取relation_id
    const currentPath = window.location.pathname;
    const relationId = currentPath.split('/relation/')[1];
    
    if (!relationId) {
      message.error('无法获取知识库ID');
      isLoading.value = false;
      return;
    }

    // 获取用户信息
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    if (!userInfo.userId || !userInfo.username) {
      message.error('用户信息获取失败');
      isLoading.value = false;
      return;
    }

    // 调用获取地图信息接口
    const res = await http.get(`/knowledge_api/${relationId}/map`, {
      params: {
        username: userInfo.username,
        user_id: userInfo.userId
      }
    });

    if (res.data.code === 200) {
      console.log('直接获取到的地图信息:', res.data);
      // 更新图谱数据
      updateGraphData(res.data);
      
      // 通知父组件刷新数据（可能父组件也需要更新其他数据）
      emit('refreshMap');
    } else {
      message.error(res.data.message || '获取地图信息失败');
      isLoading.value = false;
    }
  } catch (error) {
    console.error('获取地图信息失败:', error);
    message.error('获取地图信息失败');
    isLoading.value = false;
  }
};

// 暴露方法给父组件
defineExpose({
  refreshGraphData,
  fetchMapData
});

// 监听窗口大小变化
const handleResize = () => {
  if (chart.value) {
    chart.value.resize();
  }
};

// 组件挂载时初始化
onMounted(() => {
  initGraph();
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  if (chart.value) {
    chart.value.dispose();
  }
  window.removeEventListener('resize', handleResize);
});

// 处理遮罩层点击
const handleMaskClick = () => {
  selectedItem.value = null;
  resetNodeOpacity();
};

// 处理删除
const handleDelete = async () => {
  if (!selectedItem.value) return;
  
  if (selectedItem.value.type === 'node') {
    try {
      // 从当前URL中获取relation_id
      const currentPath = window.location.pathname;
      const relationId = currentPath.split('/relation/')[1];
      
      if (!relationId) {
        message.error('无法获取知识库ID');
        return;
      }

      // 获取用户信息
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      if (!userInfo.userId || !userInfo.username) {
        message.error('用户信息获取失败');
        return;
      }

      // 从mapInfo中获取version
      const nodeData = props.mapInfo?.data?.nodes?.find(node => node.id === selectedItem.value.id);
      const version = nodeData?.fields?.version || '';

      // 调用删除实体接口
      const res = await http.delete(`/knowledge_api/${relationId}/entity`, {
        data: {
          version: version,
          entity_id: selectedItem.value.id
        }
      });

      if (res.data.code === 200) {
        // 删除实体
        const nodeIndex = graphData.value.nodes.findIndex(node => node.id === selectedItem.value.id);
        if (nodeIndex !== -1) {
          graphData.value.nodes.splice(nodeIndex, 1);
        }
        
        // 删除与该实体相关的所有关系
        graphData.value.links = graphData.value.links.filter(link => 
          link.source !== selectedItem.value.id && link.target !== selectedItem.value.id
        );
  
  // 更新图表
        chart.value.setOption({
          series: [{
            data: graphData.value.nodes,
            links: graphData.value.links
          }]
        });
      
        // 关闭编辑卡片
        selectedItem.value = null;
        message.success('实体已删除');
        
        // 通知父组件刷新数据
        emit('refreshMap');
        
        // 直接从接口获取最新数据
        fetchMapData();
      } else {
        message.error(res.data.message || '删除实体失败');
      }
    } catch (error) {
      console.error('删除实体失败:', error);
      message.error('删除实体失败');
    }
  } else if (selectedItem.value.type === 'edge') {
    try {
      // 获取 relation_uid 和 relation_id
      const currentPath = window.location.pathname;
      const relationUid = currentPath.split('/relation/')[1];
      const relationId = selectedItem.value.id;

      // 调用新的删除关系接口
      const res = await http.delete(`/knowledge_api/${relationUid}/delete_relation/${relationId}`);
      if (res.data.code === 200) {
        // 前端同步删除
        let index = graphData.value.links.findIndex(link => link.id === relationId);
        if (index === -1) {
          index = graphData.value.links.findIndex(link => 
            link.source === selectedItem.value.source && link.target === selectedItem.value.target
          );
        }
        if (index !== -1) {
          graphData.value.links.splice(index, 1);
        }
        chart.value.setOption({
          series: [{ links: graphData.value.links }]
        });
        selectedItem.value = null;
        message.success('关系已删除');
        emit('refreshMap');
        fetchMapData();
      } else {
        message.error(res.data.message || '删除关系失败');
      }
    } catch (error) {
      console.error('删除关系失败:', error);
      message.error('删除关系失败');
    }
  }
};

// 获取实体的所有关系
const getEntityRelations = (entityId) => {
  return graphData.value.links.filter(link => link.source === entityId);
};

// 获取实体的上游关系
const getUpstreamRelations = (entityId) => {
  return graphData.value.links.filter(link => link.target === entityId);
};

// 获取实体的下游关系
const getDownstreamRelations = (entityId) => {
  return graphData.value.links.filter(link => link.source === entityId);
};

const filterOption = (input, option) => {
  // 兼容 Ant Design Vue 2.x/3.x
  const label = (option.label || (option.children && option.children[0]) || '').toString().toLowerCase();
  const value = (option.value || '').toString().toLowerCase();
  return label.includes(input.toLowerCase()) || value.includes(input.toLowerCase());
};
</script>

<style scoped>
.relation-map-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
}

/* 左侧面板样式 */
.left-panel {
  width: 320px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 100;
}

.panel-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 16px;
  transition: all 0.3s ease;
}

.action-section {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  margin-top: 16px;
}

.action-section .ant-btn {
  flex: 1;
}

.search-section {
  width: 100%;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.stat-item {
  flex: 1;
  padding: 12px;
  border-radius: 4px;
  text-align: center;
  transition: all 0.3s;
}

.stat-item:hover {
  background-color: #f0f0f0;
}

.stat-value {
  font-size: 20px;
  font-weight: 500;
  color: #1890ff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
}

/* 图谱容器样式 */
.graph-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  width: 108%;
}

.graph-content {
  width: 100%;
  height: 100%;
}

/* 右侧面板样式 */
.right-panel {
  position: absolute;
  top: 16px;
  right: -300px;
  width: 340px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  z-index: 100;
  transform: translateX(0);
  opacity: 0;
}

.right-panel.show {
  right: 16px;
  transform: translateX(0);
  opacity: 1;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.close-btn {
  padding: 0;
  height: 24px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.panel-content {
  padding: 16px;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  margin-bottom: 8px;
  color: #262626;
}

.entity-info {
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
}

/* 遮罩层样式 */
.mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.02);
  z-index: 99;
}

/* 详情卡片样式 */
.detail-card {
  margin-top: 0;
  max-height: 40vh;
  overflow-y: auto;
}

.detail-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 8px;
}

.detail-label {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
  min-width: 80px;
  flex-shrink: 0;
  text-align: left;
}

.detail-value {
  font-size: 14px;
  color: #262626;
  word-break: break-all;
  flex: 1;
  text-align: left;
}

/* 隐藏原生滚动条 */
.detail-card::-webkit-scrollbar {
  width: 6px;
}

.detail-card::-webkit-scrollbar-track {
  background: transparent;
}

.detail-card::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.detail-card:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
}

.relation-preview {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 12px;
}

.relation-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.entity-box {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.entity-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  height: 28px;
  background-color: #1890ff;
  border-radius: 4px;
  max-width: 120px;
}

.entity-icon {
  font-size: 14px;
  color: #fff;
}

.entity-name {
  font-size: 13px;
  color: #fff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
}

.relation-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  padding: 0 8px;
  flex: 0 0 auto;
}

.relation-arrow.upstream {
  color: #722ed1;
}

.relation-arrow.downstream {
  color: #1890ff;
}

.relation-edit {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
  text-align: left;
}

.relation-input {
  width: 100%;
}

.relation-input :deep(.ant-input) {
  text-align: left;
  font-weight: 500;
  color: #1890ff;
}

.relation-description {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.description-input :deep(.ant-input) {
  font-size: 13px;
  color: #595959;
}

.delete-section {
  background-color: #fff;
  border-radius: 4px;
}

.entity-preview {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 12px;
}

.entity-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 12px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.entity-relations {
  display: flex;
  flex-direction: column;
}

.relation-list {
  display: flex;
  flex-direction: column;
}

.relation-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px;
  border-radius: 4px;
  margin-bottom: 4px;
}

.relation-label {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
  min-width: 60px;
  text-align: left;
}

.relation-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  padding: 0 8px;
}

.entity-select {
  min-width: 100%;
  margin-top: 20px;
}

.relation-type-input {
  flex: 1;
  margin-top: 20px;
}

.relation-type {
  font-size: 13px;
  font-weight: 500;
  min-width: 60px;
  text-align: left;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: #f0f0f0;
}

.relation-type.upstream {
  color: #722ed1;
  background-color: #f9f0ff;
}

.relation-type.downstream {
  color: #1890ff;
  background-color: #e6f7ff;
}

.related-entity {
  display: flex;
  justify-content: flex-start;
}

.relation-group {
  margin-bottom: 8px;
}

/* 添加加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1000;
}

.loading-text {
  margin-top: 16px;
  font-size: 14px;
  color: #595959;
}

.custom-fields {
  margin-top: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 12px;
}

.fields-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.fields-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.fields-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.field-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-separator {
  color: #8c8c8c;
  padding: 0 4px;
}

.form-row {
  display: flex;
  gap: 16px;
}

.form-item-name {
  flex: 1;
}

.form-item-type {
  flex: 1;
}

.relation-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.relation-main {
  width: 100%;
  margin-top: 2%;
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.relation-arrow-col {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  font-size: 16px;
  flex: 0 0 24px;
}

.entity-select {
  width: 100%;
  flex: 1;
}

.relation-type-input {
  width: 100%;
  flex: 1;
}
</style>

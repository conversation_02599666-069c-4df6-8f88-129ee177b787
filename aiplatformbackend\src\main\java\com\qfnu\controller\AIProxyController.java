package com.qfnu.controller;

import com.qfnu.common.ResponseVO;
import com.qfnu.config.ModelConfig;
import com.qfnu.model.param.GenerateRequest;
import okhttp3.*;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;
import java.nio.charset.StandardCharsets;

@RestController
@RequestMapping("/api/ai")
public class AIProxyController {

    private static final okhttp3.MediaType JSON = okhttp3.MediaType.parse("application/json; charset=utf-8");
    private static final Gson gson = new GsonBuilder().setLenient().create();
    private static final String SSE_CONTENT_TYPE = "text/event-stream";
    
    // 恒脑AI配置
    private static final String HENGNAO_APP_KEY = "hengnaovfNxKmImw9cQGkdA0pfg";
    private static final String HENGNAO_APP_SECRET = "5n58tgpsuv58typuhqsjtmd6ln6vlf1u";
    private static final String HENGNAO_BASE_URL = "https://www.das-ai.com/open/api/chat";
    
    private String generateHengnaoSign() {
        try {
            long timestamp = System.currentTimeMillis();
            String data = timestamp + "\n" + HENGNAO_APP_SECRET + "\n" + HENGNAO_APP_KEY;
            
            Mac hmacSha256 = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(HENGNAO_APP_SECRET.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            hmacSha256.init(secretKey);
            
            byte[] signBytes = hmacSha256.doFinal(data.getBytes(StandardCharsets.UTF_8));
            String signBase64 = Base64.getEncoder().encodeToString(signBytes);
            
            return timestamp + signBase64;
        } catch (Exception e) {
            throw new RuntimeException("生成签名失败", e);
        }
    }

    static class RequestBodyData {
        String model;
        List<GenerateRequest.Message> messages;
        Integer max_tokens;
        Double temperature;
        Boolean stream;
    }

    private final ExecutorService executorService = Executors.newCachedThreadPool();

    @PostMapping(value = "/model/generate", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter generateResponse(@RequestBody GenerateRequest request) {
        SseEmitter emitter = new SseEmitter(180000L); // 3分钟超时
        try {
            String OLLAMA_URL = ModelConfig.getModelUrl(request.getModel());
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                    .writeTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                    .readTimeout(120, java.util.concurrent.TimeUnit.SECONDS)
                    .build();

            // 构建请求体
            RequestBodyData requestBodyData = new RequestBodyData();
            requestBodyData.model = request.getModel();
            requestBodyData.messages = request.getMessages();
            requestBodyData.max_tokens = request.getMax_tokens();
            requestBodyData.temperature = request.getTemperature();
            requestBodyData.stream = true; // 强制开启流式请求

            String json = gson.toJson(requestBodyData);
            okhttp3.RequestBody body = okhttp3.RequestBody.create(json, JSON);
            Request httpRequest = new Request.Builder()
                    .url(OLLAMA_URL)
                    .post(body)
                    .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Accept", SSE_CONTENT_TYPE) // 请求服务器返回SSE格式
                    .build();

            executorService.execute(() -> {
                try (Response response = client.newCall(httpRequest).execute()) {
                    if (!response.isSuccessful()) {
                        emitter.completeWithError(new IOException("Server error: " + response.code()));
                        return;
                    }

                    try (BufferedReader reader = new BufferedReader(
                            new InputStreamReader(response.body().byteStream()))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            if (!line.isEmpty()) {
                                JsonObject jsonLine = JsonParser.parseString(line).getAsJsonObject();
                                JsonObject responseData = new JsonObject();

                                // 提取需要的字段（根据实际响应结构调整）
                                if (jsonLine.has("response")) {
                                    responseData.addProperty("content", jsonLine.get("response").getAsString());
                                }
                                if (jsonLine.has("done")) {
                                    responseData.addProperty("done", jsonLine.get("done").getAsBoolean());
                                }

                                // 构造SSE事件格式：data: {"content": "...", "done": true}\n\n
                                String sseEvent = "data: " + gson.toJson(responseData) + "\n\n";
                                emitter.send(sseEvent); // 使用SSE标准格式发送

                                // 完成时关闭流
                                if (jsonLine.has("done") && jsonLine.get("done").getAsBoolean()) {
                                    emitter.complete();
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    emitter.completeWithError(e);
                }
            });

        } catch (Exception e) {
            emitter.completeWithError(e);
        }

        return emitter;
    }

    // 非流式处理保持原有逻辑（如需保留）
    @PostMapping("/model/generate/nostream")
    public ResponseVO<String> generateNonStreamResponse(@RequestBody GenerateRequest request) {
        try {
            if ("hengnao".equals(request.getModel())) {
                return handleHengnaoRequest(request);
            }
            
            String OLLAMA_URL = ModelConfig.getModelUrl(request.getModel());
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                    .writeTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                    .readTimeout(120, java.util.concurrent.TimeUnit.SECONDS)
                    .build();

            // 构建请求体
            RequestBodyData requestBodyData = new RequestBodyData();
            requestBodyData.model = request.getModel();
            requestBodyData.messages = request.getMessages();
            requestBodyData.max_tokens = request.getMax_tokens();
            requestBodyData.temperature = request.getTemperature();
            requestBodyData.stream = false;

            String json = gson.toJson(requestBodyData);
            okhttp3.RequestBody body = okhttp3.RequestBody.create(json, JSON);
            Request httpRequest = new Request.Builder()
                    .url(OLLAMA_URL)
                    .post(body)
                    .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Accept", "*/*")
                    .build();

            try (Response response = client.newCall(httpRequest).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code: " + response);
                }
                String responseBody = response.body().string();
                // 解析 JSON 数据
                JsonObject jsonObject = JsonParser.parseString(responseBody).getAsJsonObject();
                JsonArray choicesArray = jsonObject.getAsJsonArray("choices");
                if (choicesArray != null && !choicesArray.isEmpty()) {
                    JsonObject firstChoice = choicesArray.get(0).getAsJsonObject();
                    JsonObject messageObject = firstChoice.getAsJsonObject("message");
                    if (messageObject != null) {
                        String content = messageObject.get("content").getAsString();
                        return ResponseVO.success(content, "", 200);
                    }
                }
                return ResponseVO.success(responseBody);
            }
        } catch (Exception e) {
            return ResponseVO.validFail(e.getMessage());
        }
    }
    
    private ResponseVO<String> handleHengnaoRequest(GenerateRequest request) {
        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                    .writeTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                    .readTimeout(120, java.util.concurrent.TimeUnit.SECONDS)
                    .build();

            // 构建恒脑AI请求体
            JsonObject requestBody = new JsonObject();
            requestBody.addProperty("query", request.getMessages().get(request.getMessages().size() - 1).getContent());
            
            okhttp3.RequestBody body = okhttp3.RequestBody.create(requestBody.toString(), JSON);
            Request httpRequest = new Request.Builder()
                    .url(HENGNAO_BASE_URL)
                    .post(body)
                    .addHeader("appKey", HENGNAO_APP_KEY)
                    .addHeader("sign", generateHengnaoSign())
                    .addHeader("Content-Type", "application/json")
                    .build();

            try (Response response = client.newCall(httpRequest).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("请求恒脑AI失败: " + response.code());
                }
                
                String responseBody = response.body().string();
                JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();
                
                // 提取answer字段
                if (jsonResponse.has("data") && jsonResponse.getAsJsonObject("data").has("answer")) {
                    String answer = jsonResponse.getAsJsonObject("data").get("answer").getAsString();
                    return ResponseVO.success(answer, "", 200);
                }
                
                return ResponseVO.success(responseBody);
            }
        } catch (Exception e) {
            return ResponseVO.validFail("调用恒脑AI接口失败: " + e.getMessage());
        }
    }
}
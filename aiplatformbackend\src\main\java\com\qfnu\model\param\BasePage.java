package com.qfnu.model.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BasePage {
    /**
     * 当前页码
     */
    @Builder.Default
    @ApiModelProperty("当前页码")
    private Integer pageNum = 1;

    /**
     * 每页显示条数
     */
    @Builder.Default
    @ApiModelProperty("每页显示条数")
    private Integer pageSize = 10;

    /**
     * 偏移量，用于分页查询
     */
    @ApiModelProperty(value = "偏移量", hidden = true)
    private Long offset;
}
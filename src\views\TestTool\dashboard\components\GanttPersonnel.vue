<template>
    <div class="p-6">
      <!-- 工具栏 -->
      <div class="mb-6 flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <!-- 人员筛选 -->
          <el-select v-model="selectedPersons" multiple clearable placeholder="选择人员" class="w-48">
            <el-option
              v-for="person in allPersons"
              :key="person.id"
              :label="person.label"
              :value="person.id"
            />
          </el-select>
          <!-- 时间筛选 -->
          <div class="flex items-center space-x-2">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
              class="w-80"
            />
          </div>
        </div>
      </div>
  
    <!-- 甘特图容器 -->
    <div class="bg-white border border-black rounded-lg overflow-hidden">
      <!-- 甘特图头部 -->
      <div class="flex border-b border-black">
        <!-- 区域1: 左上角空白区域 -->
        <div class="bg-orange-200 border-r border-black flex-shrink-0 flex flex-col items-center justify-center" :style="{ width: `${personnelColumnWidth}px`, height: '96px' }">
          <span class="text-sm font-medium text-gray-700">人员/任务</span>
        </div>
        
        <!-- 头部拖拽条区域 -->
        <div class="w-1 bg-gray-300 flex-shrink-0"></div>
        
        <!-- 区域2: 日期栏 -->
        <div class="flex-1 h-24 overflow-hidden">
          <div class="overflow-auto" ref="dateHeaderScroll">
            <div class="flex" :style="{ minWidth: `${timelineDays.length * 64}px` }">
              <!-- 月份行 -->
              <div 
                v-for="monthGroup in monthGroups" 
                :key="monthGroup.month"
                class="flex-shrink-0 bg-orange-200 border-r border-black flex items-center justify-center h-8"
                :style="{ width: `${monthGroup.days * 64}px` }"
              >
                <span class="text-sm font-medium text-gray-700">{{ monthGroup.month }}</span>
              </div>
            </div>
            <div class="flex border-t border-black" :style="{ minWidth: `${timelineDays.length * 64}px` }">
              <div 
                v-for="day in timelineDays" 
                :key="day.key"
                class="w-16 p-3 text-center text-sm font-medium border-r border-black flex-shrink-0 bg-orange-200 h-16"
                :class="getTimeHeaderClass(day)"
              >
                <div class="text-xs text-gray-500 mb-1">{{ day.dayName }}</div>
                <div>{{ day.label }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 甘特图主体 -->
      <div class="flex">
        <!-- 区域3: 人员/任务列 (动态宽度，竖向可滚动) -->
        <div class="border-r border-black flex-shrink-0 overflow-hidden task-list-container" :style="{ width: `${personnelColumnWidth}px` }">
          <div class="overflow-auto" ref="taskListScroll" :style="{ height: `${dynamicHeight}px` }">
            <!-- 递归渲染任务树 -->
            <template v-for="(item, index) in flattenedGanttData" :key="item.id">
              <div 
                class="border-b border-gray-200 hover:bg-white transition-colors duration-200 task-row"
                :style="{ height: '40px', minHeight: '40px', maxHeight: '40px' }"
              >
                <div 
                  class="p-2 text-xs cursor-pointer flex items-center h-full relative"
                  :class="getRowClass(item.type)"
                  @click="toggleExpand(item.id)"
                  :style="{ 
                    paddingLeft: `${item.level * 16 + 8}px`,
                    borderLeft: item.level > 0 ? `${item.level * 2}px solid rgba(59, 130, 246, 0.1)` : 'none'
                  }"
                >
                  <!-- 展开/折叠箭头 -->
                  <span 
                    v-if="(item.children && item.children.length > 0) || (item.timeRanges && item.timeRanges.length > 0)"
                    class="mr-2 text-gray-500 transition-transform duration-200 hover:text-gray-700 expand-arrow flex items-center justify-center"
                    :class="item.expanded ? 'rotate-90' : ''"
                    style="font-size: 10px; width: 16px; height: 16px;"
                  >
                    <svg 
                      width="12" 
                      height="12" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      stroke="currentColor" 
                      stroke-width="2" 
                      stroke-linecap="round" 
                      stroke-linejoin="round"
                      class="transition-transform duration-200"
                      :class="item.expanded ? 'rotate-90' : ''"
                    >
                      <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                  </span>
                  <span v-else class="mr-4"></span>
                  
                  <!-- 任务标签 -->
                  <span class="flex-1 font-medium text-gray-800 truncate text-xs">{{ item.label }}</span>
                  
                  <!-- 任务类型标识 -->
                  <div 
                    v-if="item.type === 'person'"
                    class="w-1.5 h-1.5 rounded-full ml-1 task-type-indicator"
                    :style="{ backgroundColor: getPersonColor(item.id).primary }"
                  ></div>
                  <div 
                    v-else-if="item.type === 'timerange'"
                    class="w-1.5 h-1.5 rounded-full ml-1 task-type-indicator"
                    :style="{ backgroundColor: getPersonColor(item.id.split('_')[0]).light }"
                  ></div>
                  <div 
                    v-else-if="item.type === 'subtask'"
                    class="w-1.5 h-1.5 rounded-full ml-1 task-type-indicator"
                    :style="{ backgroundColor: getPersonColor(item.id.split('_')[0]).lighter }"
                  ></div>
                </div>
              </div>
            </template>
          </div>
        </div>
  
        <!-- 拖拽调整宽度的分隔条 -->
        <div 
          class="w-1 bg-gray-300 hover:bg-blue-500 cursor-col-resize transition-colors duration-200"
          @mousedown="startResize"
          title="拖拽调整宽度"
        ></div>
        
        <!-- 区域4: 时间轴 (横向和竖向都可滚动) -->
        <div class="flex-1 overflow-hidden">
          <div class="overflow-auto" ref="timelineScroll" :style="{ height: `${dynamicHeight}px` }">
            <div class="relative timeline-container" :style="{ width: `${timelineDays.length * 64}px`, height: `${timelineContentHeight}px` }">
              <!-- 时间格子背景 - 使用虚线边框 -->
              <div 
                v-for="day in timelineDays" 
                :key="day.key"
                class="absolute w-16 timeline-cell"
                :class="getTimeContentClass(day)"
                :style="{ left: `${day.key * 64}px`, height: `${timelineContentHeight}px` }"
              >
              </div>
              
              <!-- 任务行 -->
              <div 
                v-for="(item, index) in flattenedGanttData" 
                :key="`row_${item.id}`"
                class="absolute w-full timeline-row"
                :style="{ top: `${index * 40}px`, height: '40px', minHeight: '40px', maxHeight: '40px' }"
              >
                <!-- 任务条 -->
                <div 
                  v-for="task in getTaskBars(item)"
                  :key="`${task.id}_bar`"
                  class="absolute h-6 rounded-md text-xs text-white flex items-center justify-center task-bar"
                  :style="{ ...getTaskBarStyle(task), ...getTaskBarColorStyle(task) }"
                  :class="getTaskBarClass(task)"
                  :title="`${task.label}: ${task.start} 至 ${task.end}`"
                >
                  <span class="truncate px-1 text-xs font-medium">{{ task.label }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'

// 响应式数据
const dateRange = ref<[string, string] | null>(null)
const personnelColumnWidth = ref(192) // 默认宽度 192px (w-48)

// 拖拽调整宽度相关
const isResizing = ref(false)
const startX = ref(0)
const startWidth = ref(0)

// 颜色管理
const colorPalette = [
  { primary: '#3b82f6', light: '#60a5fa', lighter: '#93c5fd' }, // 蓝色系
  { primary: '#ef4444', light: '#f87171', lighter: '#fca5a5' }, // 红色系
  { primary: '#10b981', light: '#34d399', lighter: '#6ee7b7' }, // 绿色系
  { primary: '#f59e0b', light: '#fbbf24', lighter: '#fcd34d' }, // 黄色系
  { primary: '#8b5cf6', light: '#a78bfa', lighter: '#c4b5fd' }, // 紫色系
  { primary: '#06b6d4', light: '#22d3ee', lighter: '#67e8f9' }, // 青色系
  { primary: '#f97316', light: '#fb923c', lighter: '#fdba74' }, // 橙色系
  { primary: '#ec4899', light: '#f472b6', lighter: '#f9a8d4' }, // 粉色系
]

// 为每个人员分配颜色
const personColors = new Map()

// 获取人员颜色
const getPersonColor = (personId: string) => {
  if (!personColors.has(personId)) {
    const colorIndex = personColors.size % colorPalette.length
    personColors.set(personId, colorIndex)
  }
  return colorPalette[personColors.get(personId)]
}

// 滚动容器引用
const dateHeaderScroll = ref<HTMLElement>()
const taskListScroll = ref<HTMLElement>()
const timelineScroll = ref<HTMLElement>()

// 计算任务的并集时间范围
const calculateUnionTimeRange = (task: any): { start: string, end: string } | null => {
  // 如果任务本身有时间范围，直接返回
  if (task.start && task.end) {
    return { start: task.start, end: task.end }
  }
  
  // 如果没有子任务，返回null
  if (!task.children || task.children.length === 0) {
    return null
  }
  
  // 递归收集所有子任务的时间范围
  const childRanges: { start: string, end: string }[] = []
  
  const collectChildRanges = (node: any) => {
    if (node.start && node.end) {
      childRanges.push({ start: node.start, end: node.end })
    }
    if (node.children) {
      node.children.forEach((child: any) => {
        collectChildRanges(child)
      })
    }
  }
  
  collectChildRanges(task)
  
  // 如果没有找到时间范围，返回null
  if (childRanges.length === 0) {
    return null
  }
  
  // 计算时间范围的并集
  const startDates = childRanges.map(range => new Date(range.start))
  const endDates = childRanges.map(range => new Date(range.end))
  
  const minStart = new Date(Math.min(...startDates.map(date => date.getTime())))
  const maxEnd = new Date(Math.max(...endDates.map(date => date.getTime())))
  
  return {
    start: minStart.toISOString().split('T')[0],
    end: maxEnd.toISOString().split('T')[0]
  }
}

// 计算一级任务的时间范围（根据是否有子任务决定数据来源）
const calculateFirstLevelTimeRange = (task: any): { start: string, end: string } | null => {
  // 如果一级任务下有二级任务，取二级任务的并集
  if (task.children && task.children.length > 0) {
    return calculateUnionTimeRange(task)
  }
  
  // 如果一级任务下没有二级任务，取一级任务自身数据
  if (task.start && task.end) {
    return { start: task.start, end: task.end }
  }
  
  return null
}

// 甘特图数据
const ganttData = ref([
  {
    id: 'person1',
    label: '张三',
    type: 'person',
    progress: 60,
    expanded: false,
    timeRanges: [
      {
        id: 'timerange0',
        label: '用例设计',
        expanded: false,
        children:[
            {
                id: 'subtask0_1_1',
                label: '用例设计',
                type: 'subtask',
                start: '2025-07-16',
                end: '2025-07-30',
                progress: 100,
                expanded: false,
            }
        ]
      },
      {
        id: 'timerange1',
        label: '用例编写',
        progress: 100,
        expanded: false,
        children:[
            {
                id: 'subtask1_1_2',
                label: '用例执行',
                type: 'subtask',
                start: '2025-07-12',
                end: '2025-07-15',
                progress: 100,
                expanded: false,
            }
        ]
      }
    ]
  },
  {
    id: 'person2',
    label: '李四',
    type: 'person',
    progress: 80,
    expanded: false,
    timeRanges: [
      {
        id: 'timerange2',
        label: '前端开发',
        progress: 90,
        start: '2025-07-24',
        end: '2025-08-02',
        expanded: false,
      }
    ]
  },
  {
    id: 'person3',
    label: '王五',
    type: 'person',
    progress: 45,
    expanded: false,
    timeRanges: [
      {
        id: 'timerange3',
        label: '后端开发',
        progress: 70,
        start: '2025-06-25',
        end: '2025-07-18',
        expanded: false,
        children:[
            {
                id: 'subtask3_1',
                label: 'API设计',
                type: 'subtask',
                start: '2025-07-25',
                end: '2025-07-30',
                progress: 100,
                expanded: false,
            },
            {
                id: 'subtask3_2',
                label: '数据库设计',
                type: 'subtask',
                start: '2025-07-11',
                end: '2025-07-18',
                progress: 60,
                expanded: false,
            }
        ]
      }
    ]
  },
  {
    id: 'person4',
    label: '赵六',
    type: 'person',
    progress: 30,
    expanded: false,
    timeRanges: [
      {
        id: 'timerange4',
        label: '测试',
        progress: 40,
        start: '2024-07-02',
        end: '2024-07-12',
        expanded: false,
      }
    ]
  },
  {
    id: 'person5',
    label: '钱七',
    type: 'person',
    progress: 20,
    expanded: false,
    timeRanges: [
      {
        id: 'timerange5',
        label: '部署',
        progress: 25,
        start: '2024-07-10',
        end: '2024-07-21',
        expanded: false,
      }
    ]
  }
])

// 时间轴天数
const timelineDays = computed(() => {
  const days = []
  
  // 如果有选择的时间范围，使用选择的范围；否则使用默认范围
  let startDate: Date
  let endDate: Date
  
  if (dateRange.value) {
    startDate = new Date(dateRange.value[0])
    endDate = new Date(dateRange.value[1])
  } else {
    const today = new Date()
    startDate = new Date(today.getTime() - 15 * 24 * 60 * 60 * 1000)
    endDate = new Date(today.getTime() + 15 * 24 * 60 * 60 * 1000)
  }
  
  const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000)) + 1
  
  for (let i = 0; i < totalDays; i++) {
    const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000)
    const dayOfWeek = date.getDay()
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6
    const today = new Date()
    
    days.push({
      key: i,
      label: `${date.getMonth() + 1}/${date.getDate()}`,
      fullLabel: `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`,
      date: date,
      isToday: date.toDateString() === today.toDateString(),
      dayOfWeek: dayOfWeek,
      dayName: getDayName(dayOfWeek),
      isWeekend: isWeekend
    })
  }
  return days
})

// 月份分组
const monthGroups = computed(() => {
  const groups: { month: string; days: number }[] = []
  let currentMonth = timelineDays.value[0].date.getMonth()
  let currentYear = timelineDays.value[0].date.getFullYear()
  let startIndex = 0
  
  for (let i = 1; i < timelineDays.value.length; i++) {
    const currentDate = timelineDays.value[i].date
    if (currentDate.getMonth() !== currentMonth || currentDate.getFullYear() !== currentYear) {
      // 添加前一个月份组
      const monthName = `${currentYear}年${currentMonth + 1}月`
      groups.push({ 
        month: monthName, 
        days: i - startIndex 
      })
      
      // 更新当前月份
      currentMonth = currentDate.getMonth()
      currentYear = currentDate.getFullYear()
      startIndex = i
    }
  }
  
  // 添加最后一个月份组
  const lastMonthName = `${currentYear}年${currentMonth + 1}月`
  groups.push({ 
    month: lastMonthName, 
    days: timelineDays.value.length - startIndex 
  })
  
  return groups
})

// 获取星期几名称
const getDayName = (dayOfWeek: number) => {
  const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return dayNames[dayOfWeek]
}

// 获取行样式
const getRowClass = (type: string) => {
  switch (type) {
    case 'person':
      return 'bg-white border-l-4 border-l-blue-500 shadow-sm'
    case 'timerange':
      return 'bg-white border-l-4 border-l-blue-400'
    case 'task':
      return 'bg-white'
    case 'subtask':
      return 'bg-gray-50 border-l-4 border-l-blue-300'
    default:
      return 'bg-white'
  }
}

// 获取任务条样式
const getTaskBarClass = (task: any) => {
  // 根据任务类型和层级返回对应的颜色类名
  return 'task-bar-color'
}

// 获取任务条颜色样式
const getTaskBarColorStyle = (task: any) => {
  // 根据任务ID确定人员
  let personId = null
  
  // 从任务ID中提取人员ID
  if (task.id.includes('person')) {
    // 格式：person1_timerange1_subtask1_1_1 或 person1_timerange1_self
    const parts = task.id.split('_')
    personId = parts[0] // 第一部分是人员ID
  } else if (task.id.startsWith('timerange')) {
    // 格式：timerange1_subtask1_1_1 或 timerange1_self
    // 需要从扁平化数据中找到对应的人员
    for (const person of ganttData.value) {
      if (person.timeRanges) {
        for (const timeRange of person.timeRanges) {
          if (timeRange.id === task.id.split('_')[0]) {
            personId = person.id
            break
          }
        }
      }
      if (personId) break
    }
  }
  
  if (!personId) {
    return { backgroundColor: '#6b7280' } // 默认灰色
  }
  
  const colors = getPersonColor(personId)
  
  // 根据任务类型选择颜色深浅
  if (task.type === 'person') {
    return { backgroundColor: colors.primary }
  } else if (task.type === 'timerange') {
    return { backgroundColor: colors.light }
  } else if (task.type === 'subtask') {
    return { backgroundColor: colors.lighter }
  } else {
    return { backgroundColor: colors.light }
  }
}

// 获取任务条
const getTaskBars = (item: any) => {
  // 人员行：显示多个独立的时间段
  if (item.type === 'person' && item.timeRanges) {
    const taskBars: any[] = []
    
    // 收集所有一级任务的时间范围
    item.timeRanges.forEach((timeRange: any) => {
      // 如果一级任务下有二级任务，显示每个二级任务的独立时间段
      if (timeRange.children && timeRange.children.length > 0) {
        timeRange.children.forEach((child: any) => {
          if (child.start && child.end) {
            // 检查是否在时间轴范围内
            const taskStart = new Date(child.start)
            const taskEnd = new Date(child.end)
            const timelineStart = timelineDays.value[0].date
            const timelineEnd = timelineDays.value[timelineDays.value.length - 1].date
            
            if (taskStart <= timelineEnd && taskEnd >= timelineStart) {
              taskBars.push({
                id: `${item.id}_${timeRange.id}_${child.id}`,
                label: timeRange.label, // 使用一级任务的名字
                type: 'subtask', // 修正：子任务类型
                start: child.start,
                end: child.end
              })
            }
          }
        })
      } else {
        // 如果一级任务下没有二级任务，取一级任务自身数据
        if (timeRange.start && timeRange.end) {
          const taskStart = new Date(timeRange.start)
          const taskEnd = new Date(timeRange.end)
          const timelineStart = timelineDays.value[0].date
          const timelineEnd = timelineDays.value[timelineDays.value.length - 1].date
          
          if (taskStart <= timelineEnd && taskEnd >= timelineStart) {
            taskBars.push({
              id: `${item.id}_${timeRange.id}_self`,
              label: timeRange.label, // 使用一级任务的名字
              type: 'timerange', // 修正：一级任务类型
              start: timeRange.start,
              end: timeRange.end
            })
          }
        }
      }
    })
    
    return taskBars
  }
  
  // 一级任务行：显示多个独立的时间段
  if (item.type === 'timerange') {
    const taskBars: any[] = []
    
    // 如果一级任务下有二级任务，显示每个二级任务的独立时间段
    if (item.children && item.children.length > 0) {
      item.children.forEach((child: any) => {
        if (child.start && child.end) {
          // 检查是否在时间轴范围内
          const taskStart = new Date(child.start)
          const taskEnd = new Date(child.end)
          const timelineStart = timelineDays.value[0].date
          const timelineEnd = timelineDays.value[timelineDays.value.length - 1].date
          
          if (taskStart <= timelineEnd && taskEnd >= timelineStart) {
            taskBars.push({
              id: `${item.id}_${child.id}`,
              label: item.label,
              type: 'subtask', // 修正：子任务类型
              start: child.start,
              end: child.end
            })
          }
        }
      })
    } else {
      // 如果一级任务下没有二级任务，取一级任务自身数据
      if (item.start && item.end) {
        const taskStart = new Date(item.start)
        const taskEnd = new Date(item.end)
        const timelineStart = timelineDays.value[0].date
        const timelineEnd = timelineDays.value[timelineDays.value.length - 1].date
        
        if (taskStart <= timelineEnd && taskEnd >= timelineStart) {
          taskBars.push({
            id: `${item.id}_self`,
            label: item.label,
            type: 'timerange', // 修正：一级任务类型
            start: item.start,
            end: item.end
          })
        }
      }
    }
    
    return taskBars
  }
  
  // 其他任务行：直接显示自身时间范围
  if (item.start && item.end) {
    const taskStart = new Date(item.start)
    const taskEnd = new Date(item.end)
    const timelineStart = timelineDays.value[0].date
    const timelineEnd = timelineDays.value[timelineDays.value.length - 1].date
    
    if (taskStart <= timelineEnd && taskEnd >= timelineStart) {
      return [{
        id: item.id,
        label: item.label,
        type: item.type, // 保持原有类型
        start: item.start,
        end: item.end
      }]
    }
  }
  
  return []
}

// 获取任务条样式
const getTaskBarStyle = (task: any) => {
  const taskStart = new Date(task.start)
  const taskEnd = new Date(task.end)
  const timelineStart = timelineDays.value[0].date
  const timelineEnd = timelineDays.value[timelineDays.value.length - 1].date
  
  // 计算任务在整个时间轴上的起始和结束位置（像素）
  const timelineDuration = timelineEnd.getTime() - timelineStart.getTime()
  const taskStartOffset = taskStart.getTime() - timelineStart.getTime()
  const taskEndOffset = taskEnd.getTime() - timelineStart.getTime()
  
  // 确保任务在时间轴范围内
  const clampedStartOffset = Math.max(0, taskStartOffset)
  const clampedEndOffset = Math.min(timelineDuration, taskEndOffset)
  
  // 转换为像素位置（每个格子64px）
  // 确保对齐到格子边界，并留出6px边距为悬停效果
  const startDayIndex = Math.floor((clampedStartOffset / timelineDuration) * timelineDays.value.length)
  const endDayIndex = Math.ceil((clampedEndOffset / timelineDuration) * timelineDays.value.length)
  
  const left = startDayIndex * 64 + 6
  const width = (endDayIndex - startDayIndex) * 64 - 12 // 减去左右各6px的边距
  
  return {
    left: `${left}px`,
    width: `${width}px`,
    top: '10px', // 调整为10px，与上下边框保持更均匀的距离
    display: 'block'
  }
}

// 切换展开状态
const toggleExpand = (id: string) => {
  const findAndToggle = (items: any[]) => {
    items.forEach(item => {
      if (item.id === id) {
        item.expanded = !item.expanded
      } else if (item.children) {
        findAndToggle(item.children)
      } else if (item.timeRanges) {
        item.timeRanges.forEach((timeRange: any) => {
          if (timeRange.id === id) {
            timeRange.expanded = !timeRange.expanded
          } else if (timeRange.children) {
            findAndToggle(timeRange.children)
          }
        })
      }
    })
  }
  findAndToggle(ganttData.value)
}

// 扁平化甘特图数据，用于递归渲染
const flattenedGanttData = computed(() => {
  const flatten = (items: any[], level = 0): any[] => {
    return items.flatMap((item: any) => {
      const newItem = { ...item, level }
      
      // 处理人员类型，遍历timeRanges
      if (item.type === 'person' && item.timeRanges) {
        const personItem = { ...item, level }
        
        const result = [personItem]
        
        // 只有当人员是展开状态时，才添加时间段及其子任务
        if (item.expanded === true) {
          item.timeRanges.forEach((timeRange: any, index: number) => {
            const timeRangeItem = {
              ...timeRange,
              id: timeRange.id || `${item.id}_timerange_${index}`,
              type: 'timerange',
              level: level + 1,
              parentId: item.id
            }
            
            // 添加时间段
            result.push(timeRangeItem)
            
            // 只有当时间段展开时，才添加其子任务
            if (timeRange.children && timeRange.children.length > 0) {
              if (timeRange.expanded === true) {
                result.push(...flatten(timeRange.children, level + 2))
              }
            }
          })
        }
        
        return result
      }
      
      // 处理其他类型
      if (newItem.children && newItem.children.length > 0 && newItem.expanded === true) {
        return [newItem, ...flatten(newItem.children, level + 1)]
      }
      return [newItem]
    })
  }
  return flatten(filteredGanttData.value)
})

// 计算动态高度
const dynamicHeight = computed(() => {
  const minHeight = 500
  const calculatedHeight = flattenedGanttData.value.length * 40
  return Math.max(minHeight, calculatedHeight)
})

// 计算时间轴内容高度
const timelineContentHeight = computed(() => {
  return flattenedGanttData.value.length * 40
})

// 人员筛选
const selectedPersons = ref<string[]>([])
const allPersons = computed(() => ganttData.value.map(p => ({ id: p.id, label: p.label })))
// 任务树筛选
const taskFilter = ref('')

// 组合筛选后的数据
const filteredGanttData = computed(() => {
  // 先按人员筛选
  let data = ganttData.value
  if (selectedPersons.value.length > 0) {
    data = data.filter(person => selectedPersons.value.includes(person.id))
  }
  // 任务树筛选
  if (taskFilter.value.trim()) {
    const filterText = taskFilter.value.trim()
    // 递归过滤任务树
    const filterTree = (items: any[]): any[] => {
      return items.map(item => {
        let match = false
        // 检查人员名
        if ((item.label && item.label.includes(filterText))) match = true
        // 检查一级任务
        if (item.timeRanges) {
          const filteredTimeRanges = item.timeRanges.map((tr: any) => {
            let trMatch = false
            if (tr.label && tr.label.includes(filterText)) trMatch = true
            // 检查二级任务
            if (tr.children) {
              const filteredChildren = tr.children.filter((c: any) => c.label && c.label.includes(filterText))
              if (filteredChildren.length > 0) trMatch = true
              tr.children = filteredChildren
            }
            return trMatch ? tr : null
          }).filter(Boolean)
          if (filteredTimeRanges.length > 0) match = true
          item.timeRanges = filteredTimeRanges
        }
        return match ? item : null
      }).filter(Boolean)
    }
    data = filterTree(data)
  }
  return data
})

// 设置滚动联动
const setupScrollSync = () => {
  // 横向滚动联动：日期栏和时间轴
  if (dateHeaderScroll.value && timelineScroll.value) {
    dateHeaderScroll.value.addEventListener('scroll', (e) => {
      const target = e.target as HTMLElement
      if (target && timelineScroll.value) {
        timelineScroll.value.scrollLeft = target.scrollLeft
      }
    })
    
    timelineScroll.value.addEventListener('scroll', (e) => {
      const target = e.target as HTMLElement
      if (target && dateHeaderScroll.value) {
        dateHeaderScroll.value.scrollLeft = target.scrollLeft
      }
    })
  }
  
  // 竖向滚动联动：人员/任务列和时间轴
  if (taskListScroll.value && timelineScroll.value) {
    taskListScroll.value.addEventListener('scroll', (e) => {
      const target = e.target as HTMLElement
      if (target && timelineScroll.value) {
        // 使用 requestAnimationFrame 确保平滑滚动
        requestAnimationFrame(() => {
          timelineScroll.value!.scrollTop = target.scrollTop
        })
      }
    })
    
    timelineScroll.value.addEventListener('scroll', (e) => {
      const target = e.target as HTMLElement
      if (target && taskListScroll.value) {
        // 使用 requestAnimationFrame 确保平滑滚动
        requestAnimationFrame(() => {
          taskListScroll.value!.scrollTop = target.scrollTop
        })
      }
    })
  }
}

// 时间筛选
const handleDateRangeChange = () => {
  console.log('时间筛选:', dateRange.value)
  // 这里可以添加实际的时间筛选逻辑
  // 例如：更新时间轴显示范围、过滤任务等
}

// 清除时间筛选
const clearDateFilter = () => {
  dateRange.value = null
  console.log('清除时间筛选')
  // 这里可以添加清除筛选后的逻辑
}

// 开始拖拽调整宽度
const startResize = (e: MouseEvent) => {
  isResizing.value = true
  startX.value = e.clientX
  startWidth.value = personnelColumnWidth.value
  
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  
  // 防止文本选择
  e.preventDefault()
}

// 处理拖拽调整宽度
const handleResize = (e: MouseEvent) => {
  if (!isResizing.value) return
  
  const deltaX = e.clientX - startX.value
  const newWidth = Math.max(160, Math.min(256, startWidth.value + deltaX)) // 最小160px，最大256px
  
  personnelColumnWidth.value = newWidth
}

// 停止拖拽调整宽度
const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
}

// 获取时间轴头部样式
const getTimeHeaderClass = (day: any) => {
  if (day.isToday) {
    return 'bg-orange-300 text-blue-700'
  }
  return 'bg-orange-200 text-gray-700'
}

// 获取时间轴内容样式
const getTimeContentClass = (day: any) => {
  if (day.isToday) {
    return 'bg-blue-50'
  }
  return ''
}

// 组件挂载时初始化
onMounted(async () => {
  console.log('甘特图组件已挂载')

  // 设置默认时间范围（今天前7天到后21天）
  const today = new Date()
  const start = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
  const end = new Date(today.getTime() + 21 * 24 * 60 * 60 * 1000)
  const pad = (n: number) => n.toString().padStart(2, '0')
  dateRange.value = [
    `${start.getFullYear()}-${pad(start.getMonth() + 1)}-${pad(start.getDate())}`,
    `${end.getFullYear()}-${pad(end.getMonth() + 1)}-${pad(end.getDate())}`
  ]

  // 等待DOM更新后设置滚动联动
  await nextTick()
  setupScrollSync()
})
  </script> 

<style scoped>
/* 自定义滚动条 */
.overflow-auto::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 隐藏时间轴头部的滚动条 */
.flex-1.min-w-0.overflow-hidden .overflow-auto::-webkit-scrollbar {
  display: none;
}

/* 确保任务条在行中正确显示 */
.absolute {
  position: absolute;
}

/* 任务条样式优化 */
.rounded-md {
  border-radius: 4px;
}

/* 确保时间轴内容区域正确显示 */
.min-w-0 {
  min-width: 0;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

/* 固定布局优化 */
.overflow-hidden {
  overflow: hidden;
}

/* 确保滚动条可见 */
.overflow-auto {
  overflow: auto !important;
}

/* 确保行高一致 */
.border-b {
  box-sizing: border-box;
}

/* 防止行高变化 */
.h-full {
  height: 100% !important;
}

/* 确保滚动时对齐 */
.relative {
  position: relative;
}

/* 防止内容溢出影响布局 */
.flex-1 {
  flex: 1 1 0%;
}

/* 甘特图时间轴边框样式 */
.timeline-container {
  position: relative;
}

.timeline-cell {
  border-right: 1px dashed #6b7280;
}

.timeline-row {
  border-bottom: 1px dashed #6b7280;
}

/* 任务条遮挡边框 */
.task-bar {
  z-index: 10;
  border: 2px solid transparent;
  box-sizing: border-box;
  transform-origin: center;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  margin: 0px;
  max-width: calc(100% - 0px);
  max-height: calc(100% - 0px);
}

.task-bar:hover {
  z-index: 20;
  transform: scale(1.02);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.4);
  filter: brightness(1.05);
  margin: 0px;
}

.task-bar:hover span {
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 任务条颜色样式 */
.task-bar-color {
  /* 基础样式，具体颜色通过内联样式设置 */
}

/* 区域三样式优化 */
.task-list-container {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.task-row {
  position: relative;
  transition: all 0.2s ease-in-out;
}

.task-row:hover {
  transform: translateX(2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.task-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(180deg, #3b82f6 0%, #60a5fa 100%);
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.task-row:hover::before {
  opacity: 1;
}

/* 展开箭头样式 */
.expand-arrow {
  transition: all 0.2s ease-in-out;
  border-radius: 3px;
  cursor: pointer;
}

.expand-arrow:hover {
  transform: scale(1.1);
  background-color: rgba(59, 130, 246, 0.1);
}

.expand-arrow svg {
  transition: transform 0.2s ease-in-out;
}

.expand-arrow:hover svg {
  stroke: #3b82f6;
}

/* 任务类型标识样式 */
.task-type-indicator {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 层级缩进样式 */
.task-row[style*="borderLeft"] {
  position: relative;
}

.task-row[style*="borderLeft"]::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.05) 0%, transparent 100%);
  pointer-events: none;
}

/* 确保边框样式一致 */
.border-gray-100 {
  border-color: #f3f4f6;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

/* 确保hover效果一致 */
.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}
</style>

# MySQL关系保存修改总结

## 修改概述

根据您的要求，将API关系保存从Neo4j改为MySQL数据库的`api_data_model_relations`表，并新增风险等级等字段。

## 主要修改内容

### 1. 数据库表结构修改

#### 新增字段
在`api_data_model_relations`表中新增以下字段：

```sql
-- 风险等级字段
`risk_level` VARCHAR(10) DEFAULT NULL COMMENT '风险等级：低、中、高'

-- 语义相似度字段  
`semantic_score` DECIMAL(5,4) DEFAULT NULL COMMENT '语义相似度分数(0-1)'

-- 动词相似度字段
`verb_score` DECIMAL(5,4) DEFAULT NULL COMMENT '动词相似度分数(0-1)'

-- 综合相似度字段
`final_score` DECIMAL(5,4) DEFAULT NULL COMMENT '综合相似度分数(0-1)'
```

#### 索引优化
```sql
-- 风险等级索引
ADD INDEX `idx_risk_level` (`risk_level`)

-- 综合相似度索引  
ADD INDEX `idx_final_score` (`final_score`)
```

### 2. 数据库模型修改

#### `aimodelapplication/db/mysql/models/api_data_model_relations.py`

**修改的方法:**

1. **`create_relation()`** - 新增参数:
   - `risk_level`: 风险等级
   - `semantic_score`: 语义相似度
   - `verb_score`: 动词相似度  
   - `final_score`: 综合相似度

2. **查询方法更新** - 包含新字段:
   - `get_relations_with_pagination()`
   - `get_relations_by_knowledge_base()`
   - `get_relation_by_id()`

**新增的方法:**

1. **`create_prerequisite_relation()`** - 专门用于创建前置依赖关系:
   ```python
   async def create_prerequisite_relation(
       self,
       knowledge_base_id: str,
       upstream_api: Dict,
       downstream_api: Dict, 
       prerequisite: str,
       semantic_score: float,
       verb_score: float,
       final_score: float,
       risk_level: str,
       task_id: Optional[str] = None
   ) -> int
   ```

2. **`batch_create_prerequisite_relations()`** - 批量创建前置依赖关系:
   ```python
   async def batch_create_prerequisite_relations(
       self,
       knowledge_base_id: str,
       relations: List[Dict],
       task_id: Optional[str] = None
   ) -> Dict[str, int]
   ```

### 3. 主逻辑修改

#### `aimodelapplication/api/v1/knowledge_api/relation_uid/views.py`

**移除的代码:**
- Neo4j关系服务导入和使用
- `aimodelapplication.service.relation_service`相关代码

**新增的代码:**
```python
# 使用现有的MySQL模型保存关系
api_data_model_relations = ApiDataModelRelationsModel()
await api_data_model_relations.initialize()

try:
    # 批量保存关系到MySQL
    save_result = await api_data_model_relations.batch_create_prerequisite_relations(
        knowledge_base_id=relation_uid,  # 使用relation_uid作为knowledge_base_id
        relations=relations_to_save,
        task_id=task_id
    )
    saved_relations_count = save_result.get("success", 0)
    
finally:
    await api_data_model_relations.close()
```

### 4. 数据存储结构

#### 关系数据格式
```python
{
    "upstream_api": {
        "id": "api_001",
        "method": "POST", 
        "path": "/api/projects",
        "summary": "项目创建",
        "name": "createProject"
    },
    "downstream_api": {
        "id": "api_002",
        "method": "POST",
        "path": "/api/accounts", 
        "summary": "账号创建",
        "name": "createAccount"
    },
    "relation_info": {
        "prerequisite": "创建项目",
        "semantic_score": 0.85,
        "verb_score": 0.72,
        "final_score": 0.785,
        "risk_level": "低",
        "prerequisite_verbs": ["创建"],
        "summary_verbs": ["创建"]
    }
}
```

#### MySQL存储字段映射
| 字段名 | 数据类型 | 说明 | 来源 |
|--------|----------|------|------|
| `knowledge_base_id` | VARCHAR(255) | 知识库ID | relation_uid |
| `upstream_api` | JSON | 上游API信息 | rerank匹配的前置API |
| `downstream_api` | JSON | 下游API信息 | 当前分析的API |
| `relation_description` | TEXT | 关系描述 | 自动生成 |
| `risk_level` | VARCHAR(10) | 风险等级 | rerank计算结果 |
| `semantic_score` | DECIMAL(5,4) | 语义相似度 | rerank模型输出 |
| `verb_score` | DECIMAL(5,4) | 动词相似度 | jieba+向量计算 |
| `final_score` | DECIMAL(5,4) | 综合相似度 | 平均值 |
| `task_id` | VARCHAR(50) | 任务ID | 传入参数 |
| `status` | INT | 状态 | 默认0(待确认) |

### 5. 文件清单

#### 修改的文件
1. `aimodelapplication/db/mysql/models/api_data_model_relations.py` - 数据库模型
2. `aimodelapplication/api/v1/knowledge_api/relation_uid/views.py` - 主逻辑

#### 新增的文件
1. `database_migration_add_risk_fields.sql` - 数据库迁移脚本
2. `test_mysql_relation_service.py` - MySQL关系服务测试
3. `MySQL关系保存修改总结.md` - 本文档

#### 删除的文件
1. `aimodelapplication/service/relation_service.py` - Neo4j关系服务（已删除）

### 6. 部署步骤

#### 1. 执行数据库迁移
```bash
# 连接到MySQL数据库
mysql -u username -p database_name

# 执行迁移脚本
source database_migration_add_risk_fields.sql
```

#### 2. 验证表结构
```sql
DESCRIBE api_data_model_relations;
```

#### 3. 测试功能
```bash
python test_mysql_relation_service.py
```

### 7. 优势

#### 相比Neo4j方案
1. **简化架构**: 无需额外的Neo4j数据库
2. **数据一致性**: 所有数据在同一个MySQL数据库中
3. **维护成本**: 减少数据库维护复杂度
4. **查询性能**: 利用现有的MySQL索引和优化

#### 数据完整性
1. **详细记录**: 保存完整的匹配过程数据
2. **可追溯**: 包含语义分数、动词分析等详细信息
3. **风险评估**: 三级风险等级便于后续处理
4. **状态管理**: 支持关系确认流程

### 8. 使用示例

#### 查询高风险关系
```sql
SELECT upstream_api, downstream_api, risk_level, final_score
FROM api_data_model_relations 
WHERE risk_level = '高' 
ORDER BY final_score DESC;
```

#### 查询特定任务的关系
```sql
SELECT * FROM api_data_model_relations 
WHERE task_id = 'your_task_id' 
AND status = 0;
```

#### 统计风险分布
```sql
SELECT risk_level, COUNT(*) as count 
FROM api_data_model_relations 
WHERE knowledge_base_id = 'your_kb_id'
GROUP BY risk_level;
```

### 9. 注意事项

1. **数据迁移**: 执行SQL脚本前请备份数据库
2. **字段长度**: 风险等级字段限制为10个字符
3. **精度设置**: 相似度字段使用DECIMAL(5,4)，支持0.0000-0.9999
4. **索引优化**: 新增索引可能影响写入性能，请根据实际情况调整
5. **连接管理**: 注意在使用后正确关闭数据库连接

这样的修改既满足了您使用MySQL数据库的要求，又保持了rerank+向量化匹配的核心功能，同时简化了整体架构。

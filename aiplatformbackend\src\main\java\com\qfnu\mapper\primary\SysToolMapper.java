package com.qfnu.mapper.primary;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qfnu.model.dto.SysToolDTO;
import com.qfnu.model.param.SysToolParam;
import com.qfnu.model.param.SysToolQueryParam;
import com.qfnu.model.po.SysToolPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qfnu.model.vo.SysToolVO;
import com.qfnu.model.vo.ToolDistributionVO;
import com.qfnu.model.vo.TopRankingsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysToolMapper extends BaseMapper<SysToolPO> {

//    IPage<SysToolPO> getToolsList(Page<SysUserManagementPO> page, SysToolQueryParam sysToolQueryParam);

    IPage<SysToolDTO> getToolsList(Page<SysToolDTO> page, @Param("param") SysToolQueryParam sysToolQueryParam);

    Integer countTotal(Page<SysToolDTO> page,  @Param("param") SysToolQueryParam sysToolQueryParam);

    int toolCount();

    int totalUsage();

    double avgRating();

    /**
     * 查询使用次数最多的前5个工具
     */
    List<TopRankingsVO.ToolRankVO> getHotTools();

    /**
     * 查询评分最高的前5个工具
     */
    List<TopRankingsVO.ToolRankVO> getTopRatedTools();

    /**
     * 查询贡献工具数量最多的前5个用户
     */
    List<TopRankingsVO.ContributorRankVO> getTopContributors();

    /**
     * 查询工具类别分布数据
     */
    List<ToolDistributionVO.CategoryData> getCategoryDistribution();

    /**
     * 查询工具月度趋势数据
     */
    List<ToolDistributionVO.MonthlyTrendData> getMonthlyTrend();

    /**
     * 软删除工具
     */
    boolean myDelete(@Param("param") SysToolParam param);

    /**
     * 根据工具ID查询工具详情
     * @param toolId 工具ID
     * @return 工具信息
     */
    SysToolPO getToolById(@Param("toolId") String toolId);

    /**
     * 根据工具ID更新工具信息
     * @param sysToolPO 工具信息
     * @return 更新结果
     */
    int myUpdateById(SysToolPO sysToolPO);

    /**
     * 查询用户提交的工具列表
     * @param page 分页参数
     * @param param 查询参数
     * @return 工具列表
     */
    IPage<SysToolVO> getMyToolList(Page<SysToolVO> page, @Param("param") SysToolQueryParam param);
}
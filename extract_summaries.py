#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口Summary字段提取工具
从接口文档中提取所有summary字段并保存到文件
"""

import json
import os
from datetime import datetime
from collections import Counter


def load_api_docs_from_json(file_path: str):
    """从JSON文件加载API文档"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"成功加载JSON文件: {file_path}")
        
        # 如果是包装在某个键下的数据，尝试提取
        if isinstance(data, dict):
            print("JSON数据是字典格式，尝试提取API列表...")
            # 尝试常见的键名
            for key in ['apis', 'data', 'paths', 'endpoints', 'api_list']:
                if key in data:
                    print(f"找到键: {key}")
                    if isinstance(data[key], list):
                        print(f"提取到 {len(data[key])} 个API")
                        return data[key]
                    elif isinstance(data[key], dict):
                        # 如果是字典，可能是OpenAPI格式的paths
                        apis = []
                        for path, methods in data[key].items():
                            if isinstance(methods, dict):
                                for method, details in methods.items():
                                    if method.lower() in ['get', 'post', 'put', 'delete', 'patch']:
                                        api_item = {
                                            "method": method.upper(),
                                            "path": path,
                                            "summary": details.get('summary', ''),
                                            "description": details.get('description', ''),
                                            "tags": details.get('tags', [])
                                        }
                                        apis.append(api_item)
                        if apis:
                            print(f"从OpenAPI格式提取到 {len(apis)} 个API")
                            return apis
            
            # 如果没有找到标准键，打印所有键供参考
            print(f"未找到标准API键，可用的键有: {list(data.keys())}")
            return []
            
        elif isinstance(data, list):
            print(f"JSON数据是列表格式，包含 {len(data)} 个项目")
            return data
        else:
            print("JSON数据格式不支持")
            return []
            
    except FileNotFoundError:
        print(f"文件不存在: {file_path}")
        return []
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {str(e)}")
        return []
    except Exception as e:
        print(f"加载API文档失败: {str(e)}")
        return []


def extract_api_summaries(input_file: str = None, output_file: str = "api_summaries.txt"):
    """
    提取接口文档中所有的summary字段并保存到文件
    
    Args:
        input_file: 输入的JSON文件路径，如果为None则自动查找
        output_file: 输出文件名，默认为api_summaries.txt
    
    Returns:
        list: 提取的summary信息列表
    """
    print("=== API Summary字段提取工具 ===")
    print(f"输出文件: {output_file}")
    print()
    
    # 确定输入文件
    if input_file and os.path.exists(input_file):
        api_docs = load_api_docs_from_json(input_file)
        loaded_file = input_file
    else:
        # 自动查找API文档文件
        possible_paths = [
            "api_docs.json",
            "apis.json", 
            "swagger.json",
            "openapi.json",
            "api_document.json"
        ]
        
        api_docs = []
        loaded_file = None
        
        for file_path in possible_paths:
            if os.path.exists(file_path):
                print(f"尝试加载: {file_path}")
                api_docs = load_api_docs_from_json(file_path)
                if api_docs:
                    loaded_file = file_path
                    break
    
    if not api_docs:
        print("✗ 未找到有效的API文档文件")
        return []
    
    print(f"✓ 成功从 {loaded_file} 加载 {len(api_docs)} 个API")
    print()
    
    # 提取所有summary字段
    summaries = []
    summary_counter = Counter()
    method_counter = Counter()
    keyword_counter = Counter()
    
    for i, api in enumerate(api_docs, 1):
        method = api.get('method', 'N/A').upper()
        path = api.get('path', 'N/A')
        summary = api.get('summary', '').strip()
        description = api.get('description', '').strip()
        tags = api.get('tags', [])
        
        if summary:
            # 收集summary信息
            summary_info = {
                'index': i,
                'method': method,
                'path': path,
                'summary': summary,
                'description': description,
                'tags': tags
            }
            summaries.append(summary_info)
            
            # 统计
            summary_counter[summary] += 1
            method_counter[method] += 1
            
            # 统计关键词
            keywords = ['创建', '查询', '更新', '删除', '列表', '获取', '批量', '关联', '添加', '移除', '修改', '导入', '导出', '上传', '下载']
            for keyword in keywords:
                if keyword in summary:
                    keyword_counter[keyword] += 1
    
    # 保存到文件
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# API接口Summary字段提取结果\n")
        f.write(f"# 数据源: {loaded_file}\n")
        f.write(f"# 提取时间: {current_time}\n")
        f.write(f"# 总API数量: {len(api_docs)}\n")
        f.write(f"# 有效Summary数量: {len(summaries)}\n\n")
        
        # 写入HTTP方法统计
        f.write("## HTTP方法统计\n")
        for method, count in method_counter.most_common():
            f.write(f"- {method}: {count} 个\n")
        f.write("\n")
        
        # 写入关键词统计
        f.write("## 关键词统计\n")
        for keyword, count in keyword_counter.most_common():
            f.write(f"- {keyword}: {count} 个\n")
        f.write("\n")
        
        # 写入重复Summary统计
        duplicate_summaries = {k: v for k, v in summary_counter.items() if v > 1}
        if duplicate_summaries:
            f.write("## 重复Summary统计\n")
            for summary, count in sorted(duplicate_summaries.items(), key=lambda x: x[1], reverse=True):
                f.write(f"- \"{summary}\": {count} 次\n")
            f.write("\n")
        
        # 写入完整API信息
        f.write("## 完整API信息列表\n\n")
        for summary_info in summaries:
            f.write(f"{summary_info['index']:3d}. [{summary_info['method']}] {summary_info['summary']}\n")
            f.write(f"     路径: {summary_info['path']}\n")
            if summary_info['description']:
                f.write(f"     描述: {summary_info['description']}\n")
            if summary_info['tags']:
                f.write(f"     标签: {', '.join(summary_info['tags'])}\n")
            f.write("\n")
        
        # 写入纯summary文本（便于其他用途）
        f.write("\n## 纯Summary文本列表\n")
        f.write("# 每行一个Summary，便于复制使用\n\n")
        for summary_info in summaries:
            f.write(f"{summary_info['summary']}\n")
        
        # 写入JSON格式数据
        f.write("\n## JSON格式数据\n")
        f.write("# 便于程序处理\n\n")
        f.write("```json\n")
        json_data = {
            "source_file": loaded_file,
            "extract_time": current_time,
            "total_apis": len(api_docs),
            "valid_summaries": len(summaries),
            "summaries": [
                {
                    "method": info['method'],
                    "path": info['path'],
                    "summary": info['summary']
                }
                for info in summaries
            ]
        }
        f.write(json.dumps(json_data, ensure_ascii=False, indent=2))
        f.write("\n```\n")
    
    print(f"✓ 成功提取 {len(summaries)} 个Summary字段")
    print(f"✓ 结果已保存到: {output_file}")
    print()
    
    # 显示统计信息
    print("HTTP方法统计:")
    for method, count in method_counter.most_common():
        print(f"  {method}: {count} 个")
    
    print("\n关键词统计:")
    for keyword, count in keyword_counter.most_common():
        print(f"  {keyword}: {count} 个")
    
    if duplicate_summaries:
        print(f"\n发现 {len(duplicate_summaries)} 个重复的Summary:")
        for summary, count in list(sorted(duplicate_summaries.items(), key=lambda x: x[1], reverse=True))[:5]:
            print(f"  \"{summary}\": {count} 次")
    
    # 显示前10个summary
    print(f"\n前10个Summary:")
    for i, summary_info in enumerate(summaries[:10], 1):
        print(f"  {i}. [{summary_info['method']}] {summary_info['summary']}")
    
    if len(summaries) > 10:
        print(f"  ... 还有 {len(summaries) - 10} 个")
    
    return summaries


def main():
    """主函数"""
    import sys
    
    # 支持命令行参数
    input_file = None
    output_file = "api_summaries.txt"
    
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    # 执行提取
    summaries = extract_api_summaries(input_file, output_file)
    
    if summaries:
        print(f"\n✅ 提取完成！共处理 {len(summaries)} 个API接口")
        print(f"📄 结果文件: {output_file}")
    else:
        print("\n❌ 提取失败，请检查输入文件")


if __name__ == "__main__":
    main()

package com.qfnu.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qfnu.common.ResponseVO;
import com.qfnu.common.annotation.RequiredPermission;
import com.qfnu.model.po.SysFeaturePO;
import com.qfnu.service.SysFeatureService;
import com.qfnu.model.vo.SysFeatureVO;
import com.qfnu.model.param.SysFeatureParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(tags = "SysFeature接口")
@RestController
@RequestMapping("/api/SysFeature")
public class SysFeatureController {
    
    @Resource
    private SysFeatureService sysFeatureService;


    @RequiredPermission({"super,admin"})
    @ApiOperation("新增")
    @PostMapping("/save")
    public ResponseVO<Integer> save(@RequestBody SysFeatureParam param,HttpServletRequest request) {
        return ResponseVO.success(sysFeatureService.save(param,request),200);
    }
//
//    @ApiOperation("批量新增")
//    @PostMapping("/saveBatch")
//    public ResponseVO<Integer> saveBatch(@RequestBody List<SysFeatureParam> paramList) {
//        return ResponseVO.success(sysFeatureService.saveBatch(paramList));
//    }
//
    @RequiredPermission({"super,admin"})
    @ApiOperation("更新")
    @PostMapping("/update")
    public ResponseVO<Integer> update(@RequestBody SysFeatureParam param, HttpServletRequest request) {
        return ResponseVO.success(sysFeatureService.myUpdate(param,request),200);
    }

    @RequiredPermission({"super,admin"})
    @ApiOperation("删除")
    @PostMapping("/delete")
    public ResponseVO<Integer> delete(@RequestBody SysFeatureParam param) {
        return ResponseVO.success(sysFeatureService.myDelete(param),200);
    }
//
//    @ApiOperation("根据ID查询")
//    @PostMapping("/getById")
//    public ResponseVO<SysFeatureVO> getById(@RequestBody SysFeatureParam param) {
//        return ResponseVO.success(sysFeatureService.getById(param.getFeatureId()));
//    }
//
    @ApiOperation("列表查询")
    @PostMapping("/list")
    public ResponseVO<Page<SysFeatureVO>> getList(@RequestBody SysFeatureParam param) {
        Page<SysFeatureVO> list = sysFeatureService.getList(param);
        return ResponseVO.success(list,200);
    }


    @ApiOperation("获取目录树")
    @PostMapping("/feature_tree")
    public ResponseVO<List<SysFeaturePO>> getTree(@RequestBody SysFeatureParam param) {
        return ResponseVO.success(sysFeatureService.getTree(param),200);
    }

//
//    @ApiOperation("批量新增或更新")
//    @PostMapping("/batchAddOrUpdate")
//    public ResponseVO<List<SysFeatureVO>> batchAddOrUpdate(@RequestBody List<SysFeatureParam> paramList) {
//        return ResponseVO.success(sysFeatureService.batchAddOrUpdate(paramList));
//    }





} 
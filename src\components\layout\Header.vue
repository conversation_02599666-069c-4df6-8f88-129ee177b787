<template>
  <div>
    <!-- 顶部导航 -->
    <nav class="fixed top-0 left-0 right-0 h-[60px] bg-white shadow-md z-50">
      <div class="max-w-[1440px] mx-auto px-6 h-full flex items-center justify-between">
        <div class="flex items-center gap-8">
          <img :src="logoUrl" alt="Logo" class="w-[40px] h-[40px]" />
          <div class="flex gap-8">
            <a v-for="(item, index) in navItems" 
               :key="index" 
               @click="handleNavClick(index)" 
               :class="['text-gray-600 hover:text-blue-600 cursor-pointer whitespace-nowrap relative',
                 item.active ? 'text-blue-600 font-medium after:content-[\'\'] after:absolute after:bottom-[-18px] after:left-0 after:w-full after:h-[2px] after:bg-blue-600 after:transition-all after:duration-300' : '']">
              {{ item.name }}
              <el-loading v-if="item.active && loading" class="ml-1" :size="14" />
            </a>
          </div>
        </div>
        <div class="flex items-center gap-3">
          <a-dropdown :trigger="['hover']" placement="bottomRight">
            <div class="flex items-center gap-2 cursor-pointer">
              <img :src="avatarUrl" alt="用户头像" class="w-[36px] h-[36px] rounded-full" />
              <span class="text-gray-700">{{ username }}</span>
            </div>
            <template #overlay>
              <a-menu @click="handleCommand">
                <a-menu-item key="publish">
                  <div class="flex items-center">
                    <span class="i-mdi-plus mr-2"></span>
                    <span>发布工具</span>
                  </div>
                </a-menu-item>
                <a-menu-item key="submission">
                  <div class="flex items-center">
                    <span class="i-mdi-history mr-2"></span>
                    <span>提交记录</span>
                  </div>
                </a-menu-item>

                <a-menu-item key="logout">
                  <div class="flex items-center">
                    <span class="i-mdi-logout mr-2"></span>
                    <span>退出登录</span>
                  </div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </nav>
    <SubmissionHistory v-model:visible="showSubmissionHistoryModal" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import SubmissionHistory from '../SubmissionHistory.vue';
import { defineEmits, defineProps } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { getRandomAvatar } from '@/utils/getRandomAvatar';

const showSubmissionHistoryModal = ref(false);

const emit = defineEmits(['nav-click', 'publish-tool']);

defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  currentView: {
    type: String,
    default: 'home'
  }
});

const logoUrl = 'https://public.readdy.ai/ai/img_res/9b098a8f99b92d3df0d6b795fe8f12c4.jpg';
const avatarUrl = getRandomAvatar();

const router = useRouter();
const route = useRoute();

const navItems = ref([
  { name: '基础安全产品线', active: false, path: '/' },
  { name: '首页', active: true, path: '/' },
  { name: 'AI 提效', active: false, path: '/ai-efficiency' },
  { name: '测试工具', active: false, path: '/test-tools' },
  { name: '系统配置', active: false, path: '/system' }
]);

// 根据当前路由更新导航项的激活状态
const updateActiveNav = () => {
  const currentPath = route.path;
  navItems.value.forEach((item) => {
    if (currentPath === '/') {
      // 在首页时只激活首页导航项
      item.active = item.name === '首页';
    } else {
      // 其他情况下，检查路径是否匹配或者是否是子路径
      item.active = currentPath.startsWith(item.path) && item.path !== '/';
    }
  });
};

// 监听路由变化
watch(() => route.path, () => {
  updateActiveNav();
});

// 组件挂载时初始化激活状态
onMounted(() => {
  updateActiveNav();
});

// 添加用户名响应式变量
const username = ref('');

// 在组件挂载时获取用户信息
onMounted(() => {
  updateActiveNav();
  // 从localStorage获取用户信息
  const userInfo = localStorage.getItem('userInfo');
  if (userInfo) {
    try {
      const parsedUserInfo = JSON.parse(userInfo);
      username.value = parsedUserInfo.username || '未知用户';
    } catch (e) {
      console.error('解析用户信息失败:', e);
      username.value = '未知用户';
    }
  } else {
    username.value = '未知用户';
  }
});

const handleNavClick = (index) => {
  const targetPath = navItems.value[index].path;
  router.push(targetPath);
  emit('nav-click', index);
};

const handleCommand = ({ key }: { key: string }) => {
  if (key === 'submission') {
    showSubmissionHistoryModal.value = true;
  } else if (key === 'publish') {
    emit('publish-tool');
  } else if (key === 'logout') {
    console.log('退出登录，使用TAM官方登出接口');

    // 获取当前用户的token（在清除之前）
    const token = localStorage.getItem('token');

    // 清除所有本地认证相关数据
    localStorage.removeItem('token');
    localStorage.removeItem('userInfo');
    localStorage.removeItem('oauth_auth_state');
    localStorage.removeItem('oauth_callback_pending');
    localStorage.removeItem('oauth_callback_code');
    localStorage.removeItem('oauth_callback_state');
    localStorage.removeItem('oauth_callback_error');
    localStorage.removeItem('oauth_callback_error_description');
    localStorage.removeItem('userId');
    localStorage.removeItem('realName');
    localStorage.removeItem('userType');
    localStorage.removeItem('username');
    localStorage.removeItem('role');
    localStorage.removeItem('lastLogin');
    localStorage.removeItem('product');

    // 根据TAM文档，使用官方的登出接口
    // https://sso.das-security.cn/iam/auth/login/logout?redirectUrl=xx&token=xx
    const redirectUrl = window.location.origin + '/';
    const logoutUrl = 'https://sso.das-security.cn/iam/auth/login/logout';

    // 构建完整的登出URL
    let fullLogoutUrl = `${logoutUrl}?redirectUrl=${encodeURIComponent(redirectUrl)}`;
    if (token) {
      fullLogoutUrl += `&token=${encodeURIComponent(token)}`;
    }

    console.log('跳转到TAM登出页面:', fullLogoutUrl);

    // 跳转到TAM的官方登出接口
    window.location.href = fullLogoutUrl;
  }
};
</script>

<style scoped>
.ant-dropdown {
  margin-top: 8px !important;
}

.ant-dropdown-menu {
  padding: 4px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.ant-dropdown-menu-item {
  padding: 8px 16px;
  transition: all 0.2s ease;
}

.ant-dropdown-menu-item:hover {
  background-color: #f0f7ff;
  color: #1890ff;
}

.header-dropdown {
  transition: all 0.3s ease;
  margin-top: 8px !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 9999 !important;
  transform-origin: top center;
}

.header-dropdown .el-dropdown-menu__item {
  padding: 8px 16px;
  transition: all 0.2s ease;
}

.header-dropdown .el-dropdown-menu__item:hover {
  background-color: #f0f7ff;
  color: #409eff;
}
</style>
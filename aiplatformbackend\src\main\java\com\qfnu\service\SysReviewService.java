package com.qfnu.service;

import com.qfnu.mapper.primary.SysToolMapper;
import com.qfnu.mapper.primary.SysUserManagementMapper;
import com.qfnu.model.po.SysToolPO;
import com.qfnu.model.po.SysUserManagementPO;
import com.qfnu.model.vo.SysUserManagementVO;
import lombok.extern.slf4j.Slf4j;
import com.qfnu.mapper.primary.SysReviewMapper;
import com.qfnu.model.vo.SysReviewVO;
import com.qfnu.model.dto.SysReviewDTO;
import com.qfnu.model.param.SysReviewParam;
import com.qfnu.model.po.SysReviewPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class SysReviewService extends ServiceImpl<SysReviewMapper, SysReviewPO> {

    @Resource
    private SysReviewMapper sysReviewMapper;


    @Autowired
    private SysToolMapper sysToolMapper;


    @Autowired
    private SysUserManagementMapper sysUserManagementMapper;
    
    @Resource
    private com.qfnu.converter.SysReviewMapper reviewConverter;
    
    @Transactional(rollbackFor = Exception.class)
    public int save(SysReviewParam param) {
        param.setReviewId(UUID.randomUUID().toString());
        SysReviewPO po = reviewConverter.toSysReviewPO(param);
        return save(po) ? 1 : 0;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public int saveBatch(List<SysReviewParam> paramList) {
        List<SysReviewPO> poList = reviewConverter.toSysReviewPOList(paramList);
        return saveBatch(poList) ? poList.size() : 0;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public int update(SysReviewParam param) {
        SysReviewPO po = reviewConverter.toSysReviewPO(param);
        return updateById(po) ? 1 : 0;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public int delete(String id) {
        return removeById(id) ? 1 : 0;
    }
    
    public SysReviewVO getById(String id) {
        SysReviewPO po = super.getById(id);
        if (po == null) {
            return null;
        }
        return reviewConverter.toSysReviewVO(po);
    }


    /**
     * 分页查询审核记录
     * @param param 查询参数
     * @return 分页结果
     */
    @Transactional(rollbackFor = Exception.class)
    public IPage<SysReviewVO> getPage(SysReviewParam param) {
        // 创建分页对象
        Page<SysReviewDTO> page = new Page<>(param.getPageNum() != null ? param.getPageNum() : 1,
                param.getPageSize() != null ? param.getPageSize() : 10);

        // 添加日志输出查询参数
        log.info("分页查询参数: pageNum={}, pageSize={}, approvalStatus={}, relatedId={}, reviewer={}, submissionTime={}",
                page.getCurrent(), page.getSize(), param.getApprovalStatus(),
                param.getRelatedId(), param.getReviewer(), param.getSubmissionTime());

        // 执行分页查询
        IPage<SysReviewDTO> dtoPage = sysReviewMapper.getPageList(page, param);

        Integer count = sysReviewMapper.countTotal(page,param);

        // 添加日志输出查询结果
        log.info("分页查询结果: total={}, current={}, size={}, records.size={}",
                dtoPage.getTotal(), dtoPage.getCurrent(), dtoPage.getSize(),
                dtoPage.getRecords() != null ? dtoPage.getRecords().size() : 0);

        // 转换为VO对象
        Page<SysReviewVO> voPage = new Page<>();
        voPage.setTotal(count);
        voPage.setCurrent(dtoPage.getCurrent());
        voPage.setSize(dtoPage.getSize());
        voPage.setRecords(reviewConverter.toSysReviewVOListFromDTO(dtoPage.getRecords()));

        return voPage;
    }

    @Transactional(rollbackFor = Exception.class)
    public int handleApprovalDecision(SysReviewParam param, HttpServletRequest request) {
        //设置审核人
        String user_id = (String) request.getAttribute("id");
        SysUserManagementPO managementPO = sysUserManagementMapper.selectById(user_id);
        param.setReviewer(managementPO.getRealName());
        //查询出工具
        SysReviewPO po = sysReviewMapper.selectById(param.getReviewId());
        //设置审核时间
        Calendar calendar = Calendar.getInstance();
        Date date = calendar.getTime();
        param.setApprovalTime(date);

        SysToolPO sysToolPO = new SysToolPO();
        sysToolPO.setToolId(po.getRelatedId());

        //审核通过
        if (param.getApprovalStatus() == 1) {
            //对审核表进行更改
            sysReviewMapper.updateById(reviewConverter.toSysReviewPO(param));
            //对工具表进行更改
            sysToolPO.setIsPublished("1");
            sysToolPO.setReviewStatus("1");
            sysToolMapper.updateById(sysToolPO);
            return 1;
       }else if (param.getApprovalStatus() == 2) {
            //审核未通过
            sysReviewMapper.updateById(reviewConverter.toSysReviewPO(param));
            sysToolPO.setIsPublished("0");
            sysToolPO.setReviewStatus("0");
            sysToolMapper.updateById(sysToolPO);
            return 0;
        }else{
            log.info("审核数据异常");
            return 0;
        }
    }
}
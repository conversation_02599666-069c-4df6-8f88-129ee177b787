import os
from loguru import logger
from config import ROOT_PATH
import sys

# 清除所有已存在的处理器
logger.remove()

# 日志配置
ROTATION = "1 day"
RETENTION = "7 days"
FORMAT = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
LEVEL = "INFO"

# 添加控制台输出
logger.add(
    sink=sys.stderr,
    format=FORMAT,
    level=LEVEL
)

def logging():
    """获取普通日志处理器"""
    
    # 添加处理器
    logger.add(
        sink='./logs/app_{time:YYYY-MM-DD}.log',
        rotation=ROTATION,
        retention=RETENTION,
        format=FORMAT,
        level=LEVEL,
        enqueue=True,
        filter=lambda record: not record["extra"].get("connection_log", False)  # 过滤掉连接日志
    )
    
    return logger

def connection_logging():
    """获取连接日志处理器"""

    # 为连接日志添加标记
    connection_logger = logger.bind(connection_log=True)
    
    # 添加处理器
    logger.add(
        sink='./logs/connection_{time:YYYY-MM-DD}.log',
        rotation=ROTATION,
        retention=RETENTION,
        format=FORMAT,
        level=LEVEL,
        enqueue=True,
        filter=lambda record: record["extra"].get("connection_log", False)  # 只包含连接日志
    )
    
    return connection_logger



package com.qfnu.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("sys_user_management")
@ApiModel(value = "SysUserManagementPO", description = "SysUserManagement实体类")
public class SysUserManagementPO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 管理记录唯一标识，自增主键
     */
    @TableField(value = "management_id")
    @ApiModelProperty(value = "管理记录唯一标识，自增主键")
    private String managementId;

    /**
     * 关联的用户 ID，与 sys_users 表中的 user_id 关联
     */
    @TableField(value = "user_id")
    @ApiModelProperty(value = "关联的用户 ID，与 sys_users 表中的 user_id 关联")
    private String userId;

    /**
     * 关联的用户名，与 sys_users 表中的 username 关联
     */
    @TableField(value = "username")
    @ApiModelProperty(value = "关联的用户名，与 sys_users 表中的 username 关联")
    private String username;

    /**
     * 用户账户名
     */
    @TableField(value = "account_name")
    @ApiModelProperty(value = "用户账户名")
    private String accountName;

    /**
     * 用户真实姓名
     */
    @TableField(value = "real_name")
    @ApiModelProperty(value = "用户真实姓名")
    private String realName;

    /**
     * 用户类型
     */
    @TableField(value = "user_type")
    @ApiModelProperty(value = "用户类型")
    private String userType;

    /**
     * 权限，super:超级管理员 admin 管理员 common 普通用户
     */
    @TableField(value = "role")
    @ApiModelProperty(value = "权限，super:超级管理员 admin 管理员 common 普通用户")
    private String role;

    /**
     * 用户关联的产品信息
     */
    @TableField(value = "product")
    @ApiModelProperty(value = "用户关联的产品信息")
    private String product;

    /**
     * 工号（来自sys_users表）
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "工号")
    private String jobNo;


}
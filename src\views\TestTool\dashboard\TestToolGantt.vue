<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 内容区域 -->
    <div class="p-6 pt-4">
      <div class="bg-white rounded-lg shadow-sm border">
        <GanttPersonnel />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import GanttPersonnel from './components/GanttPersonnel.vue'
import GanttProject from './components/GanttProject.vue'

// 当前激活的标签页
const activeTab = ref<'personnel' | 'project'>('personnel')
</script> 
package com.qfnu.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("sys_tool")
@ApiModel(value = "SysToolPO", description = "SysTool实体类")
public class SysToolPO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 工具ID（主键）
     */
    @TableField(value = "tool_id")
    @ApiModelProperty(value = "工具ID（主键）")
    private String toolId;

    /**
     * 工具名称
     */
    @TableField(value = "tool_name")
    @ApiModelProperty(value = "工具名称")
    private String toolName;

    /**
     * 链接地址
     */
    @TableField(value = "link_address")
    @ApiModelProperty(value = "链接地址")
    private String linkAddress;

    /**
     * 功能介绍
     */
    @TableField(value = "function_description")
    @ApiModelProperty(value = "功能介绍")
    private String functionDescription;

    /**
     * 类别
     */
    @TableField(value = "category")
    @ApiModelProperty(value = "类别")
    private String category;

    /**
     * 标签
     */
    @TableField(value = "tags")
    @ApiModelProperty(value = "标签")
    private String tags;

    /**
     * 创建人（多人，JSON数组存储）
     */
    @TableField(value = "creators")
    @ApiModelProperty(value = "创建人（多人，JSON数组存储）")
    private String creators;

    /**
     * 关联用户ID（多个，JSON数组存储）
     */
    @TableField(value = "user_ids")
    @ApiModelProperty(value = "关联用户ID（多个，JSON数组存储）")
    private String userIds;

    /**
     * 评分（范围0.00-5.00）
     */
    @TableField(value = "rating")
    @ApiModelProperty(value = "评分（范围0.00-5.00）")
    private BigDecimal rating;

    /**
     * 使用次数
     */
    @TableField(value = "usage_count")
    @ApiModelProperty(value = "使用次数")
    private Long usageCount;

    /**
     * 审核状态（0=未通过, 1=通过, 2=待审核）
     */
    @TableField(value = "review_status")
    @ApiModelProperty(value = "审核状态（0=未通过, 1=通过, 2=待审核）")
    private String reviewStatus;

    /**
     * 是否上架（1=上架, 0=下架）
     */
    @TableField(value = "is_published")
    @ApiModelProperty(value = "是否上架（1=上架, 0=下架）")
    private String isPublished;

    /**
     * 是否删除（1=删除, 0=未删除）
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "是否删除（1=删除, 0=未删除）")
    private String isDeleted;

    /**
     * 删除时间
     */
    @TableField(value = "delete_time")
    @ApiModelProperty(value = "删除时间")
    private Date deleteTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 更新者
     */
    @ApiModelProperty("更新者")
    private String updateBy;




}
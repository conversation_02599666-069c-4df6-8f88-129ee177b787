<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qfnu.mapper.primary.SysToolMapper">

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_tool
        WHERE tool_id = #{id}
        AND is_deleted = '0'
    </select>

    <resultMap id="BaseResultMap" type="com.qfnu.model.po.SysToolPO">
                <id column="tool_id" property="toolId"/>
        <result column="tool_name" property="toolName"/>
        <result column="link_address" property="linkAddress"/>
        <result column="function_description" property="functionDescription"/>
        <result column="create_time" property="createTime"/>
        <result column="category" property="category"/>
        <result column="tags" property="tags"/>
        <result column="creators" property="creators"/>
        <result column="user_ids" property="userIds"/>
        <result column="rating" property="rating"/>
        <result column="usage_count" property="usageCount"/>
        <result column="review_status" property="reviewStatus"/>
        <result column="is_published" property="isPublished"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="delete_time" property="deleteTime"/>

    </resultMap>

    <sql id="Base_Column_List">
        tool_id, tool_name, link_address, function_description, create_time, category, tags, creators, user_ids, rating, usage_count, review_status, is_published, is_deleted, delete_time
    </sql>

    <select id="getToolsList" resultType="com.qfnu.model.dto.SysToolDTO">
        SELECT SQL_CALC_FOUND_ROWS
            <include refid="Base_Column_List"/>
        FROM sys_tool
        <where>
            <if test="param.toolId != null and param.toolId != ''">
                AND tool_id = #{param.toolId}
            </if>
            <if test="param.toolName != null and param.toolName != ''">
                AND tool_name LIKE CONCAT('%', #{param.toolName}, '%')
            </if>
            <if test="param.reviewStatus != null and param.reviewStatus != ''">
                AND review_status = #{param.reviewStatus}
            </if>
            <if test="param.isPublished != null and param.isPublished != ''">
                AND is_published = #{param.isPublished}
            </if>
            <if test="param.category != null and param.category != ''">
                AND category = #{param.category}
            </if>
            <if test="param.tags != null and param.tags != ''">
                AND tags LIKE CONCAT('%', #{param.tags}, '%')
            </if>
            <if test="param.creators != null">
                AND creators = #{param.creators}
            </if>
            <if test="param.createTimeStart != null and param.createTimeStart != ''">
                AND create_time &gt;= #{param.createTimeStart}
            </if>
            <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
                AND create_time &lt;= #{param.createTimeEnd}
            </if>
            AND is_deleted = '0' AND review_status = '1' AND is_published = '1'
        </where>
        <if test="param.sortField != null and param.sortField != ''">
            ORDER BY ${param.sortField} 
            <if test="param.sortOrder != null and param.sortOrder != ''">
                ${param.sortOrder}
            </if>
        </if>
        <if test="param.sortField == null or param.sortField == ''">
            ORDER BY create_time DESC
        </if>
        <if test="param.pageSize != null and param.offset != null">
            LIMIT #{param.pageSize} OFFSET #{param.offset}
        </if>
    </select>
    <select id="countTotal" resultType="java.lang.Integer">
        SELECT FOUND_ROWS()
    </select>

    <select id="toolCount" resultType="int">
        select count(*) from sys_tool
    </select>

    <select id="totalUsage" resultType="int">
        select sum(usage_count) from sys_tool
    </select>

    <select id="avgRating" resultType="double">
        SELECT
            sum( rating )/ count(*)
        FROM
            sys_tool
    </select>

    <select id="getHotTools" resultType="com.qfnu.model.vo.TopRankingsVO$ToolRankVO">
        SELECT 
            t.tool_id as toolId,
            t.tool_name as toolName,
            t.tags as category,
            t.usage_count as usageCount,
            t.rating as rating
        FROM sys_tool t
        ORDER BY t.usage_count DESC
        LIMIT 5
    </select>

    <select id="getTopRatedTools" resultType="com.qfnu.model.vo.TopRankingsVO$ToolRankVO">
        SELECT 
            t.tool_id as toolId,
            t.tool_name as toolName,
            t.category as category,
            t.usage_count as usageCount,
            t.rating as rating
        FROM sys_tool t
        ORDER BY t.rating DESC
        LIMIT 5
    </select>

    <select id="getTopContributors" resultType="com.qfnu.model.vo.TopRankingsVO$ContributorRankVO">
        SELECT
            u.user_id as userId,
            u.real_name as userName,
            u.user_type as groupName,
            COUNT(t.tool_id) as aiToolCount
        FROM sys_user_management u
                 LEFT JOIN sys_tool t
                           ON CONVERT(t.user_ids USING utf8mb4) COLLATE utf8mb4_unicode_ci = CONVERT(u.user_id USING utf8mb4) COLLATE utf8mb4_unicode_ci
        WHERE t.is_deleted = 0
        GROUP BY u.user_id, u.real_name, u.user_type
        ORDER BY aiToolCount DESC
            LIMIT 5;
    </select>

    <select id="getCategoryDistribution" resultType="com.qfnu.model.vo.ToolDistributionVO$CategoryData">
        SELECT
            category as name,
            COUNT(*) as value
        FROM sys_tool
        WHERE is_deleted = 0
        GROUP BY category
        ORDER BY value DESC
    </select>

    <select id="getMonthlyTrend" resultType="com.qfnu.model.vo.ToolDistributionVO$MonthlyTrendData">
        SELECT
            DATE_FORMAT(create_time, '%Y-%m') as month,
            COUNT(*) as count
        FROM sys_tool
        WHERE is_deleted = 0
        GROUP BY DATE_FORMAT(create_time, '%Y-%m')
        ORDER BY month ASC
    </select>
    <select id="getToolById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_tool
        WHERE tool_id = #{toolId}
        AND is_deleted = '0'
    </select>

    <update id="myDelete" parameterType="com.qfnu.model.param.SysToolParam">
        UPDATE sys_tool
        SET is_deleted = #{param.isDeleted},
            delete_time = NOW()
        WHERE tool_id = #{param.toolId}
    </update>

    <update id="myUpdateById" parameterType="com.qfnu.model.po.SysToolPO">
        UPDATE sys_tool
        <set>
            <if test="toolName != null">tool_name = #{toolName},</if>
            <if test="linkAddress != null">link_address = #{linkAddress},</if>
            <if test="functionDescription != null">function_description = #{functionDescription},</if>
            <if test="category != null">category = #{category},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="creators != null">creators = #{creators},</if>
            <if test="userIds != null">user_ids = #{userIds},</if>
            <if test="rating != null">rating = #{rating},</if>
            <if test="usageCount != null">usage_count = #{usageCount},</if>
            <if test="reviewStatus != null">review_status = #{reviewStatus},</if>
            <if test="isPublished != null">is_published = #{isPublished},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime}</if>
        </set>
        WHERE tool_id = #{toolId}
    </update>
    <select id="getMyToolList" resultType="com.qfnu.model.vo.SysToolVO">
        SELECT 
            t.tool_name,
            t.category,
            t.tags,
            r.submission_time,
            t.review_status
        FROM sys_tool t
        LEFT JOIN sys_review r ON t.tool_id = r.related_id
        <where>
            <if test="param.creators != null">
                AND t.user_ids = #{param.creators}
            </if>
            AND t.is_deleted = '0'
        </where>
        ORDER BY r.submission_time DESC
        <if test="param.pageSize != null and param.offset != null">
            LIMIT #{param.pageSize} OFFSET #{param.offset}
        </if>
    </select>
</mapper>
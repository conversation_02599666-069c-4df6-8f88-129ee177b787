package com.qfnu.converter;

import com.qfnu.model.dto.SysFeatureDTO;
import com.qfnu.model.param.SysFeatureParam;
import com.qfnu.model.po.SysFeaturePO;
import com.qfnu.model.vo.SysFeatureVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SysFeatureConverter {
    SysFeatureConverter INSTANCE = Mappers.getMapper(SysFeatureConverter.class);

    @Mappings({
        @Mapping(target = "featureId", source = "featureId"),
        @Mapping(target = "description", source = "description"),
        @Mapping(target = "creator", source = "creator"),
        @Mapping(target = "modifier", source = "modifier")
    })
    SysFeaturePO paramToPo(SysFeatureParam param);
    
    List<SysFeaturePO> paramToPoList(List<SysFeatureParam> paramList);

    @Mappings({
        @Mapping(target = "featureId", source = "featureId"),
        @Mapping(target = "description", source = "description"),
        @Mapping(target = "creator", source = "creator"),
        @Mapping(target = "modifier", source = "modifier")
    })
    SysFeatureDTO poToDto(SysFeaturePO po);

    @Mappings({
        @Mapping(target = "featureId", source = "featureId"),
        @Mapping(target = "description", source = "description"),
        @Mapping(target = "creator", source = "creator"),
        @Mapping(target = "modifier", source = "modifier")
    })
    SysFeatureVO poToVo(SysFeaturePO po);
    
    List<SysFeatureVO> poToVoList(List<SysFeaturePO> poList);

    @Mappings({
        @Mapping(target = "featureId", source = "featureId"),
        @Mapping(target = "description", source = "description"),
        @Mapping(target = "creator", source = "creator"),
        @Mapping(target = "modifier", source = "modifier")
    })
    SysFeaturePO dtoToPo(SysFeatureDTO dto);

    @Mappings({
        @Mapping(target = "featureId", source = "featureId"),
        @Mapping(target = "description", source = "description"),
        @Mapping(target = "creator", source = "creator"),
        @Mapping(target = "modifier", source = "modifier")
    })
    SysFeatureVO dtoToVo(SysFeatureDTO dto);
}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试向量化匹配效果
"""

import json
from vector_api_matcher import VectorAPIMatching


def quick_test():
    """快速测试向量化匹配"""
    
    # API密钥
    api_key = "sk-fdfd70db7f9b48b4b8cad01c56c3fe99"
    
    # 前置依赖查询
    queries = [
        "创建项目",
        "创建账号", 
        "创建改密计划"
    ]
    
    # 从API文档文件加载数据
    possible_files = ["api_docs.json", "apis.json", "swagger.json", "openapi.json"]
    api_docs = []
    loaded_file = None

    for file_path in possible_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            print(f"成功加载文件: {file_path}")

            # 提取API列表
            if "apis" in data:
                api_docs = data["apis"]
                loaded_file = file_path
                break
            elif "paths" in data:
                # OpenAPI格式
                api_docs = []
                for path, methods in data["paths"].items():
                    for method, details in methods.items():
                        if method.lower() in ['get', 'post', 'put', 'delete', 'patch']:
                            api_docs.append({
                                "method": method.upper(),
                                "path": path,
                                "summary": details.get('summary', ''),
                                "description": details.get('description', '')
                            })
                loaded_file = file_path
                break
            elif isinstance(data, list):
                api_docs = data
                loaded_file = file_path
                break

        except FileNotFoundError:
            continue
        except Exception as e:
            print(f"加载文件 {file_path} 失败: {e}")
            continue

    if not api_docs:
        print("未找到有效的API文档文件")
        return
    
    print(f"=== 向量化匹配测试 ===")
    print(f"数据源: {loaded_file}")
    print(f"API文档数量: {len(api_docs)}")
    print(f"测试查询: {queries}")
    print()
    
    try:
        matcher = VectorAPIMatching(api_key=api_key)
        
        results_summary = {}
        
        for query in queries:
            print(f"查询: {query}")
            print("-" * 30)
            
            results = matcher.find_best_matching_apis(
                api_description=query,
                api_documents=api_docs,
                top_k=3,
                similarity_threshold=0.5
            )
            
            if results:
                print(f"找到 {len(results)} 个匹配:")
                best_match = results[0]
                api_data = best_match['api_data']
                
                print(f"  最佳匹配: [{api_data.get('method')}] {api_data.get('summary')}")
                print(f"  路径: {api_data.get('path')}")
                print(f"  相似度: {best_match['similarity_score']:.4f}")
                
                # 显示其他匹配
                if len(results) > 1:
                    print("  其他匹配:")
                    for i, result in enumerate(results[1:], 2):
                        api = result['api_data']
                        print(f"    {i}. {api.get('summary')} (相似度: {result['similarity_score']:.4f})")
                
                results_summary[query] = {
                    "best_match": api_data.get('summary'),
                    "similarity": best_match['similarity_score'],
                    "method": api_data.get('method'),
                    "path": api_data.get('path')
                }
            else:
                print("  未找到匹配")
                results_summary[query] = None
            
            print()
        
        # 输出汇总
        print("=== 匹配汇总 ===")
        for query, result in results_summary.items():
            if result:
                print(f"{query} → {result['best_match']} (相似度: {result['similarity']:.4f})")
            else:
                print(f"{query} → 未匹配")
        
        # 验证是否避免了错误匹配
        print("\n=== 匹配质量验证 ===")
        
        # 检查是否有不合理的匹配
        issues = []
        
        for query, result in results_summary.items():
            if result:
                summary = result['best_match']
                
                # 检查一些明显不合理的匹配
                if "创建项目" in query and "审批" in summary:
                    issues.append(f"❌ {query} 错误匹配到 {summary}")
                elif "创建账号" in query and "项目" in summary and "账号" not in summary:
                    issues.append(f"❌ {query} 错误匹配到 {summary}")
                elif "改密计划" in query and "改密" not in summary and "密码" not in summary:
                    issues.append(f"❌ {query} 错误匹配到 {summary}")
                else:
                    print(f"✅ {query} → {summary} (匹配合理)")
        
        if issues:
            print("\n发现的问题:")
            for issue in issues:
                print(issue)
        else:
            print("✅ 所有匹配都是合理的，没有发现明显的错误匹配")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")


if __name__ == "__main__":
    quick_test()

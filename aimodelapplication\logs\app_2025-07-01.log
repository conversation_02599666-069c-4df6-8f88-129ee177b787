2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:45 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-01 10:45:59 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))

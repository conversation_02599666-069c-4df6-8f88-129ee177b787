package com.qfnu.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qfnu.common.constant.DefaultConstants;
import com.qfnu.model.dto.AuthUserDto;
import com.qfnu.model.po.SysUsersPO;
import com.qfnu.model.param.SysUsersParam;
import com.qfnu.mapper.primary.SysUsersMapper;
import org.mindrot.jbcrypt.BCrypt;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;

@Service
public class SysUsersService extends ServiceImpl<SysUsersMapper, SysUsersPO> {

    @Resource
    private SysUsersMapper sysUsersMapper;

    @Value("${app.security.bcrypt.salt}")
    private String salt;



    public AuthUserDto selectOne(String username) {

//        QueryWrapper<SysUsersPO> queryWrapper = new QueryWrapper<>();
//        // 设置查询条件，用户名等于指定值
//        queryWrapper.eq("username", username);
        return sysUsersMapper.getUserByUseranme(username);
    }

    public AuthUserDto getUserDtoById(String userId) {
        return sysUsersMapper.getUserById(userId);
    }

    @Transactional(rollbackFor = Exception.class)
    public int resetPassword(SysUsersParam param) {
        SysUsersPO sysUsersPO = new SysUsersPO();
        sysUsersPO.setPassword(BCrypt.hashpw(DefaultConstants.DEFAULT_PASSWORD, salt));
        sysUsersPO.setUserId(param.getUserId());
        return sysUsersMapper.MyupdateById(sysUsersPO);
    }

    @Transactional(rollbackFor = Exception.class)
    public int updateLastLoginTime(SysUsersPO sysUsersPO) {
        return sysUsersMapper.MyupdateById(sysUsersPO);
    }

    /**
     * 根据邮箱查询用户
     */
    public AuthUserDto selectByEmail(String email) {
        LambdaQueryWrapper<SysUsersPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUsersPO::getEmail, email);
        SysUsersPO userPO = this.getOne(queryWrapper);

        if (userPO != null) {
            // 转换为AuthUserDto
            AuthUserDto authUserDto = new AuthUserDto();
            authUserDto.setUserId(userPO.getUserId());
            authUserDto.setUsername(userPO.getUsername());
            authUserDto.setEmail(userPO.getEmail());
            authUserDto.setFirstName(userPO.getFirstName());
            authUserDto.setLastName(userPO.getLastName());
            authUserDto.setIsActive(userPO.getIsActive());
            authUserDto.setIsStaff(userPO.getIsStaff());
            authUserDto.setIsSuperuser(userPO.getIsSuperuser());
            authUserDto.setDateJoined(userPO.getDateJoined());
            authUserDto.setLastLogin(userPO.getLastLogin());
            return authUserDto;
        }

        return null;
    }

    /**
     * 根据工号查询用户
     */
    public AuthUserDto selectByJobNo(String jobNo) {
        if (jobNo == null || jobNo.isEmpty()) {
            return null;
        }
        return sysUsersMapper.getUserByJobNo(jobNo);
    }
}
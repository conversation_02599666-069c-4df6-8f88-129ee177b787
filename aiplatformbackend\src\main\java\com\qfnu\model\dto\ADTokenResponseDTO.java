package com.qfnu.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@ApiModel(value = "ADTokenResponseDTO", description = "AD Token响应传输对象")
public class ADTokenResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("响应状态码：0成功，非0失败")
    private Integer code;

    @ApiModelProperty("响应消息")
    private String msg;

    @ApiModelProperty("用户信息内容")
    private ADUserInfoDTO content;
}

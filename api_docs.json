{"apis": [{"id": "1", "method": "POST", "path": "/openapi/v1.0/project/projects", "summary": "项目创建", "description": "创建新的项目，返回项目ID", "tags": ["项目管理"], "parameters": [{"name": "name", "type": "string", "description": "项目名称"}, {"name": "description", "type": "string", "description": "项目描述"}]}, {"id": "2", "method": "POST", "path": "/openapi/v1.0/project/projects/{projectId}/approve_rules", "summary": "审批规则创建", "description": "为指定项目创建审批规则", "tags": ["审批管理"], "parameters": [{"name": "projectId", "type": "string", "description": "项目ID"}]}, {"id": "3", "method": "POST", "path": "/openapi/v1.0/project/projects/{projectId}/assets/{assetId}/accounts", "summary": "资产账号创建", "description": "在指定资产下创建账号，返回账号ID", "tags": ["账号管理"], "parameters": [{"name": "projectId", "type": "string", "description": "项目ID"}, {"name": "assetId", "type": "string", "description": "资产ID"}]}, {"id": "4", "method": "POST", "path": "/openapi/v1.0/project/projects/{projectId}/password_plans", "summary": "改密计划创建", "description": "创建密码变更计划，返回计划ID", "tags": ["密码管理"], "parameters": [{"name": "projectId", "type": "string", "description": "项目ID"}]}, {"id": "5", "method": "POST", "path": "/openapi/v1.0/project/projects/{projectId}/password_plans:add_accounts", "summary": "关联改密计划账号", "description": "将账号关联到改密计划", "tags": ["密码管理"], "parameters": [{"name": "projectId", "type": "string", "description": "项目ID"}, {"name": "passwordPlanId", "type": "string", "description": "改密计划ID"}]}, {"id": "6", "method": "GET", "path": "/openapi/v1.0/project/projects", "summary": "项目列表查询", "description": "查询项目列表", "tags": ["项目管理"]}, {"id": "7", "method": "POST", "path": "/openapi/v1.0/accounts", "summary": "账号创建", "description": "创建新账号", "tags": ["账号管理"]}, {"id": "8", "method": "GET", "path": "/openapi/v1.0/accounts", "summary": "账号列表查询", "description": "查询账号列表", "tags": ["账号管理"]}, {"id": "9", "method": "PUT", "path": "/openapi/v1.0/project/projects/{projectId}", "summary": "项目信息更新", "description": "更新项目信息", "tags": ["项目管理"], "parameters": [{"name": "projectId", "type": "string", "description": "项目ID"}]}, {"id": "10", "method": "DELETE", "path": "/openapi/v1.0/project/projects/{projectId}", "summary": "项目删除", "description": "删除指定项目", "tags": ["项目管理"], "parameters": [{"name": "projectId", "type": "string", "description": "项目ID"}]}, {"id": "11", "method": "POST", "path": "/openapi/v1.0/assets", "summary": "资产创建", "description": "创建新资产", "tags": ["资产管理"]}, {"id": "12", "method": "GET", "path": "/openapi/v1.0/assets", "summary": "资产列表查询", "description": "查询资产列表", "tags": ["资产管理"]}]}
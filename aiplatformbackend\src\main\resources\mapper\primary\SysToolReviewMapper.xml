<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qfnu.mapper.primary.SysToolReviewMapper">

    <resultMap id="ReviewDetailMap" type="com.qfnu.model.dto.SysReviewDTO">
        <id column="review_id" property="reviewId"/>
        <result column="related_id" property="relatedId"/>
        <result column="submission_time" property="submissionTime"/>
        <result column="approval_time" property="approvalTime"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="reviewer" property="reviewer"/>
        <association property="tool" javaType="com.qfnu.model.dto.SysToolDTO">
            <result column="tool_name" property="toolName"/>
            <result column="link_address" property="linkAddress"/>
            <result column="creators" property="creators"/>
            <result column="function_description" property="functionDescription"/>
            <result column="category" property="category"/>
            <result column="tags" property="tags"/>
            <result column="rating" property="rating"/>
            <result column="review_status" property="reviewStatus"/>
            <result column="is_deleted" property="isDeleted"/>
            <result column="is_published" property="isPublished"/>
        </association>
    </resultMap>

    <!-- 插入审核记录 -->
    <insert id="insertToolReview" parameterType="com.qfnu.model.po.SysReviewPO">
        INSERT INTO sys_review (
            review_id, related_id, submission_time, approval_status
        ) VALUES (
            #{reviewId}, #{relatedId}, #{submissionTime}, #{approvalStatus}
        )
    </insert>

    <!-- 更新审核状态 -->
    <update id="updateReviewStatus" parameterType="com.qfnu.model.po.SysReviewPO">
        UPDATE sys_review
        SET approval_status = #{approvalStatus},
            approval_time = #{approvalTime},
            reviewer = #{reviewer}
        WHERE related_id = #{relatedId}
    </update>

    <!-- 分页查询审核记录 -->
    <select id="selectReviewPage" resultMap="ReviewDetailMap">
        SELECT 
            r.review_id, r.related_id, r.submission_time, r.approval_time, 
            r.approval_status, r.reviewer,
            t.tool_name, t.link_address, t.creators, t.function_description, 
            t.category, t.tags, t.rating,t.review_status,t.is_published,t.is_deleted
        FROM sys_review r
        LEFT JOIN sys_tool t ON r.related_id = t.tool_id
        <where>
            t.is_deleted = '0'
            <choose>
                <when test="param.approvalStatusList != null and param.approvalStatusList.size() > 0">
                    AND r.approval_status IN
                    <foreach collection="param.approvalStatusList" item="status" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </when>
                <when test="param.approvalStatus != null">
                    AND r.approval_status = #{param.approvalStatus}
                </when>
            </choose>
            <if test="param.relatedId != null and param.relatedId != ''">
                AND r.related_id = #{param.relatedId}
            </if>
            <if test="param.reviewer != null and param.reviewer != ''">
                AND r.reviewer = #{param.reviewer}
            </if>
        </where>
        ORDER BY r.submission_time DESC
    </select>

    <!-- 获取审核详情 -->
    <select id="selectReviewDetail" resultMap="ReviewDetailMap">
        SELECT 
            r.review_id, r.related_id, r.submission_time, r.approval_time, 
            r.approval_status, r.reviewer,
            t.tool_name, t.link_address, t.creators, t.function_description, 
            t.category, t.tags, t.rating
        FROM sys_review r
        LEFT JOIN sys_tool t ON r.related_id = t.tool_id
        WHERE r.review_id = #{reviewId}
        AND t.is_deleted = '0'
    </select>

    <!-- 更新工具状态 -->
    <update id="updateToolStatus">
        UPDATE sys_tool
        SET review_status = #{status}
        WHERE tool_id = #{toolId}
    </update>

    <!-- 更新工具删除状态 -->
    <update id="updateToolDeleteStatus">
        UPDATE sys_tool
        SET is_deleted = #{status}
        WHERE tool_id = #{toolId}
    </update>

    <!-- 更新工具上架状态 -->
    <update id="updatePublish">
        UPDATE sys_tool
        SET is_published = #{status}
        WHERE tool_id = #{toolId}
    </update>

</mapper>
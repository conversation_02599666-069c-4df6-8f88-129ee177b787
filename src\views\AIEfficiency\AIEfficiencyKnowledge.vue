<template>
  <div class="knowledge-container">
    <!-- 标签筛选器 -->
    <div class="filter-container">
      <div class="filter-section">
        <div class="filter-label">分类：</div>
        <div class="tags-container">
          <button class="scroll-btn scroll-left" @click="scrollTags('category', 'left')"
            :class="{ 'hidden': categoryScrollPos <= 0 }">
            <left-outlined />
          </button>
          <div class="tags-wrapper" ref="categoryTagsWrapper">
            <a-tag v-for="category in allCategories" :key="category"
              :class="['filter-tag', { active: selectedCategories.includes(category) }]" :data-category="category"
              @click="toggleCategory(category)">
              {{ category }}
            </a-tag>
          </div>
          <button class="scroll-btn scroll-right" @click="scrollTags('category', 'right')"
            :class="{ 'hidden': !hasMoreCategoryTags }">
            <right-outlined />
          </button>
        </div>
      </div>

      <div class="filter-section">
        <div class="filter-label">标签：</div>
        <div class="tags-container">
          <button class="scroll-btn scroll-left" @click="scrollTags('tag', 'left')"
            :class="{ 'hidden': tagScrollPos <= 0 }">
            <left-outlined />
          </button>
          <div class="tags-wrapper" ref="tagTagsWrapper">
            <a-tag v-for="tag in allTags" :key="tag" :class="['filter-tag', { active: selectedTags.includes(tag) }]"
              @click="toggleTag(tag)">
              {{ tag }}
            </a-tag>
          </div>
          <button class="scroll-btn scroll-right" @click="scrollTags('tag', 'right')"
            :class="{ 'hidden': !hasMoreTags }">
            <right-outlined />
          </button>
        </div>
      </div>

      <div class="filter-section">
        <div class="flex items-center gap-4">
          <a-input v-model:value="searchText" placeholder="使用文档名进行搜索" class="search-input" @pressEnter="handleSearch">
            <template #prefix>
              <search-outlined style="color: rgba(0, 0, 0, 0.25)" />
            </template>
          </a-input>
          <a-button type="primary" @click="showDifyModal = true">
            <div class="flex-center">
              <partition-outlined />
              绑定dify账号
            </div>
          </a-button>
        </div>
      </div>
    </div>

    <!-- 中间容器 -->
    <div class="middle-container">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 card-grid">
        <!-- 创建卡片 -->
        <div
          class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 overflow-hidden border border-blue-200 cursor-pointer h-full flex items-center justify-center"
          @click="showCreateModal = true">
          <div class="p-6 w-full">
            <div class="flex flex-col items-center justify-center text-center">
              <div class="flex items-center justify-center space-x-4 mb-4">
                <div
                  class="w-12 h-12 rounded-lg overflow-hidden bg-white/50 flex items-center justify-center ring-1 ring-blue-200">
                  <plus-outlined style="font-size: 24px; color: #1890ff;" />
                </div>
                <h3 class="text-lg font-semibold text-blue-900">创建知识库</h3>
              </div>
              <p class="text-sm text-blue-700 line-clamp-2">导入您自己的文本数据或通过 Webhook 实时写入数据以增强 LLM 的上下文。</p>
            </div>
          </div>
        </div>

        <!-- 知识库卡片 -->
        <div v-for="kb in filteredKnowledgeBases" :key="kb.id"
          class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 overflow-hidden border border-gray-100 flex flex-col cursor-pointer relative card-container"
          @click="handleCardClick(kb.id)">
          <div class="p-6 pb-3 flex-grow">
            <div class="flex items-center space-x-4 mb-4">
              <div class="w-12 h-12 rounded-lg overflow-hidden flex items-center justify-center"
                :style="{ backgroundColor: getCategoryBgColor(kb.category) }">
                <component :is="getIconByCategory(kb.category)" :style="{ fontSize: '24px', color: '#fff' }" />
              </div>
              <div class="flex-grow">
                <h3 class="text-lg font-semibold text-gray-900">{{ kb.title }}</h3>
                <div class="flex items-center space-x-4 mt-2">
                  <span class="text-xs text-gray-500">
                    {{ kb.docs }} 文档
                  </span>
                  <span class="text-xs text-gray-500">
                    {{ kb.chars }} 字符
                  </span>
                </div>
              </div>
            </div>
            <p class="text-sm text-gray-600 mb-2 line-clamp-2">{{ kb.description }}</p>
          </div>
          <div class="px-6 pt-0 pb-4">
            <div class="flex items-center justify-between">
              <!-- 标签容器，占4/5的空间 -->
              <div class="flex-grow flex items-center flex-wrap mr-2 overflow-hidden" style="max-width: 80%;">
                <span v-for="tag in kb.tags" :key="tag"
                  class="px-2 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-600 mr-1 mb-1 cursor-pointer hover:bg-blue-100"
                  @click.stop="toggleTag(tag)">
                  {{ tag }}
                </span>
              </div>

              <!-- 按钮容器，占1/5的空间，悬停时显示 -->
              <div class="flex items-center justify-end btn-container" style="width: 20%;">
                <ApiOutlined class="action-btn" @click.stop="goToApiDocs(kb.id, $event)"
                  style="color: #1890ff; margin-right: 8px;" />
                <SettingOutlined class="action-btn" @click.stop="showEditModal(kb)"
                  style="color: #52c41a; margin-right: 8px;" />
                <DeleteOutlined class="action-btn" @click.stop="deleteKnowledgeBase(kb.id)" style="color: red;" />
              </div>
            </div>
          </div>
        </div>

        <!-- 空白占位卡片 -->
        <div v-for="n in placeholderCards" :key="'placeholder-' + n"
          class="bg-transparent rounded-lg flex flex-col h-full min-h-[200px]">
          <div class="p-6 flex-grow"></div>
        </div>
      </div>
      <!-- 分页器 -->
      <div class="mt-6 flex justify-center">
        <a-pagination v-model:current="currentPage" v-model:pageSize="pageSize" :total="totalKnowledgeBases"
          :pageSizeOptions="['7', '15', '23']" :showSizeChanger="true" :showQuickJumper="true"
          @change="handlePageChange" @showSizeChange="handleSizeChange" />
      </div>
    </div>

    <!-- 底部容器 -->
    <div class="bottom-container">
      <a-pagination v-model:current="currentPage" :total="totalKnowledgeBases" :pageSize="14" @change="handlePageChange"
        :showSizeChanger="false" />
    </div>
  </div>
  <knowledge-detail v-if="showDetail" @back="showDetail = false" />

  <!-- Dify账号绑定弹窗 -->
  <a-modal v-model:visible="showDifyModal" :title="null" @cancel="handleCancel" :footer="null" width="30%"
    class="dify-modal" :closable="false">
    <div class="dify-form">
      <div class="dify-header">
        <div class="dify-title">
          <h2>{{ isLogin ? '嗨，近来可好' : '申请dify账号' }}</h2>
          <div class="dify-subtitle">
            <span class="wave-icon">{{ isLogin ? '👋' : '📝' }}</span>
            {{ isLogin ? '欢迎使用dify, 绑定账号以继续' : '申请dify账号后，需要对申请的账号进行绑定' }}
          </div>
        </div>
      </div>

      <a-form :model="formState" name="difyForm" @finish="handleSubmit" :label-col="{ flex: '60px' }"
        :wrapper-col="{ flex: 'auto' }">
        <a-form-item style="width: 100%;" label="邮箱" name="email"
          :rules="[{ required: true, type: 'email', message: '请输入有效的邮箱地址' }]">
          <a-input v-model:value="formState.email" placeholder="输入邮箱地址" size="large" />
        </a-form-item>

        <a-form-item v-if="isLogin" style="width: 100%;" label="密码" name="password"
          :rules="[{ required: true, message: '请输入密码' }]">
          <a-input-password v-model:value="formState.password" placeholder="输入密码" size="large" />
        </a-form-item>

        <div class="form-footer">
          <a-button type="primary" html-type="submit" :loading="loading" style="margin-right: 5%">
            {{ isLogin ? '绑定dify账号' : '申请账号' }}
          </a-button>
          <span>
            {{ isLogin ? '没有账号？' : '已有账号？' }}
            <a-button type="link" @click="toggleMode">
              {{ isLogin ? '去申请' : '返回绑定' }}
            </a-button>
          </span>
        </div>
      </a-form>
    </div>
  </a-modal>

  <!-- 创建知识库弹窗 -->
  <a-modal v-model:visible="showCreateModal" :title="null" @ok="handleCreateSubmit" @cancel="handleCreateCancel"
    :confirmLoading="createLoading" width="35%" class="dify-modal" :closable="true" :footer="null">
    <div class="dify-form">
      <div class="dify-header">
        <div class="dify-title">
          <h2>创建</h2>
          <div class="dify-subtitle">
            <span class="wave-icon">📚</span> 创建一个新的空知识库来管理你的数据
          </div>
        </div>
      </div>

      <a-form :model="createForm" :rules="createRules" ref="createFormRef" name="createForm"
        @finish="handleCreateSubmit" :label-col="{ flex: '100px' }" :wrapper-col="{ flex: 'auto' }">
        <a-form-item label="知识库名称" name="name" :rules="[{ required: true, message: '请输入知识库名称' }]">
          <a-input v-model:value="createForm.name" placeholder="请输入知识库名称" />
        </a-form-item>

        <a-form-item label="知识库类别" name="category" :rules="[{ required: true, message: '请选择知识库类别' }]"
          :label-col="{ flex: '100px' }" :wrapper-col="{ flex: 'auto' }">
          <div class="select-row">
            <a-select v-model:value="createForm.category" style="width: 100%" @change="handleCategoryChange">
              <a-select-option value="A">文档型</a-select-option>
              <a-select-option value="B">代码型</a-select-option>
              <a-select-option value="C">图谱型</a-select-option>
            </a-select>
          </div>
        </a-form-item>

        <!-- 使用独立的容器来包裹条件渲染的内容 -->
        <div v-if="createForm.category === 'C'" class="additional-form-fields">
          <div class="mt-4 space-y-3 border-t pt-3 border-gray-100">
            <!-- 并排布局 -->
            <div class="flex gap-4">
              <!-- IP 地址 -->
              <a-form-item label="IP 地址" name="neo4j_host" :rules="[{ required: true, message: '请输入 IP 地址' }]"
                class="flex-1">
                <a-input v-model:value="createForm.neo4j_host" placeholder="例如：***********" />
              </a-form-item>

              <!-- 端口号 -->
              <a-form-item label="端口号" name="neo4j_port"
                :rules="[{ required: true, pattern: /^[0-9]+$/, message: '请输入有效的端口号' }]" class="flex-1">
                <a-input v-model:value="createForm.neo4j_port" placeholder="例如：5432" />
              </a-form-item>
            </div>

            <!-- 用户名 -->
            <a-form-item label="用户名" name="neo4j_user" :rules="[{ required: true, message: '请输入用户名' }]" class="flex-1">
              <a-input v-model:value="createForm.neo4j_user" placeholder="输入用户名" />
            </a-form-item>

            <!-- 密码 -->
            <a-form-item label="密码" name="neo4j_password" :rules="[{ required: true, message: '请输入密码' }]"
              class="flex-1">
              <a-input-password v-model:value="createForm.neo4j_password" placeholder="输入密码" size="middle" />
            </a-form-item>

            <!-- 数据库 -->
            <a-form-item label="数据库" name="neo4j_db" :rules="[{ required: true, message: '请输入数据库名称' }]" class="flex-1">
              <a-input v-model:value="createForm.neo4j_db" placeholder="请输入数据库名称" />
            </a-form-item>

            <!-- 标签 -->
            <a-form-item label="标签" name="neo4j_tags" class="flex-1">
              <div class="tag-form-item-wrapper">
                <!-- 标签输入框 -->
                <a-input v-model:value="newTag" placeholder="输入标签，按回车添加" @keyup.enter.native.prevent="addTag"
                  @keydown.enter.prevent ref="tagInput" size="middle" class="tag-input" />
                <!-- 标签展示区域 -->
                <div class="tags-list">
                  <a-tag v-for="(tag, index) in createForm.neo4j_tags" :key="index" color="blue" closable
                    @close="removeTag(index)">
                    {{ tag }}
                  </a-tag>
                </div>
              </div>
            </a-form-item>
          </div>
        </div>

        <div class="form-footer">
          <a-button type="primary" html-type="submit" :loading="createLoading">
            创建
          </a-button>
        </div>
      </a-form>
    </div>
  </a-modal>

  <!-- Add a drawer for code-type knowledge base settings -->
  <a-drawer v-model:open="openCode" size="large" class="custom-class" root-class-name="root-class-name"
    :root-style="{ color: 'blue' }" style="color: black" title="代码型知识库设置" placement="right"
    @after-open-change="afterOpenChange">
    <template #footer>
      <a-button @click="onClose">取消</a-button>
      <a-button type="primary" style="margin-left: 10px;" @click="onClose">完成</a-button>
    </template>
    <div>
      <h1 style="position: sticky; top: 0; left: 0; font-weight: 600; font-size: 16px; line-height: 28px;">代码型知识库设置内容
      </h1>
      <!-- Add content specific to code-type knowledge base settings here -->
    </div>
  </a-drawer>

  <!-- 编辑知识库弹窗 -->
  <a-modal v-model:visible="showEditModalVisible" :title="null" @ok="handleEditSubmit" @cancel="handleEditCancel"
    :confirmLoading="editLoading" width="35%" class="dify-modal" :closable="true" :footer="null">
    <div class="dify-form">
      <div class="dify-header">
        <div class="dify-title">
          <h2>编辑知识库</h2>
          <div class="dify-subtitle">
            <span class="wave-icon">✏️</span> 编辑知识库信息
          </div>
        </div>
      </div>

      <a-form :model="editForm" :rules="editRules" ref="editFormRef" name="editForm" @finish="handleEditSubmit"
        :label-col="{ flex: '100px' }" :wrapper-col="{ flex: 'auto' }">
        <a-form-item label="知识库名称" name="name" :rules="[{ required: true, message: '请输入知识库名称' }]">
          <a-input v-model:value="editForm.name" placeholder="请输入知识库名称" />
        </a-form-item>

        <a-form-item label="知识库描述" name="description" class="description-item"
          :rules="[{ required: true, message: '请输入知识库描述' }]">
          <a-textarea style="height: 80px;" v-model:value="editForm.description" placeholder="请输入知识库描述" :rows="4" />
        </a-form-item>

        <a-form-item label="知识库权限" name="permission" :rules="[{ required: true, message: '请选择知识库权限' }]"
          :label-col="{ flex: '100px' }" :wrapper-col="{ flex: 'auto' }">
          <div class="visibility-wrapper">
            <div class="select-row">
              <a-select v-model:value="editForm.permission" style="width: 100%" placeholder="请选择知识库权限">
                <a-select-option value="all_team_members">公开</a-select-option>
                <a-select-option value="only_me">私有</a-select-option>
              </a-select>
            </div>
          </div>
        </a-form-item>

        <div class="form-footer">
          <a-button type="primary" html-type="submit" :loading="editLoading">
            保存
          </a-button>
        </div>
      </a-form>
    </div>
  </a-modal>

  <!-- 加载动画 -->
  <div v-if="showLoading" class="loading-container">
    <loading-outlined :style="{ fontSize: '48px', color: '#1890ff' }" />
  </div>

</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, h } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'; // 确保正确导入图标
import {
  SearchOutlined,
  PlusOutlined,
  ArrowRightOutlined,
  FolderOutlined,
  PartitionOutlined,
  SettingOutlined,
  BookOutlined,
  FileTextOutlined,
  AppstoreOutlined,
  CodeOutlined,
  DatabaseOutlined,
  InboxOutlined,
  DownOutlined,
  FontSizeOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  LeftOutlined,
  RightOutlined,
  ApiOutlined,
  LoadingOutlined
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import http from '@/utils/ai/http'
import { createApp } from 'vue'

const router = useRouter()

const showDetail = ref(false)
const showDifyModal = ref(false)
const isLogin = ref(true)
const loading = ref(false)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(7)
const totalKnowledgeBases = ref(0)

// 标签筛选
const selectedTags = ref([])
const selectedCategories = ref([])
// 计算所有标签
const allTags = computed(() => {
  const tagSet = new Set()
  knowledgeBases.forEach(kb => {
    if (kb.tags && Array.isArray(kb.tags)) {
      kb.tags.forEach(tag => tagSet.add(tag))
    }
  })
  return Array.from(tagSet)
})
const allCategories = ref(['文档型', '代码型', '图谱型'])

// 示例知识库数据
const knowledgeBases = reactive([])

const formState = reactive({
  email: '',
  password: '',
  confirmPassword: ''
})

// 其他配置
const value = ref(null)
const OPTIONS = ['Apples', 'Nails', 'Bananas', 'Helicopters']
const selectedItems = ref([])
const slider_value = ref(30)
const filteredOptions = computed(() => OPTIONS.filter(o => !selectedItems.value.includes(o)))
const plainOptions = ['权重设置', 'ReRank模型']
const value1 = ref(plainOptions[0])

const embedding_items = ref(['netease-youdao/bce-embedding-large', 'lucy'])
const embedding_value = ref(embedding_items.value[0])

const checked = ref(true)
const checked1 = ref(false)

const state1 = ref(true)

const current = ref(0)
const current1 = ref(0)
const current2 = ref(0)

const max_line_value = ref(500)
const double_line_value = ref(50)

// Mock 数据
const mockData = []
for (let i = 0; i < 20; i++) {
  mockData.push({
    key: i.toString(),
    title: `content${i + 1}`,
    description: `description of content${i + 1}`,
    disabled: i % 3 < 1,
  })
}

const fileList = ref([])
const handleChange = (info) => {
  const status = info.file.status
  if (status !== 'uploading') {
    console.log(info.file, info.fileList)
  }
  if (status === 'done') {
    message.success(`${info.file.name} file uploaded successfully.`)
  } else if (status === 'error') {
    message.error(`${info.file.name} file upload failed.`)
  }
}

function handleDrop(e) {
  console.log(e)
}

const open = ref(false)
const openCode = ref(false)
const openKnowledge = ref(false)
const placement = ref('topLeft')

const afterOpenChange = (bool) => {
  console.log('open', bool)
}

const last = () => {
  if (current.value > 0) {
    current.value -= 1;
  }
};

const next = () => {
  if (current.value < 2) {
    current.value += 1;
  }
};

const showDrawerKnowledge = () => {
  showCreateModal.value = true
}

const onClose = () => {
  open.value = false;
  current.value = 0;
};

// 搜索相关
const searchText = ref('')

// 计算过滤和分页后的知识库列表
const filteredKnowledgeBases = computed(() => {
  let filtered = knowledgeBases

  // 如果有搜索文本，进行过滤
  if (searchText.value) {
    const searchLower = searchText.value.toLowerCase()
    filtered = filtered.filter(kb =>
      kb.title.toLowerCase().includes(searchLower)
    )
  }

  // 如果有选中的标签，进行过滤
  if (selectedTags.value.length > 0) {
    filtered = filtered.filter(kb =>
      kb.tags.some(tag => selectedTags.value.includes(tag))
    )
  }

  // 如果有选中的分类，进行过滤
  if (selectedCategories.value.length > 0) {
    filtered = filtered.filter(kb => {
      const categoryMap = {
        '文档型': 'A',
        '代码型': 'B',
        '图谱型': 'C'
      }
      return selectedCategories.value.some(category =>
        kb.category === categoryMap[category]
      )
    })
  }

  // 更新总数
  totalKnowledgeBases.value = filtered.length

  // 计算分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value

  return filtered.slice(start, end)
})

// 搜索处理函数
const handleSearch = (value) => {
  searchText.value = value
  currentPage.value = 1 // 重置为第一页
}

// 根据分类获取对应的图标组件
const getIconByCategory = (category) => {
  const iconMap = {
    'A': FileTextOutlined, // 文档型图标
    'B': CodeOutlined,     // 代码型图标
    'C': DatabaseOutlined  // 图谱型图标
  }
  return iconMap[category] || FileTextOutlined
}

// 根据分类获取对应的颜色
const getCategoryColor = (category) => {
  const colorMap = {
    'A': '#1890ff', // 蓝色 - 文档型
    'B': '#52c41a', // 绿色 - 代码型
    'C': '#722ed1'  // 紫色 - 图谱型
  }
  return colorMap[category] || '#1890ff'
}

// 标签变化处理
const handleTagChange = (value) => {
  selectedTags.value = value
  currentPage.value = 1 // 重置为第一页
}

// 页码变化处理
const handlePageChange = (page) => {
  currentPage.value = page
}

// 每页条数变化处理
const handleSizeChange = (current, size) => {
  pageSize.value = size
  currentPage.value = 1 // 重置为第一页
}

// 标签选择逻辑
const toggleTag = (tag) => {
  const index = selectedTags.value.indexOf(tag)
  if (index === -1) {
    selectedTags.value.push(tag)
  } else {
    selectedTags.value.splice(index, 1)
  }
  currentPage.value = 1 // 重置为第一页
}

// 分类选择逻辑
const toggleCategory = (category) => {
  const index = selectedCategories.value.indexOf(category)
  if (index === -1) {
    selectedCategories.value.push(category)
  } else {
    selectedCategories.value.splice(index, 1)
  }
  currentPage.value = 1 // 重置为第一页
}

// 初始化
totalKnowledgeBases.value = knowledgeBases.length

// 添加保存邮箱数据的响应式变量
const savedEmail = ref('')

const dify_mapping = () => {
  // 调用dify_mapping接口
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  if (userInfo.username && userInfo.userId) {
    http.get('/dify_mapping/', {
      params: {
        username: userInfo.username,
        user_id: userInfo.userId
      }
    })
      .then(res => {
        if (res.data.code === 200) {
          savedEmail.value = res.data.data.records.dify_email
          formState.email = savedEmail.value
        }
      })
      .catch(err => {
        console.error('调用dify_mapping接口失败:', err)
      })
  } else {
    console.warn('未找到用户信息，无法调用dify_mapping接口')
  }
}


// 修改handleCancel函数，不再清空email
const handleCancel = () => {
  showDifyModal.value = false
  formState.password = ''
  formState.confirmPassword = ''
}

// 修改toggleMode函数，使用保存的email
const toggleMode = () => {
  isLogin.value = !isLogin.value
  formState.email = savedEmail.value
  formState.password = ''
  formState.confirmPassword = ''
}

const handleSubmit = async (values) => {
  loading.value = true
  try {
    if (isLogin.value) {
      // 绑定账号时需要验证邮箱和密码
      if (!values.email || !values.password) {
        message.error('请填写邮箱和密码')
        return
      }

      // 获取用户信息
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
      if (!userInfo.username || !userInfo.userId) {
        message.error('未找到用户信息')
        return
      }

      // 构建请求参数
      const params = {
        username: userInfo.username,
        email: values.email,
        password: values.password || '',
        user_id: userInfo.userId
      }

      // 调用dify_mapping接口
      const res = await http.post('/dify_mapping/', params)

      if (res.data.code === 200) {
        message.success(res.data.message || '绑定成功')
        savedEmail.value = values.email
        handleCancel()
      } else {
        message.error(res.data.message || '绑定失败')
      }
    } else {
      // 申请账号时只需要验证邮箱
      if (!values.email) {
        message.error('请填写邮箱')
        return
      }

      // 调用创建账号接口
      const res = await http.post('/dify_mapping/create_dify_account', {
        email: values.email
      })

      if (res.data.code === 200) {
        message.success(res.data.message || '申请成功')
        savedEmail.value = values.email

        // 如果有激活链接，打开新窗口
        if (res.data.data?.activation_url) {
          window.open(res.data.data.activation_url, '_blank')
        }

        handleCancel()
      } else {
        message.error(res.data.message || '申请失败')
        // 申请失败时不关闭弹窗
        return
      }
    }
  } catch (error) {
    console.error('操作失败:', error)
    message.error(error.response?.data?.message || (isLogin.value ? '绑定失败' : '申请失败'))
    // 申请失败时不关闭弹窗
    if (!isLogin.value) return
  } finally {
    loading.value = false
  }
}

// 编辑知识库
const editKnowledgeBase = (id) => {
  const kb = knowledgeBases.find(kb => kb.id === id);
  if (!kb) return;

  if (kb.category === 'B') { // Code type
    openCode.value = true;
  } else if (kb.category === 'A') {
    open.value = true;
    current.value = 0;
  } else if (kb.category === 'C') {
    openKnowledge.value = true;
    current2.value = 0;
  }
}

// 计算需要添加的占位卡片数量
const getPlaceholderCount = computed(() => {
  const totalSlots = 15; // 3行 x 5列 = 15个位置
  const actualCards = filteredKnowledgeBases.value.length + 1; // +1 是因为创建卡片
  return Math.max(0, totalSlots - actualCards);
})

// 创建知识库相关
const showCreateModal = ref(false)
const createLoading = ref(false)
const createFormRef = ref(null)

const createForm = reactive({
  name: '',
  category: 'A',
  interfaceType: 'swagger接口',
  neo4j_host: '',
  neo4j_port: '7687',
  neo4j_user: '',
  neo4j_password: '',
  neo4j_db: '',
  neo4j_tags: [], // 初始标签
})

const createRules = {
  name: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择知识库类别', trigger: 'change' }
  ],
  neo4j_host: [
    { required: true, message: '请输入 IP 地址', trigger: 'blur' }
  ],
  neo4j_port: [
    { required: true, pattern: /^[0-9]+$/, message: '请输入有效的端口号', trigger: 'blur' }
  ],
  neo4j_user: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  neo4j_password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  neo4j_tags: [
    { required: true, message: '请输入标签', trigger: 'blur' }
  ]
}
// 新标签输入
const newTag = ref('');
const tagInput = ref(null); // 输入框的引用
const addTag = (e) => {
  e.preventDefault(); // 阻止默认提交行为
  // 获取新标签的值并去除首尾空格
  const newTagValue = newTag.value.trim();

  // 检查新标签是否为空或已存在
  if (newTagValue && !createForm.neo4j_tags.includes(newTagValue)) {
    // 将新标签添加到 neo4j_tags 数组中
    createForm.neo4j_tags.push(newTagValue);

    // 清空输入框
    newTag.value = '';

    focusTagInput();
  }
};
// 删除标签方法
const removeTag = (index) => {
  // 移除指定索引位置的标签
  createForm.neo4j_tags.splice(index, 1);
};

// 输入框失去焦点时的处理方法
const handleTagInputBlur = () => {
  // 获取新标签值并去除首尾空格
  const newTagValue = newTag.value.trim();

  // 检查新标签是否为空或已存在
  if (newTagValue && !createForm.neo4j_tags.includes(newTagValue)) {
    // 将新标签添加到 neo4j_tags 数组中
    createForm.neo4j_tags.push(newTagValue);
  }

  // 清空输入框
  newTag.value = '';

  focusTagInput();
};

const focusTagInput = () => {
  nextTick(() => {
    if (tagInput.value) { // 检查 tagInput 是否已初始化
      tagInput.value.focus();
    }
  });
};
const handleCreateSubmit = async () => {
  try {
    await createFormRef.value.validate()
    createLoading.value = true

    // 检查知识库类型和Dify账号绑定
    if ((createForm.category === 'A' || createForm.category === 'B') && !savedEmail.value) {
      message.error('请先绑定Dify账号')
      return
    }

    // 获取用户信息
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    if (!userInfo.username || !userInfo.userId) {
      message.error('未找到用户信息')
      return
    }

    // 构建请求参数
    let params = {
      username: userInfo.username,
      user_id: userInfo.userId,
      name: createForm.name,
      description: '该知识库暂未设置描述',
      permission: 'only_me',
      category: createForm.category
    }

    // 如果是图谱型知识库，添加信息
    if (createForm.category === 'C') {
      params = {
        ...params,
        neo4j_host: createForm.neo4j_host,
        neo4j_port: createForm.neo4j_port,
        neo4j_user: createForm.neo4j_user,
        neo4j_password: createForm.neo4j_password,
        neo4j_db: createForm.neo4j_db,
        neo4j_tags: createForm.neo4j_tags.join('、') // 将标签数组转换为用“、”分隔的字符串
      }
    }

    // 调用创建知识库接口
    const res = await http.post('/knowledge_api/create_knowledge', params)

    if (res.data.code === 200) {
      message.success('知识库创建成功')
      fetchKnowledgeBases() // 重新获取数据
      handleCreateCancel()
    } else {
      message.error(res.data.message || '创建知识库失败')
    }
  } catch (error) {
    console.error('创建知识库失败:', error)
    message.error(error.response?.data?.message || '创建知识库失败')
  } finally {
    createLoading.value = false
  }
}

const handleCreateCancel = () => {
  showCreateModal.value = false
  createFormRef.value?.resetFields()
  // 重置表单数据
  Object.assign(createForm, {
    name: '',
    category: 'A',
    interfaceType: 'swagger接口'
  })
}
// 在 script setup 中添加新的响应式变量
const selectedUsers = ref([])

// 模拟用户数据
const userOptions = [
  { value: 'user1', label: '张三' },
  { value: 'user2', label: '李四' },
  { value: 'user3', label: '王五' },
  { value: 'user4', label: '赵六' },
]

// 删除知识库
const deleteKnowledgeBase = async (id) => {
  // 显示确认对话框
  const kb = knowledgeBases.find(kb => kb.id === id)
  if (!kb) return

  // 使用确认对话框询问用户是否确认删除
  const modal = Modal.confirm({
    title: '删除确认',
    content: `确定要删除知识库"${kb.title}"吗？此操作不可恢复。`,
    okText: '确认删除',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      try {
        // 获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
        if (!userInfo.username || !userInfo.userId) {
          message.error('未找到用户信息')
          return
        }

        // 构建请求参数
        const params = {
          uid: id,
          username: userInfo.username,
          category: kb.category,
          user_id: userInfo.userId
        }

        // 调用删除知识库接口
        const res = await http.delete('/knowledge_api/delete_knowledge', { data: params })

        if (res.data.code === 200) {
          message.success('知识库删除成功')
          fetchKnowledgeBases() // 重新获取数据
        } else {
          message.error(res.data.message || '删除知识库失败')
        }
      } catch (error) {
        console.error('删除知识库失败:', error)
        message.error(error.response?.data?.message || '删除知识库失败')
      }
    }
  })
}

const handleCardClick = (id) => {
  const kb = knowledgeBases.find(kb => kb.id === id);
  if (!kb) return;

  let routePath = '/documents/' + id; // Default to documents
  if (kb.category === 'B') {
    routePath = '/codder/' + id;
  } else if (kb.category === 'C') {
    routePath = '/relation/' + id;
  }

  router.push(routePath);
}

// 添加分类变化处理函数
const handleCategoryChange = (value) => {
  console.log('选择了知识库类别:', value);

  // const additionalContent = document.querySelector('.additional-info');
  // if (additionalContent) {
  //   if (value === 'C') {
  //     additionalContent.classList.add('fixed-height');
  //   } else {
  //     additionalContent.classList.remove('fixed-height');
  //   }
  // }
}

// 根据分类获取对应的背景颜色
const getCategoryBgColor = (category, alpha = 1) => {
  const colorMap = {
    'A': `rgba(24, 144, 255, ${alpha})`, // 蓝色 - 文档型
    'B': `rgba(82, 196, 26, ${alpha})`,  // 绿色 - 代码型
    'C': `rgba(114, 46, 209, ${alpha})`  // 紫色 - 图谱型
  }
  return colorMap[category] || `rgba(24, 144, 255, ${alpha})`
}

// 根据分类获取分类名称
const getCategoryName = (category) => {
  const nameMap = {
    'A': '文档型',
    'B': '代码型',
    'C': '图谱型'
  }
  return nameMap[category] || '文档型'
}

// 在script setup中添加响应式变量
const showLoading = ref(false)

// 修改fetchKnowledgeBases函数
const fetchKnowledgeBases = async () => {
  try {
    showLoading.value = true  // 显示加载动画

    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    if (userInfo.username && userInfo.userId) {
      const res = await http.get('/knowledge_base/', {
        params: {
          username: userInfo.username,
          user_id: userInfo.userId
        }
      })
      if (res.data.code === 200) {
        knowledgeBases.length = 0 // 清空原有数据
        knowledgeBases.push(...res.data.data.records) // 更新数据
        totalKnowledgeBases.value = knowledgeBases.length // 更新总数
      } else {
        message.error(res.data.message || '获取知识库数据失败')
      }
    } else {
      message.error('未找到用户信息，无法获取知识库数据')
    }
  } catch (error) {
    console.error('获取知识库数据失败:', error)
    message.error(error.response?.data?.message || '获取知识库数据失败')
  } finally {
    showLoading.value = false  // 隐藏加载动画
  }
}

// 标签滚动控制
const categoryTagsWrapper = ref(null)
const tagTagsWrapper = ref(null)
const categoryScrollPos = ref(0)
const tagScrollPos = ref(0)
const hasMoreCategoryTags = ref(false)
const hasMoreTags = ref(false)

// 滚动标签的函数
const scrollTags = (type, direction) => {
  const containerRef = type === 'category' ? categoryTagsWrapper.value : tagTagsWrapper.value
  if (!containerRef) return

  const scrollAmount = 200 // 每次滚动的像素量
  const currentScroll = containerRef.scrollLeft

  if (direction === 'left') {
    containerRef.scrollLeft = Math.max(0, currentScroll - scrollAmount)
  } else {
    containerRef.scrollLeft = currentScroll + scrollAmount
  }

  // 更新滚动位置
  setTimeout(() => {
    if (type === 'category') {
      categoryScrollPos.value = containerRef.scrollLeft
      checkMoreCategoryTags()
    } else {
      tagScrollPos.value = containerRef.scrollLeft
      checkMoreTags()
    }
  }, 100)
}

// 检查是否有更多标签
const checkMoreCategoryTags = () => {
  if (!categoryTagsWrapper.value) return

  const container = categoryTagsWrapper.value
  hasMoreCategoryTags.value = container.scrollWidth > container.clientWidth &&
    container.scrollLeft < (container.scrollWidth - container.clientWidth)
}

const checkMoreTags = () => {
  if (!tagTagsWrapper.value) return

  const container = tagTagsWrapper.value
  hasMoreTags.value = container.scrollWidth > container.clientWidth &&
    container.scrollLeft < (container.scrollWidth - container.clientWidth)
}

// 监听窗口大小变化
const handleResize = () => {
  nextTick(() => {
    checkMoreCategoryTags()
    checkMoreTags()
  })
}

onMounted(() => {
  checkMoreCategoryTags()
  checkMoreTags()
  window.addEventListener('resize', handleResize)
  dify_mapping()
  fetchKnowledgeBases()
})

// 跳转到API文档
const goToApiDocs = (id, event) => {
  event.stopPropagation()

  // 从http.defaults.baseURL构建Redoc URL
  const baseUrl = http.defaults.baseURL || ''

  // 提取域名和端口，替换路径部分为/redoc
  let redocUrl = ''

  redocUrl =


    // 在新窗口中打开Redoc文档
    window.open('http://*************:9003/redoc', '_blank')
}

// 编辑知识库相关
const showEditModalVisible = ref(false)
const editLoading = ref(false)
const editFormRef = ref(null)

const editForm = reactive({
  id: null,
  name: '',
  description: '',
  permission: 'only_me',
  category: ''  // 添加知识库类型字段
})

const editRules = {
  name: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入知识库描述', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  permission: [
    { required: true, message: '请选择知识库权限', trigger: 'change' }
  ]
}

const handleEditSubmit = async () => {
  try {
    await editFormRef.value.validate()
    editLoading.value = true

    // 获取用户信息
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    if (!userInfo.username || !userInfo.userId) {
      message.error('未找到用户信息')
      return
    }

    // 构建请求参数
    const params = {
      username: userInfo.username,
      user_id: userInfo.userId,
      name: editForm.name,
      description: editForm.description,
      permission: editForm.permission,
      uid: editForm.id, // 添加知识库ID
      category: editForm.category  // 添加知识库类型字段
    }

    // 调用编辑知识库接口
    const res = await http.put('/knowledge_api/update_knowledge', params)

    if (res.data.code === 200) {
      message.success('知识库更新成功')
      fetchKnowledgeBases() // 重新获取数据
      handleEditCancel()
    } else {
      message.error(res.data.message || '更新知识库失败')
    }
  } catch (error) {
    console.error('更新知识库失败:', error)
    message.error(error.response?.data?.message || '更新知识库失败')
  } finally {
    editLoading.value = false
  }
}

const handleEditCancel = () => {
  showEditModalVisible.value = false
  editFormRef.value?.resetFields()
}

// 显示编辑模态框
const showEditModal = (kb) => {
  editForm.name = kb.title || kb.name || '';
  editForm.description = kb.description || '';
  editForm.permission = kb.permission || 'only_me';
  editForm.id = kb.id; // 保存知识库ID
  editForm.category = kb.category || '';  // 保存知识库类型
  showEditModalVisible.value = true;
}

// 在script setup中添加计算属性
const placeholderCards = computed(() => {
  const currentCards = filteredKnowledgeBases.value.length
  const totalSlots = 7 // 每页显示7个卡片
  return Math.max(0, totalSlots - currentCards)
})
</script>

<style scoped>
.knowledge-container {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: auto;
  flex: 1;
  height: calc(100% - 48px);
  overflow: hidden;
  margin: 0 auto;
  max-width: 100%;
}

.filter-container {
  display: flex;
  flex-direction: row;
  gap: 16px;
  margin-bottom: 0;
  align-items: center;
  position: relative;
  z-index: 1;
  padding-bottom: 8px;
  border-radius: 8px;
  width: 95%;
  margin: 0 auto 8px auto;
}

.filter-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
}

.filter-section:last-child {
  margin-left: auto;
}

.filter-section:first-child {
  margin-right: 12px;
}

.filter-label {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  white-space: nowrap;
  line-height: 1.2;
  margin-right: 2px;
}

/* 标签容器 */
.tags-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

/* 标签列表样式 */
.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  min-height: 32px;
  padding: 4px 0;
}
/* 新增样式 */
.tag-form-item-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 8px;
}

/* 标签输入框样式 */
.tag-input {
  width: 100%;
  margin-top: 0;
}

/* 使用Ant Design的Tag组件自带样式 */
/* 如果需要进一步自定义Tag样式 */
:deep(.ant-tag) {
  display: inline-flex;
  align-items: center;
  height: 24px;
  line-height: 22px;
  padding: 0 8px;
  border-radius: 4px;
  font-size: 12px;
  transition: all 0.3s;
}

/* 鼠标悬停在标签上的效果 */
:deep(.ant-tag):hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

/* 调整Ant Design表单项的控制输入部分，使其内容对齐 */
:deep(.ant-form-item-control-input) {
  display: flex;
  /* flex-direction: column; */
  align-items: flex-start;
}

/* 调整标签输入框的外边距，使其与“标签”文字对齐 */
:deep(.ant-form-item-control-input-content) {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  margin-top: 2px;
}

/* 确保输入框与标签文字对齐 */
:deep(.ant-form-item-label)>label {
  display: flex;
  align-items: center;
}

/* 标签关闭按钮样式 */
:deep(.ant-tag-close-icon) {
  margin-left: 4px;
  color: rgba(94, 12, 12, 0.7);
  transition: color 0.3s;
}

:deep(.ant-tag-close-icon):hover {
  color: white;
}

/* 强制所有输入框和密码框高度一致 */
:deep(.ant-input),
:deep(.ant-input-password) {
  height: 36x !important;
  line-height: 36px !important;
  padding: 0 12px;
}

/* 密码框内部输入框的特殊调整 */
:deep(.ant-input-password .ant-input) {
  height: 36px !important;
  line-height: 36px !important;
  padding: 0 12px;
}

/* 其他相关样式 */
:deep(.ant-form-item-control) {
  line-height: 36px;
  /* 确保 form item 控制部分的一致性 */
}

.tags-wrapper {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  overflow-x: hidden;
  padding-bottom: 4px;
  scroll-behavior: smooth;
  width: 100%;
  max-width: 360px;
}

.scroll-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  border: 1px solid #e8e8e8;
  background-color: white;
  color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  z-index: 2;
  padding: 0;
  flex-shrink: 0;
}

.scroll-btn:hover {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.8);
}

.scroll-btn.hidden {
  visibility: hidden;
  opacity: 0;
}

.scroll-left {
  margin-right: 4px;
}

.scroll-right {
  margin-left: 4px;
}

.filter-tag {
  cursor: pointer;
  user-select: none;
  margin: 0;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  transition: all 0.3s;
  white-space: nowrap;
  line-height: 1.2;
}

.filter-tag[data-category="文档型"] {
  color: #1890ff;
  border-color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.filter-tag[data-category="代码型"] {
  color: #52c41a;
  border-color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
}

.filter-tag[data-category="图谱型"] {
  color: #722ed1;
  border-color: #722ed1;
  background: rgba(114, 46, 209, 0.1);
}

.filter-tag:not([data-category]) {
  color: #fa8c16;
  border-color: #fa8c16;
  background: rgba(250, 140, 22, 0.1);
}

.filter-tag:hover {
  opacity: 0.8;
}

.filter-tag.active {
  color: white;
}

.filter-tag[data-category="文档类"].active {
  background: #1890ff;
  border-color: #1890ff;
}

.filter-tag[data-category="知识库类"].active {
  background: #722ed1;
  border-color: #722ed1;
}

.filter-tag:not([data-category]).active {
  background: #fa8c16;
  border-color: #fa8c16;
}

.knowledge-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0px;
  width: 100%;
  height: calc(100% - 32px);
  overflow-x: hidden;
  position: relative;
  z-index: 2;
  padding: 0 0;
}

.flex-center {
  display: flex;
  align-items: center;
  gap: 4px;
}

.dify-modal {
  :deep(.ant-modal-content) {
    padding: 0;
    border-radius: 8px;
  }
}

.dify-form {
  padding: 16px;
}

.dify-header {
  text-align: left;
  margin-bottom: 32px;
}

.dify-title {
  h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    color: rgba(0, 0, 0, 0.88);
  }
}

.dify-subtitle {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.wave-icon {
  font-size: 16px;
}

:deep(.ant-form) {
  .ant-form-item {
    margin-bottom: 24px;

    .ant-form-item-row {
      display: flex;
      /* align-items: center; */
      align-items: flex-start;
    }

    .ant-form-item-label {
      padding: 0 8px 0 0;

      label {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.88);
        height: 36px;
        line-height: 36px;
        width: 120px;
      }
    }

    .ant-form-item-control {
      flex: 1;
    }

    /* 针对描述文本域的特殊处理 */
    &.description-item {
      .ant-form-item-row {
        align-items: flex-start;
      }
    }
  }

  /* 调整输入框和选择框的高度 */
  .ant-input {
    height: 36px;
    line-height: 36px;
    padding: 4px 11px;
    font-size: 14px;
  }

  .ant-select {
    .ant-select-selector {
      height: 36px !important;

      .ant-select-selection-search {
        height: 34px;
        line-height: 34px;

        input {
          height: 34px;
        }
      }

      .ant-select-selection-item {
        line-height: 34px;
        height: 34px;
      }

      .ant-select-selection-placeholder {
        line-height: 34px;
      }
    }
  }

  /* 多选选择器的特殊处理 */
  .ant-select-multiple {
    .ant-select-selector {
      min-height: 36px !important;
      height: auto !important;
      padding: 2px 4px !important;

      .ant-select-selection-item {
        height: 24px;
        line-height: 22px;
        margin-top: 3px;
        margin-bottom: 3px;
      }
    }
  }
}

/* 编辑和删除按钮样式 */
.card-container .btn-container {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card-container:hover .btn-container {
  opacity: 1;
}

.action-btn {
  font-size: 16px;
  margin-left: 8px;
  color: #999;
  transition: all 0.3s;
  opacity: 0.6;
}

.action-btn:hover {
  opacity: 1;
  transform: scale(1.1);
}

.card-container:hover .action-btn {
  opacity: 1;
}

.middle-container {
  position: relative;
  flex: 1;
  border-radius: 8px;
  padding: 16px;
  width: 100%;
  overflow-x: auto;
  min-height: 300px;
}

.bottom-container {
  display: none;
}

.search-input {
  width: 300px;

  :deep(.ant-input) {
    height: 32px;
    border-radius: 4px;

    &:hover {
      border-color: #40a9ff;
    }

    &:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  :deep(.ant-input-group-addon) {
    background: transparent;
    border: none;
  }

  :deep(.ant-input-search-button) {
    height: 32px;
    border-radius: 0 4px 4px 0;
  }
}

.card-grid {
  width: 95%;
  margin: 0 auto;
}

.loading-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-icon {
  margin-bottom: 12px;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: #1890ff;
  font-size: 14px;
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 修改占位卡片样式 */
.bg-transparent {
  background-color: transparent;
}

.min-h-\[200px\] {
  min-height: 200px;
}

.opacity-50 {
  opacity: 0.5;
}
</style>
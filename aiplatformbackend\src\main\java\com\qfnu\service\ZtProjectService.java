package com.qfnu.service;

import com.qfnu.model.po.ZtProjectPO;
import com.qfnu.model.vo.ZtProjectVO;
import com.qfnu.model.param.ZtProjectParam;
import com.qfnu.mapper.secondary.ZtProjectMapper;
import com.qfnu.converter.BaseConverter;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

@Service
public class ZtProjectService extends ServiceImpl<ZtProjectMapper, ZtProjectPO> {

    @Resource
    private ZtProjectMapper ztProjectMapper;

    @Resource
    private BaseConverter converter;

    @Transactional(rollbackFor = Exception.class)
    public int save(ZtProjectParam param) {
        ZtProjectPO po = converter.convertToZtProjectPO(param);
        return save(po) ? 1 : 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public int saveBatch(List<ZtProjectParam> paramList) {
        List<ZtProjectPO> poList = converter.convertToZtProjectPO(paramList);
        return saveBatch(poList) ? poList.size() : 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public int update(ZtProjectParam param) {
        ZtProjectPO po = converter.convertToZtProjectPO(param);
        return updateById(po) ? 1 : 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return removeById(id) ? 1 : 0;
    }

    public ZtProjectVO getById(Long id) {
        ZtProjectPO po = super.getById(id);
        if (po == null) {
            return null;
        }
        return converter.convertToZtProjectVO(po);
    }

    public List<ZtProjectVO> getList(ZtProjectParam param) {
        ZtProjectPO po = converter.convertToZtProjectPO(param);
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<ZtProjectPO> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(ZtProjectPO::getParent, 12);

        wrapper.eq(ZtProjectPO::getType, "program");

        List<ZtProjectPO> poList = list(wrapper);
        return converter.convertToZtProjectVO(poList);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<ZtProjectVO> batchAddOrUpdate(List<ZtProjectParam> paramList) {
        List<ZtProjectPO> poList = converter.convertToZtProjectPO(paramList);
        saveOrUpdateBatch(poList);
        return converter.convertToZtProjectVO(poList);
    }


    public List<ZtProjectPO> getListByParentAndType() {
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<ZtProjectPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ZtProjectPO::getParent, 12);
        wrapper.eq(ZtProjectPO::getType, "program");
        wrapper.select(ZtProjectPO::getId, ZtProjectPO::getName);
        List<ZtProjectPO> poList = ztProjectMapper.selectList(wrapper);

        return poList;
    }
}


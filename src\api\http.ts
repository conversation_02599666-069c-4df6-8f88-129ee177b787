import axios from "axios";
import { reqReject, reqResolve, resReject, resResolve } from "./interceptors";

export function createAxios(options = {}) {
  const defaultOptions = {
    baseURL: "",
    timeout: 300000,
  };
  const instance = axios.create({
    ...defaultOptions,
    ...options,
  });
  instance.interceptors.request.use(reqResolve, reqReject);
  instance.interceptors.response.use(resResolve, resReject);
  return instance;
}

export const defAxios = createAxios({ baseURL: "/itr/v1" });
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qfnu.mapper.primary.SysUserManagementMapper">

    <resultMap id="BaseResultMap" type="com.qfnu.model.po.SysUserManagementPO">
                <id column="management_id" property="managementId"/>
        <result column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="account_name" property="accountName"/>
        <result column="real_name" property="realName"/>
        <result column="user_type" property="userType"/>
        <result column="role" property="role"/>
        <result column="product" property="product"/>
        <result column="job_no" property="jobNo"/>

    </resultMap>

    <sql id="Base_Column_List">
        management_id, user_id, username, account_name, real_name, user_type, role, product
    </sql>
    <select id="getPageList" resultType="com.qfnu.model.po.SysUserManagementPO">
        SELECT
            sum.management_id,
            sum.user_id,
            sum.username,
            sum.account_name,
            sum.real_name,
            sum.user_type,
            sum.role,
            sum.product,
            su.job_no
        FROM sys_user_management sum
        LEFT JOIN sys_users su ON sum.user_id = su.user_id
        <where>
            <if test="param.usernameLike != null and param.usernameLike != ''">
                AND sum.username LIKE CONCAT('%', #{param.usernameLike}, '%')
            </if>
            <if test="param.typeList != null and param.typeList.size() > 0">
                AND sum.user_type IN
                <foreach collection="param.typeList" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="param.productList != null and param.productList.size() > 0">
                AND sum.product IN
                <foreach collection="param.productList" item="product" open="(" separator="," close=")">
                    #{product}
                </foreach>
            </if>
            <if test="param.permissionList != null and param.permissionList.size() > 0">
                AND sum.role IN
                <foreach collection="param.permissionList" item="permission" open="(" separator="," close=")">
                    #{permission}
                </foreach>
            </if>
        </where>
    </select>


    <update id="myUpdateById" parameterType="com.qfnu.model.po.SysUserManagementPO">
        UPDATE sys_user_management
        <set>
            <if test="realName != null">
                real_name = #{realName},
            </if>
            <if test="userType != null">
                user_type = #{userType},
            </if>
            <if test="product != null">
                product = #{product},
            </if>
            <if test="role != null">
                role = #{role}
            </if>
        </set>
        WHERE management_id = #{managementId}
    </update>

    <delete id="myDeleteById" parameterType="string">
        delete from sys_user_management where management_id = #{managementId}
    </delete>


    <select id="getUserById" resultType="com.qfnu.model.po.SysUserManagementPO">
        select * from sys_user_management where user_id = #{userId}
    </select>



</mapper>
package com.qfnu.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("sys_users")
@ApiModel(value = "SysUsersPO", description = "SysUsers实体类")
public class SysUsersPO  implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 用户唯一标识，自增主键
     */
    @TableField(value = "user_id")
    @ApiModelProperty(value = "用户唯一标识，自增主键")
    private String userId;

    /**
     * 用户名，唯一
     */
    @TableField(value = "username")
    @ApiModelProperty(value = "用户名，唯一")
    private String username;

    /**
     * 用户密码
     */
    @TableField(value = "password")
    @ApiModelProperty(value = "用户密码")
    private String password;

    /**
     * 用户邮箱
     */
    @TableField(value = "email")
    @ApiModelProperty(value = "用户邮箱")
    private String email;

    /**
     * 工号
     */
    @TableField(value = "job_no")
    @ApiModelProperty(value = "工号")
    private String jobNo;

    /**
     * 团队名称（JSON格式存储多个团队）
     */
    @TableField(value = "team_names")
    @ApiModelProperty(value = "团队名称（JSON格式存储多个团队）")
    private String teamNames;

    /**
     * 用户名字
     */
    @TableField(value = "first_name")
    @ApiModelProperty(value = "用户名字")
    private String firstName;

    /**
     * 用户姓氏
     */
    @TableField(value = "last_name")
    @ApiModelProperty(value = "用户姓氏")
    private String lastName;

    /**
     * 是否为超级用户，0 表示否，1 表示是
     */
    @TableField(value = "is_superuser")
    @ApiModelProperty(value = "是否为超级用户，0 表示否，1 表示是")
    private String isSuperuser;

    /**
     * 是否为工作人员，0 表示否，1 表示是
     */
    @TableField(value = "is_staff")
    @ApiModelProperty(value = "是否为工作人员，0 表示否，1 表示是")
    private String isStaff;

    /**
     * 用户是否活跃，0 表示禁用，1 表示活跃
     */
    @TableField(value = "is_active")
    @ApiModelProperty(value = "用户是否活跃，0 表示禁用，1 表示活跃")
    private String isActive;

    /**
     * 用户注册时间
     */
    @TableField(value = "date_joined")
    @ApiModelProperty(value = "用户注册时间")
    private Date dateJoined;

    /**
     * 用户最后登录时间
     */
    @TableField(value = "last_login")
    @ApiModelProperty(value = "用户最后登录时间")
    private Date lastLogin;


} 
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | INFO | 成功查询知识库 c9de9ac1-9845-4fe5-99a0-133ae1f6c1fb 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0-资产+授权_平台导出 - 副本.json']
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-08-06 09:05:39 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))

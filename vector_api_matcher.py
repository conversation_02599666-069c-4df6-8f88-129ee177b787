#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里百炼向量化API匹配脚本
使用通用文本向量-v4模型进行接口描述与接口文档的向量化匹配
"""

import json
import numpy as np
from typing import List, Dict
import requests
import os
from sklearn.metrics.pairwise import cosine_similarity


class DashScopeVectorService:
    """阿里百炼向量化服务"""
    
    def __init__(self, api_key: str = None):
        """
        初始化向量化服务

        Args:
            api_key: 阿里百炼API密钥，如果不提供则从环境变量DASHSCOPE_API_KEY获取
        """
        self.api_key = api_key or os.getenv('DASHSCOPE_API_KEY')
        if not self.api_key:
            raise ValueError("请设置DASHSCOPE_API_KEY环境变量或传入api_key参数")

        self.base_url = "https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding"
        self.model = "text-embedding-v2"  # 使用v2版本，更稳定
        
    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        获取文本向量

        Args:
            texts: 文本列表

        Returns:
            List[List[float]]: 向量列表
        """
        # 限制批量大小，避免请求过大
        max_batch_size = 10
        all_embeddings = []

        for i in range(0, len(texts), max_batch_size):
            batch_texts = texts[i:i + max_batch_size]
            batch_embeddings = self._get_batch_embeddings(batch_texts)
            all_embeddings.extend(batch_embeddings)

        return all_embeddings

    def _get_batch_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        获取一批文本的向量
        """
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        payload = {
            "model": self.model,
            "input": {
                "texts": texts
            }
        }
        
        try:
            response = requests.post(self.base_url, headers=headers, json=payload)

            if response.status_code != 200:
                raise Exception(f"HTTP {response.status_code}: {response.text}")

            result = response.json()

            # 检查响应格式
            if 'output' not in result:
                raise Exception(f"响应格式错误，缺少output字段: {result}")

            if 'embeddings' not in result['output']:
                raise Exception(f"响应格式错误，缺少embeddings字段: {result['output']}")

            # 提取向量数据
            embeddings = []
            for item in result['output']['embeddings']:
                embeddings.append(item['embedding'])

            return embeddings

        except requests.exceptions.RequestException as e:
            raise Exception(f"网络请求失败: {str(e)}")
        except json.JSONDecodeError as e:
            raise Exception(f"JSON解析失败: {str(e)}")
        except Exception as e:
            raise Exception(f"获取向量失败: {str(e)}")


class VectorAPIMatching:
    """向量化API匹配器"""
    
    def __init__(self, api_key: str = None):
        """
        初始化匹配器
        
        Args:
            api_key: 阿里百炼API密钥
        """
        self.vector_service = DashScopeVectorService(api_key)
        
    def prepare_api_text(self, api_data: Dict) -> str:
        """
        准备API数据的文本表示

        Args:
            api_data: API数据字典，包含method, path, summary等字段

        Returns:
            str: 格式化的文本表示
        """
        method = api_data.get('method', '').upper()
        path = api_data.get('path', '')
        summary = api_data.get('summary', '')

        # 构建结构化的文本表示，便于向量化
        text_parts = []

        if method:
            text_parts.append(f"方法: {method}")
        if path:
            # 限制路径长度
            if len(path) > 100:
                path = path[:100] + "..."
            text_parts.append(f"路径: {path}")
        if summary:
            # 限制描述长度
            if len(summary) > 100:
                summary = summary[:100] + "..."
            text_parts.append(f"描述: {summary}")

        text = " | ".join(text_parts)

        # 确保总长度不超过限制
        if len(text) > 200:
            text = text[:200] + "..."

        return text
    
    def find_best_matching_apis(self, 
                               api_description: str, 
                               api_documents: List[Dict],
                               top_k: int = 5,
                               similarity_threshold: float = 0.7) -> List[Dict]:
        """
        找到与接口描述最匹配的API
        
        Args:
            api_description: 接口描述文本
            api_documents: API文档列表，每个元素包含method, path, summary等字段
            top_k: 返回前k个最相似的结果
            similarity_threshold: 相似度阈值，低于此值的结果将被过滤
            
        Returns:
            List[Dict]: 匹配结果列表，包含原始API数据和相似度分数
        """
        if not api_documents:
            return []
        
        print(f"开始向量化匹配，输入描述: {api_description}")
        print(f"待匹配API数量: {len(api_documents)}")
        
        # 准备所有需要向量化的文本
        texts_to_embed = [api_description]  # 第一个是查询描述
        api_texts = []
        
        for api_doc in api_documents:
            api_text = self.prepare_api_text(api_doc)
            api_texts.append(api_text)
            texts_to_embed.append(api_text)
        
        print("正在调用阿里百炼向量化服务...")
        
        try:
            # 批量获取向量
            embeddings = self.vector_service.get_embeddings(texts_to_embed)
            
            # 分离查询向量和API向量
            query_embedding = np.array(embeddings[0]).reshape(1, -1)
            api_embeddings = np.array(embeddings[1:])
            
            print(f"向量化完成，查询向量维度: {query_embedding.shape}")
            print(f"API向量矩阵维度: {api_embeddings.shape}")
            
            # 计算余弦相似度
            similarities = cosine_similarity(query_embedding, api_embeddings)[0]
            
            # 构建结果列表
            results = []
            for i, (api_doc, similarity) in enumerate(zip(api_documents, similarities)):
                if similarity >= similarity_threshold:
                    result = {
                        'api_data': api_doc,
                        'similarity_score': float(similarity),
                        'api_text': api_texts[i],
                        'rank': 0  # 将在排序后设置
                    }
                    results.append(result)
            
            # 按相似度排序
            results.sort(key=lambda x: x['similarity_score'], reverse=True)
            
            # 设置排名并取前k个
            for i, result in enumerate(results[:top_k]):
                result['rank'] = i + 1
            
            print(f"匹配完成，找到 {len(results)} 个相似度 >= {similarity_threshold} 的结果")
            print(f"返回前 {min(top_k, len(results))} 个结果")
            
            return results[:top_k]
            
        except Exception as e:
            print(f"向量化匹配失败: {str(e)}")
            return []
    
    def format_matching_results(self, results: List[Dict]) -> str:
        """
        格式化匹配结果为可读的字符串
        
        Args:
            results: 匹配结果列表
            
        Returns:
            str: 格式化的结果字符串
        """
        if not results:
            return "未找到匹配的API"
        
        output_lines = ["=== 向量化匹配结果 ==="]
        
        for result in results:
            api_data = result['api_data']
            similarity = result['similarity_score']
            rank = result['rank']
            
            output_lines.append(f"\n排名 {rank} (相似度: {similarity:.4f}):")
            output_lines.append(f"  方法: {api_data.get('method', 'N/A')}")
            output_lines.append(f"  路径: {api_data.get('path', 'N/A')}")
            output_lines.append(f"  描述: {api_data.get('summary', 'N/A')}")
            output_lines.append(f"  文本表示: {result['api_text']}")
        
        return "\n".join(output_lines)


def load_api_docs_from_json(file_path: str) -> List[Dict]:
    """从JSON文件加载API文档"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 如果是包装在某个键下的数据，尝试提取
        if isinstance(data, dict):
            # 尝试常见的键名
            for key in ['apis', 'data', 'paths', 'endpoints']:
                if key in data and isinstance(data[key], list):
                    return data[key]
            # 如果没有找到列表，返回空
            return []
        elif isinstance(data, list):
            return data
        else:
            return []

    except Exception as e:
        print(f"加载API文档失败: {str(e)}")
        return []


def main():
    """主函数，演示向量化匹配功能"""

    # 设置API密钥
    api_key = "sk-fdfd70db7f9b48b4b8cad01c56c3fe99"

    # 从本地JSON文件加载API文档
    json_file_path = "api_docs.json"  # 请将您的JSON文件命名为api_docs.json

    api_docs = load_api_docs_from_json(json_file_path)

    if not api_docs:
        print(f"未能从 {json_file_path} 加载API文档，使用示例数据")
        # 如果加载失败，使用示例数据
        api_docs = [
            {
                "method": "POST",
                "path": "/openapi/v1.0/project/projects",
                "summary": "项目创建"
            },
            {
                "method": "POST",
                "path": "/openapi/v1.0/project/projects/{projectId}/approve_rules",
                "summary": "审批规则创建"
            },
            {
                "method": "POST",
                "path": "/openapi/v1.0/project/projects/{projectId}/assets/{assetId}/accounts",
                "summary": "资产账号创建"
            },
            {
                "method": "POST",
                "path": "/openapi/v1.0/project/projects/{projectId}/password_plans:add_accounts",
                "summary": "关联改密计划账号"
            },
            {
                "method": "GET",
                "path": "/openapi/v1.0/project/projects",
                "summary": "项目列表查询"
            }
        ]

    print(f"成功加载 {len(api_docs)} 个API文档")

    # 测试查询描述 - 您提供的前置依赖
    query_description = "创建项目（projectId）、创建账号（accountIds）、创建改密计划（passwordPlanIds）"
    
    try:
        # 初始化匹配器，传入API密钥
        matcher = VectorAPIMatching(api_key=api_key)

        # 执行匹配
        results = matcher.find_best_matching_apis(
            api_description=query_description,
            api_documents=api_docs,
            top_k=5,
            similarity_threshold=0.5
        )
        
        # 输出结果
        formatted_results = matcher.format_matching_results(results)
        print(formatted_results)
        
        # 输出JSON格式结果
        print("\n=== JSON格式结果 ===")
        json_results = {
            "query": query_description,
            "matches": [
                {
                    "method": result['api_data']['method'],
                    "path": result['api_data']['path'], 
                    "summary": result['api_data']['summary'],
                    "similarity_score": result['similarity_score'],
                    "rank": result['rank']
                }
                for result in results
            ],
            "total": len(results)
        }
        print(json.dumps(json_results, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"执行失败: {str(e)}")


def extract_api_summaries_to_file(json_file: str = "api_docs.json", output_file: str = "api_summaries.txt"):
    """
    从JSON文件中提取所有API的summary字段并保存到文件

    Args:
        json_file: 输入的JSON文件路径
        output_file: 输出的文本文件路径

    Returns:
        list: 提取的summary列表
    """
    print(f"=== 提取API Summary字段 ===")
    print(f"输入文件: {json_file}")
    print(f"输出文件: {output_file}")

    try:
        # 加载JSON文件
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 提取API列表
        api_docs = []
        if isinstance(data, dict):
            if "apis" in data:
                api_docs = data["apis"]
            elif "paths" in data:
                # OpenAPI格式
                for path, methods in data["paths"].items():
                    for method, details in methods.items():
                        if method.lower() in ['get', 'post', 'put', 'delete', 'patch']:
                            api_docs.append({
                                "method": method.upper(),
                                "path": path,
                                "summary": details.get('summary', ''),
                                "description": details.get('description', '')
                            })
        elif isinstance(data, list):
            api_docs = data

        print(f"✓ 成功加载 {len(api_docs)} 个API")

        # 提取summary字段
        summaries = []
        for i, api in enumerate(api_docs, 1):
            method = api.get('method', 'N/A')
            path = api.get('path', 'N/A')
            summary = api.get('summary', '').strip()

            if summary:
                summaries.append({
                    'index': i,
                    'method': method,
                    'path': path,
                    'summary': summary
                })

        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"# API接口Summary字段提取结果\n")
            f.write(f"# 数据源: {json_file}\n")
            f.write(f"# 总API数量: {len(api_docs)}\n")
            f.write(f"# 有效Summary数量: {len(summaries)}\n\n")

            # 写入完整信息
            f.write("## 完整API列表\n\n")
            for item in summaries:
                f.write(f"{item['index']:3d}. [{item['method']}] {item['summary']}\n")
                f.write(f"     路径: {item['path']}\n\n")

            # 写入纯summary文本
            f.write("\n## 纯Summary文本（每行一个）\n\n")
            for item in summaries:
                f.write(f"{item['summary']}\n")

        print(f"✓ 成功提取 {len(summaries)} 个Summary字段")
        print(f"✓ 结果已保存到: {output_file}")

        # 显示统计信息
        method_count = {}
        keyword_count = {}
        keywords = ['创建', '查询', '更新', '删除', '列表', '获取', '批量', '关联']

        for item in summaries:
            # 统计HTTP方法
            method = item['method']
            method_count[method] = method_count.get(method, 0) + 1

            # 统计关键词
            summary = item['summary']
            for keyword in keywords:
                if keyword in summary:
                    keyword_count[keyword] = keyword_count.get(keyword, 0) + 1

        print("\nHTTP方法统计:")
        for method, count in sorted(method_count.items()):
            print(f"  {method}: {count} 个")

        print("\n关键词统计:")
        for keyword, count in sorted(keyword_count.items(), key=lambda x: x[1], reverse=True):
            print(f"  {keyword}: {count} 个")

        # 显示前10个summary
        print(f"\n前10个Summary:")
        for item in summaries[:10]:
            print(f"  {item['index']}. [{item['method']}] {item['summary']}")

        if len(summaries) > 10:
            print(f"  ... 还有 {len(summaries) - 10} 个")

        return [item['summary'] for item in summaries]

    except FileNotFoundError:
        print(f"✗ 文件不存在: {json_file}")
        return []
    except json.JSONDecodeError as e:
        print(f"✗ JSON解析失败: {str(e)}")
        return []
    except Exception as e:
        print(f"✗ 提取失败: {str(e)}")
        return []


def test_api_connection():
    """测试API连接"""
    api_key = "sk-fdfd70db7f9b48b4b8cad01c56c3fe99"

    try:
        service = DashScopeVectorService(api_key)

        # 测试简单的文本
        test_texts = ["测试文本"]
        print("测试API连接...")

        embeddings = service.get_embeddings(test_texts)
        print(f"✓ API调用成功，返回向量维度: {len(embeddings[0])}")

    except Exception as e:
        print(f"✗ API调用失败: {str(e)}")


if __name__ == "__main__":
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "extract":
        # 提取summary模式
        input_file = sys.argv[2] if len(sys.argv) > 2 else "api_docs.json"
        output_file = sys.argv[3] if len(sys.argv) > 3 else "api_summaries.txt"
        extract_api_summaries_to_file(input_file, output_file)
    else:
        # 正常模式
        # 先测试API连接
        test_api_connection()
        print("\n" + "="*50 + "\n")

        # 再运行主程序
        main()

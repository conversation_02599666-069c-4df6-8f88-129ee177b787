<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qfnu.mapper.secondary.ZtProjectMapper">

    <resultMap id="BaseResultMap" type="com.qfnu.model.po.ZtProjectPO">
                <id column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="path" property="path"/>
        <result column="grade" property="grade"/>
        <result column="project" property="project"/>
        <result column="name" property="name"/>
        <result column="parent" property="parent"/>
        <result column="status" property="status"/>
        <result column="substatus" property="substatus"/>
        <result column="pri" property="pri"/>
        <result column="desc" property="desc"/>
        <result column="order" property="order"/>
        <result column="deleted" property="deleted"/>
        <result column="begin" property="begin"/>
        <result column="end" property="end"/>
        <result column="PM" property="PM"/>
        <result column="model" property="model"/>

    </resultMap>

    <sql id="Base_Column_List">
        id, type, path, grade, project, name, parent, status, substatus, pri, desc, order, deleted, begin, end, PM, model
    </sql>


    <select id="selectByPrimaryKeyAndName" parameterType="java.util.Map" resultMap="BaseResultMap">
        select * from zt_project where parent = #{parent} and type = #{type}
    </select>


</mapper> 
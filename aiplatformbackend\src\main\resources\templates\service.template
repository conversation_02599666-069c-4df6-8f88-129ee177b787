package ${package}.service;

import ${package}.model.po.${className}PO;
import ${package}.model.dto.${className}DTO;
import ${package}.model.vo.${className}VO;
import ${package}.model.param.${className}Param;
import ${package}.mapper.${className}Mapper;
import ${package}.converter.BaseConverter;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

@Service
public class ${className}Service extends ServiceImpl<${className}Mapper, ${className}PO> {

    @Resource
    private ${className}Mapper ${serviceVarName}Mapper;
    
    @Resource
    private BaseConverter converter;
    
    @Transactional(rollbackFor = Exception.class)
    public int save(${className}Param param) {
        ${className}PO po = converter.convertTo${className}PO(param);
        return save(po) ? 1 : 0;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public int saveBatch(List<${className}Param> paramList) {
        List<${className}PO> poList = converter.convertTo${className}PO(paramList);
        return saveBatch(poList) ? poList.size() : 0;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public int update(${className}Param param) {
        ${className}PO po = converter.convertTo${className}PO(param);
        return updateById(po) ? 1 : 0;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return removeById(id) ? 1 : 0;
    }
    
    public ${className}VO getById(Long id) {
        ${className}PO po = super.getById(id);
        if (po == null) {
            return null;
        }
        return converter.convertTo${className}VO(po);
    }
    
    public List<${className}VO> getList(${className}Param param) {
        ${className}PO po = converter.convertTo${className}PO(param);
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<${className}PO> wrapper = new LambdaQueryWrapper<>();
        // TODO: 根据实际需求添加查询条件
        List<${className}PO> poList = list(wrapper);
        return converter.convertTo${className}VO(poList);
    }
    
    @Transactional(rollbackFor = Exception.class)
    public List<${className}VO> batchAddOrUpdate(List<${className}Param> paramList) {
        List<${className}PO> poList = converter.convertTo${className}PO(paramList);
        saveOrUpdateBatch(poList);
        return converter.convertTo${className}VO(poList);
    }
} 
package com.qfnu.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("排行榜数据VO")
public class TopRankingsVO {

    @ApiModelProperty("热门工具TOP5")
    private List<ToolRankVO> hotTools;

    @ApiModelProperty("AI应用贡献达人TOP5")
    private List<ContributorRankVO> topContributors;

    @ApiModelProperty("好评工具TOP5")
    private List<ToolRankVO> topRatedTools;

    @Data
    public static class ToolRankVO {
        @ApiModelProperty("工具ID")
        private String toolId;

        @ApiModelProperty("工具名称")
        private String toolName;

        @ApiModelProperty("工具类别")
        private String category;

        @ApiModelProperty("使用次数")
        private Integer usageCount;

        @ApiModelProperty("评分")
        private Double rating;
    }

    @Data
    public static class ContributorRankVO {
        @ApiModelProperty("用户ID")
        private String userId;

        @ApiModelProperty("用户名称")
        private String userName;

        @ApiModelProperty("用户组别")
        private String groupName;

        @ApiModelProperty("贡献AI应用数量")
        private Integer aiToolCount;
    }
}
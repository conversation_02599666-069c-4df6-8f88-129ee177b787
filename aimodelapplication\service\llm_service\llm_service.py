#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
大语言模型服务类
封装所有与大语言模型相关的调用逻辑
"""

import requests
import time
from typing import Dict, Any, Optional
from fastapi import HTTPException
from logger import logging
from ..config.llm_config import llm_config

logger = logging()

class LLMService:
    """大语言模型服务类"""
    
    def __init__(self):
        """初始化LLM服务"""
        self.config = llm_config.get_api_config()
        self.api_key = self.config["api_key"]
        self.api_url = self.config["api_url"]
        self.default_model = self.config["default_model"]
    
    def call_llm(self, 
                 prompt: str, 
                 task_type: str = "prerequisite_analysis",
                 model: Optional[str] = None) -> str:
        """
        调用大语言模型
        
        Args:
            prompt: 提示词
            task_type: 任务类型 (prerequisite_analysis, api_extraction, relation_extraction)
            model: 模型名称，如果不指定则使用默认模型
            
        Returns:
            str: 模型返回结果
            
        Raises:
            HTTPException: 调用失败时抛出异常
        """
        if not model:
            model = self.default_model
        
        # 获取任务特定的模型参数
        model_params = llm_config.get_model_parameters(task_type)
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model,
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            },
            "parameters": model_params
        }
        
        # 重试机制
        max_retries = llm_config.MAX_RETRIES
        retry_delay = llm_config.RETRY_DELAY
        
        for attempt in range(max_retries):
            try:
                logger.info(f"调用大语言模型 (任务类型: {task_type}, 尝试: {attempt + 1}/{max_retries})")
                
                response = requests.post(
                    self.api_url, 
                    headers=headers, 
                    json=data, 
                    timeout=llm_config.REQUEST_TIMEOUT
                )
                response.raise_for_status()

                result = response.json()
                logger.debug(f"API响应: {result}")

                # 处理不同的API响应格式
                if result.get("output"):
                    output = result["output"]

                    # 新格式：直接返回text字段
                    if "text" in output:
                        return output["text"]

                    # 旧格式：choices数组格式
                    elif "choices" in output and output["choices"]:
                        return output["choices"][0]["message"]["content"]

                    else:
                        raise Exception(f"API返回格式异常: {result}")
                else:
                    raise Exception(f"API返回格式异常: {result}")

            except requests.exceptions.RequestException as e:
                logger.error(f"调用大语言模型失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                
                if attempt < max_retries - 1:
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                else:
                    raise HTTPException(
                        status_code=500, 
                        detail=f"调用大语言模型失败: {str(e)}"
                    )
            
            except Exception as e:
                logger.error(f"处理API响应失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                
                if attempt < max_retries - 1:
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                else:
                    raise HTTPException(
                        status_code=500, 
                        detail=f"处理模型响应失败: {str(e)}"
                    )
    
    def call_prerequisite_analysis(self, prompt: str) -> str:
        """
        调用前置依赖分析
        
        Args:
            prompt: 分析提示词
            
        Returns:
            str: 分析结果
        """
        return self.call_llm(prompt, task_type="prerequisite_analysis")
    
    def call_api_extraction(self, prompt: str) -> str:
        """
        调用API精准提取
        
        Args:
            prompt: 提取提示词
            
        Returns:
            str: 提取结果
        """
        return self.call_llm(prompt, task_type="api_extraction")
    
    def call_relation_extraction(self, prompt: str) -> str:
        """
        调用API关系提取
        
        Args:
            prompt: 关系提取提示词
            
        Returns:
            str: 关系提取结果
        """
        return self.call_llm(prompt, task_type="relation_extraction")

# 创建全局LLM服务实例
llm_service = LLMService()

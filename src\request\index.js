import { defAxios as axios } from "@/request/http/index.js"

//从index.js 导出各模块的API
export * from "./test"

export function getApi(url, parameter, option) {
  return axios({
    url,
    method: "get",
    params: parameter,
    ...option,
  })
}

export function createApi(url, parameter, config = {}) {
  return axios({
    url,
    method: "post",
    data: parameter,
    ...config,
  })
}

export function updateApi(url, data, parameter) {
  return axios({
    url,
    method: "put",
    parameter: parameter,
    data: data,
  })
}

export function deleteApi(url, data, parameter) {
  return axios({
    url,
    method: "delete",
    params: parameter,
    data: data,
  })
}

//下载文件
export function getDownLoadFileBlob(url, parameter) {
  return axios({
    url,
    method: "get",
    params: parameter,
    responseType: "blob",
  })
}

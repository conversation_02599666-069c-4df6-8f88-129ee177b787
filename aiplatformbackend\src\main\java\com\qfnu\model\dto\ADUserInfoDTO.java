package com.qfnu.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@ApiModel(value = "OAuthUserInfoDTO", description = "OAuth2.0用户信息传输对象")
public class ADUserInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户唯一标识（OpenID Connect标准）")
    private String sub;

    @ApiModelProperty("用户名（preferred_username）")
    private String preferredUsername;

    @ApiModelProperty("用户账号")
    private String account;

    @ApiModelProperty("用户姓名")
    private String name;

    @ApiModelProperty("用户全名")
    private String fullName;

    @ApiModelProperty("手机号码")
    private String mobile;

    @ApiModelProperty("工号")
    private String jobNo;

    @ApiModelProperty("用户邮箱")
    private String email;

    @ApiModelProperty("用户状态：1正常，2停用")
    private Integer status;

    @ApiModelProperty("性别：1男，2女")
    private String gender;

    @ApiModelProperty("部门ID列表")
    private List<String> teamIds;

    @ApiModelProperty("主要部门ID")
    private String mainTeamIds;

    @ApiModelProperty("团队名称（JSON格式或字符串）")
    private String teamNames;

    @ApiModelProperty("昵称")
    private String nickName;

    @ApiModelProperty("账户ID")
    private String accountId;

    @ApiModelProperty("来源ID")
    private String sourceId;
}

package com.qfnu.controller;

import com.qfnu.common.ResponseVO;
import com.qfnu.model.po.ZtProjectPO;
import com.qfnu.service.ZtProjectService;
import com.qfnu.model.vo.ZtProjectVO;
import com.qfnu.model.dto.ZtProjectDTO;
import com.qfnu.model.param.ZtProjectParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

@Api(tags = "ZtProject接口")
@RestController
@RequestMapping("/api/ZtProject")
public class ZtProjectController {
    
    @Resource
    private ZtProjectService ztProjectService;
    
//    @ApiOperation("新增")
//    @PostMapping("/save")
//    public ResponseVO<Integer> save(@RequestBody ZtProjectParam param) {
//        return ResponseVO.success(ztProjectService.save(param));
//    }
//
//    @ApiOperation("批量新增")
//    @PostMapping("/saveBatch")
//    public ResponseVO<Integer> saveBatch(@RequestBody List<ZtProjectParam> paramList) {
//        return ResponseVO.success(ztProjectService.saveBatch(paramList));
//    }
//
//    @ApiOperation("更新")
//    @PostMapping("/update")
//    public ResponseVO<Integer> update(@RequestBody ZtProjectParam param) {
//        return ResponseVO.success(ztProjectService.update(param));
//    }
//
//    @ApiOperation("删除")
//    @PostMapping("/delete")
//    public ResponseVO<Integer> delete(@RequestBody ZtProjectParam param) {
//        return ResponseVO.success(ztProjectService.delete(param.getId()));
//    }
//
//    @ApiOperation("根据ID查询")
//    @PostMapping("/getById")
//    public ResponseVO<ZtProjectVO> getById(@RequestBody ZtProjectParam param) {
//        return ResponseVO.success(ztProjectService.getById(param.getId()));
//    }
//
//    @ApiOperation("列表查询")
//    @PostMapping("/list")
//    public ResponseVO<List<ZtProjectVO>> getList(@RequestBody ZtProjectParam param) {
//        return ResponseVO.success(ztProjectService.getList(param));
//    }
//
//    @ApiOperation("批量新增或更新")
//    @PostMapping("/batchAddOrUpdate")
//    public ResponseVO<List<ZtProjectVO>> batchAddOrUpdate(@RequestBody List<ZtProjectParam> paramList) {
//        return ResponseVO.success(ztProjectService.batchAddOrUpdate(paramList));
//    }

    @ApiOperation("根据parent和type获取列表数据")
    @GetMapping("/getListByParentAndType")
    public ResponseVO<List<ZtProjectPO>> getListByParentAndType() {
        return ResponseVO.success(ztProjectService.getListByParentAndType(),200);
    }



} 
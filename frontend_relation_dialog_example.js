// 前端关系确认弹窗示例代码
// 展示如何在关系确认弹窗中显示task_id列

// 1. 获取API数据模型关系列表的API调用示例
async function getApiDataModelRelations(knowledgeBaseId, page = 1, pageSize = 10, status = null) {
    try {
        let url = `/api_data_model_relations/${knowledgeBaseId}?page=${page}&page_size=${pageSize}`;
        if (status) {
            url += `&status=${status}`;
        }
        
        const response = await fetch(url);
        const result = await response.json();
        
        if (result.code === 200) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('获取关系列表失败:', error);
        throw error;
    }
}

// 2. 关系确认弹窗的表格列定义（以Element UI为例）
const relationTableColumns = [
    {
        type: 'selection',
        width: 55
    },
    {
        prop: 'id',
        label: 'ID',
        width: 80
    },
    {
        prop: 'task_id',
        label: '任务ID',
        width: 120,
        formatter: (row) => {
            return row.task_id || '-';
        },
        sortable: true
    },
    {
        prop: 'upstream_api',
        label: '上游API',
        width: 200,
        formatter: (row) => {
            if (row.upstream_api) {
                return `${row.upstream_api.method} ${row.upstream_api.path}`;
            }
            return '-';
        }
    },
    {
        prop: 'data_model',
        label: '数据模型',
        width: 150,
        formatter: (row) => {
            return row.data_model?.name || '-';
        }
    },
    {
        prop: 'downstream_api',
        label: '下游API',
        width: 200,
        formatter: (row) => {
            if (row.downstream_api) {
                return `${row.downstream_api.method} ${row.downstream_api.path}`;
            }
            return '-';
        }
    },
    {
        prop: 'relation_description',
        label: '关系描述',
        minWidth: 250,
        showOverflowTooltip: true
    },
    {
        prop: 'status',
        label: '状态',
        width: 100,
        formatter: (row) => {
            const statusMap = {
                0: '待确认',
                1: '已确认',
                2: '已忽略'
            };
            return statusMap[row.status] || '未知';
        }
    },
    {
        prop: 'created_at',
        label: '创建时间',
        width: 160,
        formatter: (row) => {
            return row.created_at ? new Date(row.created_at).toLocaleString() : '-';
        }
    }
];

// 3. Vue组件示例（关系确认弹窗）
const RelationConfirmDialog = {
    template: `
        <el-dialog
            title="关系确认"
            :visible.sync="visible"
            width="90%"
            :before-close="handleClose"
        >
            <div class="relation-dialog-content">
                <!-- 筛选条件 -->
                <div class="filter-bar">
                    <el-select v-model="statusFilter" placeholder="选择状态" clearable>
                        <el-option label="待确认" value="0"></el-option>
                        <el-option label="已确认" value="1"></el-option>
                        <el-option label="已忽略" value="2"></el-option>
                    </el-select>
                    <el-input 
                        v-model="taskIdFilter" 
                        placeholder="输入任务ID筛选" 
                        clearable
                        style="width: 200px; margin-left: 10px;"
                    ></el-input>
                    <el-button @click="loadRelations" type="primary">查询</el-button>
                </div>
                
                <!-- 关系列表表格 -->
                <el-table
                    :data="relations"
                    @selection-change="handleSelectionChange"
                    style="width: 100%; margin-top: 20px;"
                    max-height="400"
                >
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column prop="id" label="ID" width="80"></el-table-column>
                    <el-table-column prop="task_id" label="任务ID" width="120">
                        <template slot-scope="scope">
                            <span>{{ scope.row.task_id || '-' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="上游API" width="200">
                        <template slot-scope="scope">
                            <span v-if="scope.row.upstream_api">
                                {{ scope.row.upstream_api.method }} {{ scope.row.upstream_api.path }}
                            </span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="数据模型" width="150">
                        <template slot-scope="scope">
                            <span>{{ scope.row.data_model?.name || '-' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="下游API" width="200">
                        <template slot-scope="scope">
                            <span v-if="scope.row.downstream_api">
                                {{ scope.row.downstream_api.method }} {{ scope.row.downstream_api.path }}
                            </span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="relation_description" label="关系描述" min-width="250" show-overflow-tooltip></el-table-column>
                    <el-table-column label="状态" width="100">
                        <template slot-scope="scope">
                            <el-tag :type="getStatusType(scope.row.status)">
                                {{ getStatusText(scope.row.status) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="created_at" label="创建时间" width="160">
                        <template slot-scope="scope">
                            <span>{{ formatTime(scope.row.created_at) }}</span>
                        </template>
                    </el-table-column>
                </el-table>
                
                <!-- 分页 -->
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    style="margin-top: 20px; text-align: right;"
                >
                </el-pagination>
            </div>
            
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="confirmRelations" :disabled="selectedRelations.length === 0">
                    确认选中关系 ({{ selectedRelations.length }})
                </el-button>
            </span>
        </el-dialog>
    `,
    props: {
        visible: Boolean,
        knowledgeBaseId: String
    },
    data() {
        return {
            relations: [],
            selectedRelations: [],
            currentPage: 1,
            pageSize: 10,
            total: 0,
            statusFilter: '',
            taskIdFilter: '',
            loading: false
        };
    },
    watch: {
        visible(val) {
            if (val) {
                this.loadRelations();
            }
        }
    },
    methods: {
        async loadRelations() {
            this.loading = true;
            try {
                const data = await getApiDataModelRelations(
                    this.knowledgeBaseId,
                    this.currentPage,
                    this.pageSize,
                    this.statusFilter
                );
                
                // 如果有任务ID筛选，进行前端过滤
                if (this.taskIdFilter) {
                    data.list = data.list.filter(item => 
                        item.task_id && item.task_id.includes(this.taskIdFilter)
                    );
                }
                
                this.relations = data.list;
                this.total = data.total;
            } catch (error) {
                this.$message.error('加载关系列表失败');
            } finally {
                this.loading = false;
            }
        },
        
        handleSelectionChange(selection) {
            this.selectedRelations = selection;
        },
        
        handleSizeChange(val) {
            this.pageSize = val;
            this.loadRelations();
        },
        
        handleCurrentChange(val) {
            this.currentPage = val;
            this.loadRelations();
        },
        
        getStatusType(status) {
            const typeMap = {
                0: 'warning',
                1: 'success',
                2: 'info'
            };
            return typeMap[status] || 'info';
        },
        
        getStatusText(status) {
            const textMap = {
                0: '待确认',
                1: '已确认',
                2: '已忽略'
            };
            return textMap[status] || '未知';
        },
        
        formatTime(timeStr) {
            return timeStr ? new Date(timeStr).toLocaleString() : '-';
        },
        
        async confirmRelations() {
            if (this.selectedRelations.length === 0) {
                this.$message.warning('请选择要确认的关系');
                return;
            }
            
            try {
                const relationIds = this.selectedRelations.map(item => item.id);
                
                const response = await fetch(`/confirm_api_data_model_relations/${this.knowledgeBaseId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        relation_ids: relationIds
                    })
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    this.$message.success(`成功确认 ${result.data.confirmed_count} 个关系`);
                    this.loadRelations(); // 重新加载列表
                } else {
                    this.$message.error(result.message);
                }
            } catch (error) {
                this.$message.error('确认关系失败');
            }
        },
        
        handleClose() {
            this.$emit('update:visible', false);
        }
    }
};

// 4. 使用示例
// 在父组件中使用关系确认弹窗
/*
<relation-confirm-dialog
    :visible.sync="relationDialogVisible"
    :knowledge-base-id="currentKnowledgeBaseId"
></relation-confirm-dialog>
*/

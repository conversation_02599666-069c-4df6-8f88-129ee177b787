package com.qfnu.config;

import com.qfnu.interceptor.TokenInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@EnableWebSecurity
public class SecurityConfig implements WebMvcConfigurer {

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .authorizeRequests()
                // 不使用Filter，直接使用后续的拦截器进行拦截
                .anyRequest().permitAll()
                .and()
                .httpBasic()
                .and()
                .csrf().disable();

        return http.build();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加自定义的 Token 拦截器
        registry.addInterceptor(tokenInterceptor())
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(
                        "/api/AuthUser/login",
                        "/api/AuthUser/oauth-complete-login", // OAuth登录接口
                        "/api/register",
                        "/api/swagger-ui/**",
                        "/api/swagger-resources/**",
                        "/api/v3/api-docs/**"
                );
    }

    @Bean
    public HandlerInterceptor tokenInterceptor() {
        return new TokenInterceptor();
    }
}
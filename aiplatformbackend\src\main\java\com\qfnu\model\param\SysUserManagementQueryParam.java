package com.qfnu.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SysUserManagementQueryParam", description = "SysUserManagement查询参数对象")
@SuperBuilder
public class SysUserManagementQueryParam extends BasePage {

    @ApiModelProperty("用户名模糊查询")
    private String usernameLike;
    
    @ApiModelProperty("类型列表，支持多选")
    private List<String> typeList;
    
    @ApiModelProperty("产品列表，支持多选")
    private List<String> productList;
    
    @ApiModelProperty("权限列表，支持多选")
    private List<String> permissionList;
}
package com.qfnu.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Configuration
@EnableOpenApi
@EnableWebMvc
@EnableSwagger2
public class SwaggerConfig {
    @Value("${swagger.enabled:true}")
    private boolean enabled;

    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.OAS_30)
                .enable(enabled)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.qfnu.controller"))
                .paths(PathSelectors.any())
                .build()
                .useDefaultResponseMessages(false)
                .groupName("默认接口");
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("代码生成器接口文档")
                .description("代码生成器相关接口的文档")
                .termsOfServiceUrl("http://localhost:8080")
                .contact(new Contact("qfnu", "https://github.com/qfnu", "<EMAIL>"))
                .version("1.0")
                .build();
    }
}
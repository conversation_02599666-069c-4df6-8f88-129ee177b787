package com.qfnu.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import java.io.Serializable;
import java.util.Date;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@ApiModel(value = "SysFeatureDTO", description = "SysFeature传输对象")
public class SysFeatureDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 
     */
    @ApiModelProperty("")
    private String featureId;

    /**
     * 
     */
    @ApiModelProperty("")
    private String description;

    /**
     * 
     */
    @ApiModelProperty("")
    private String creator;

    /**
     * 
     */
    @ApiModelProperty("")
    private String modifier;

    /**
     * 
     */
    @ApiModelProperty("")
    private String deptBelongId;

    /**
     * 
     */
    @ApiModelProperty("")
    private Date updateDatetime;

    /**
     * 
     */
    @ApiModelProperty("")
    private Date createDatetime;

    /**
     * 
     */
    @ApiModelProperty("")
    private String moduleName;

    /**
     * 
     */
    @ApiModelProperty("")
    private String personResponsible;

    /**
     * 
     */
    @ApiModelProperty("")
    private String groupResponsible;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long level;

    /**
     * 
     */
    @ApiModelProperty("")
    private String isDeleted;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long parentId;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long productId;


} 
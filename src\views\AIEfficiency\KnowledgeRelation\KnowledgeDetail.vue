<template>
  <div class="detail-container">
    <!-- 顶部容器 -->
    <div class="top-container">
      <div class="left-section">
        <a-button class="back-button" @click="handleBack" type="text">
          <arrow-left-outlined />
          返回
        </a-button>
        <div class="kb-info">
          <h1 class="kb-title">{{ knowledgeBase.title }}</h1>
        </div>
      </div>
      <div class="right-section">
        <a-button type="link" class="retrieval-link" @click="toggleComponent">
          <experiment-outlined />
          {{ activeComponent === 'document' ? '前往召回测试' : '返回文档管理' }}
          <arrow-right-outlined v-if="activeComponent === 'document'" />
          <arrow-left-outlined v-else />
        </a-button>
      </div>
    </div>
    
    <!-- 描述区域 -->
    <div class="description-container" v-if="knowledgeBase.description">
      <div class="description-content">
        <info-circle-outlined class="description-icon" />
        <span>{{ knowledgeBase.description }}</span>
      </div>
    </div>
    
    <!-- 底部容器 -->
    <div class="bottom-container">
      <document-management 
        v-if="activeComponent === 'document'" 
        :knowledge-base-id="knowledgeBase.id"
        :knowledge-base-meta="knowledgeBase"
        @switch-to-recall-test="toggleComponent"
      />
      <recall-test 
        v-else 
        :knowledge-base-id="knowledgeBase.id" 
      />
    </div>
  </div>
</template>

<script setup>
import { ref, defineEmits, onMounted } from 'vue'
import { ArrowLeftOutlined, PlusOutlined, ArrowRightOutlined, ExperimentOutlined, InfoCircleOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import DocumentManagement from './RelationManagement.vue'
import RecallTest from './RecallTest.vue'
import { message } from 'ant-design-vue'
import http from '@/utils/ai/http'

const router = useRouter()
const emit = defineEmits(['back'])

// 知识库数据
const knowledgeBase = ref({
  id: '',
  title: '',
  type: '',
  entityCount: 0,
  relationCount: 0,
  description: ''
})

// 修改获取地图信息的函数
const fetchMapInfo = async () => {
  try {
    // 从当前URL中获取relation_uid
    const currentPath = window.location.pathname
    const relationUid = currentPath.split('/relation/')[1]
    
    if (!relationUid) {
      return
    }

    // 获取用户信息
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    if (!userInfo.userId || !userInfo.username) {
      return
    }

    // 调用获取地图信息接口
    const res = await http.get(`/knowledge_api/${relationUid}/`, {
      params: {
        username: userInfo.username,
        user_id: userInfo.userId
      }
    })

    if (res.data.code === 200) {
      // 更新知识库信息
      const data = res.data.data
      knowledgeBase.value = {
        id: relationUid,
        title: data.kb_name || '未命名知识库',
        type: data.type || '未知类型',
        entityCount: data.entity_count || 0,
        relationCount: data.relation_count || 0,
        description: data.description || ''
      }
    } else {
      message.error(res.data.message || '获取地图信息失败')
    }
  } catch (error) {
    console.error('获取地图信息失败:', error)
  }
}

const activeComponent = ref('document') // 'document' 或 'recall'

const handleBack = () => {
  // 触发back事件，通知父组件
  emit('back')
  // 使用路由导航回知识库页面
  router.push('/ai-efficiency/knowledge')
}

const toggleComponent = () => {
  activeComponent.value = activeComponent.value === 'document' ? 'recall' : 'document'
}

// 在组件挂载时调用获取地图信息
onMounted(() => {
  fetchMapInfo()
})
</script>

<style scoped>
.detail-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.top-container {
  flex: 0 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(to right, #f8f9fa, #ffffff);
  border-radius: 8px;
  margin: 1.5% 1% 0 1%;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  height: 50px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.left-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 4px;
  background: transparent;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
  color: rgba(0, 0, 0, 0.65);
}

.back-button:hover {
  background-color: rgba(0, 0, 0, 0.04);
  color: #1890ff;
  transform: translateX(-2px);
}

.back-button:active {
  transform: translateX(-4px);
  color: #096dd9;
}

.kb-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: rgba(0, 0, 0, 0.85);
  position: relative;
  padding-left: 12px;
}

.kb-title:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 18px;
  width: 3px;
  background: #1890ff;
  border-radius: 3px;
}

.right-section {
  display: flex;
  align-items: center;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 6px;
  border-radius: 4px;
  height: 36px;
  padding: 0 16px;
  font-weight: 500;
  transition: all 0.3s;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.add-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.2);
}

.bottom-container {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  width: 99%;
  height: calc(100% - 90px);
  max-height: 90vh;
  overflow-y: auto;
  border-radius: 8px;
  margin: 0 12px 12px 12px;
}

.bottom-header {
  display: flex;
  flex-direction: column;
  width: 100%;
  border-bottom: none;
  align-items: center;
}

.detail-tabs {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

:deep(.ant-tabs-nav) {
  margin-bottom: 0;
  justify-content: center;
  width: 100%;
}

:deep(.ant-tabs-nav::before) {
  border-bottom: none;
}

.tab-content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
}

.header-title h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: rgba(0, 0, 0, 0.85);
}

.header-actions {
  display: flex;
  align-items: center;
}

.bottom-content {
  flex: 1;
  padding: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow-y: auto;
}

.nav-tabs {
  margin-left: 24px;
}

:deep(.ant-radio-group) {
  display: flex;
}

:deep(.ant-radio-button-wrapper) {
  border: none;
  background: transparent;
  padding: 0 16px;
  height: 32px;
  line-height: 32px;
  color: rgba(0, 0, 0, 0.65);
  transition: all 0.3s;
}

:deep(.ant-radio-button-wrapper:hover) {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.05);
}

:deep(.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)) {
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
  box-shadow: none;
}

:deep(.ant-radio-button-wrapper:not(:first-child)::before) {
  display: none;
}

.retrieval-link {
  display: flex;
  align-items: center;
  color: #1890ff;
  font-size: 14px;
  margin-left: 16px;
  padding: 0 8px;
  height: 36px;
  transition: all 0.3s;
}

.retrieval-link:hover {
  background: rgba(24, 144, 255, 0.05);
  color: #40a9ff;
}

.retrieval-link .anticon {
  font-size: 14px;
}

.retrieval-link .anticon-arrow-right {
  margin-left: 4px;
  transition: transform 0.3s;
}

.retrieval-link:hover .anticon-arrow-right {
  transform: translateX(3px);
}

.kb-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.kb-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
}

.meta-item {
  color: rgba(0, 0, 0, 0.45);
}

.description-container {
  margin: 0 1%;
  padding: 12px 16px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
}

.description-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  color: rgba(0, 0, 0, 0.65);
}

.description-icon {
  color: #52c41a;
  margin-top: 2px;
}
</style>

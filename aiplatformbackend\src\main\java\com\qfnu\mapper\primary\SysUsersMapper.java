package com.qfnu.mapper.primary;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.qfnu.model.dto.AuthUserDto;
import com.qfnu.model.po.SysUsersPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SysUsersMapper extends BaseMapper<SysUsersPO> {

    AuthUserDto getUserByUseranme(String username);

    AuthUserDto getUserByJobNo(String jobNo);

    AuthUserDto getUserById(String userId);

    int MyupdateById(SysUsersPO sysUsersPO);

    int myDeleteById(String userId);

    int count();


//    List<SysUsersPO> selectList(SysUsersPO sysUsersPO);
}
package com.qfnu.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import java.io.Serializable;
import java.util.Date;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@ApiModel(value = "SysUsersVO", description = "SysUsers视图对象")
public class SysUsersVO  implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 用户唯一标识，自增主键
     */
    @ApiModelProperty("用户唯一标识，自增主键")
    private String userId;

    /**
     * 用户名，唯一
     */
    @ApiModelProperty("用户名，唯一")
    private String username;

    /**
     * 用户密码
     */
    @ApiModelProperty("用户密码")
    private String password;

    /**
     * 用户邮箱
     */
    @ApiModelProperty("用户邮箱")
    private String email;

    /**
     * 用户名字
     */
    @ApiModelProperty("用户名字")
    private String firstName;

    /**
     * 用户姓氏
     */
    @ApiModelProperty("用户姓氏")
    private String lastName;

    /**
     * 是否为超级用户，0 表示否，1 表示是
     */
    @ApiModelProperty("是否为超级用户，0 表示否，1 表示是")
    private String isSuperuser;

    /**
     * 是否为工作人员，0 表示否，1 表示是
     */
    @ApiModelProperty("是否为工作人员，0 表示否，1 表示是")
    private String isStaff;

    /**
     * 用户是否活跃，0 表示禁用，1 表示活跃
     */
    @ApiModelProperty("用户是否活跃，0 表示禁用，1 表示活跃")
    private String isActive;

    /**
     * 用户注册时间
     */
    @ApiModelProperty("用户注册时间")
    private Date dateJoined;

    /**
     * 用户最后登录时间
     */
    @ApiModelProperty("用户最后登录时间")
    private Date lastLogin;


} 
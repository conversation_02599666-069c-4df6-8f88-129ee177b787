Stack trace:
Frame         Function      Args
0007FFFFA340  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF9240) msys-2.0.dll+0x2118E
0007FFFFA340  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFA340  0002100469F2 (00021028DF99, 0007FFFFA1F8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFA340  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFA340  00021006A545 (0007FFFFA350, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFA350, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBAB380000 ntdll.dll
7FFBAA180000 KERNEL32.DLL
7FFBA8D20000 KERNELBASE.dll
7FFBA4580000 apphelp.dll
7FFBAA540000 USER32.dll
7FFBA8B90000 win32u.dll
7FFBAA510000 GDI32.dll
7FFBA8850000 gdi32full.dll
7FFBA90F0000 msvcp_win.dll
7FFBA8700000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBA9310000 advapi32.dll
7FFBA9E40000 msvcrt.dll
7FFBA9970000 sechost.dll
7FFBAA950000 RPCRT4.dll
7FFBA7B00000 CRYPTBASE.DLL
7FFBA8BC0000 bcryptPrimitives.dll
7FFBAA910000 IMM32.DLL

<template>
  <a-modal :visible="visible" @update:visible="(val) => emit('update:visible', val)" title="发布工具" @ok="handleSubmit" @cancel="handleCancel" width="800px" class="publish-tool-modal" okText="提交" cancelText="取消" :confirmLoading="loading">
    <a-form :model="formState" :rules="rules" ref="formRef" layout="vertical" class="publish-form">
      <div class="grid grid-cols-2 gap-4">
        <a-form-item label="工具名称" name="toolName" class="form-item">
          <a-input v-model:value="formState.toolName" placeholder="请输入工具名称" class="custom-input" />
        </a-form-item>

        <a-form-item label="链接地址" name="linkAddress" class="form-item">
          <a-input v-model:value="formState.linkAddress" placeholder="请输入工具链接地址" class="custom-input" />
        </a-form-item>
      </div>

      <a-form-item label="功能介绍" name="functionDescription" class="form-item">
        <a-textarea
          v-model:value="formState.functionDescription"
          :rows="3"
          placeholder="请输入工具功能介绍"
          :maxLength="200"
          show-count
          class="custom-textarea"
        />
      </a-form-item>

      <div class="grid grid-cols-2 gap-4">
        <a-form-item label="工具类型" name="category" class="form-item">
          <a-select
            v-model:value="formState.category"
            placeholder="请选择工具类型"
            @change="handleTypeChange"
            class="custom-select"
          >
            <a-select-option value="AI应用">AI应用</a-select-option>
            <a-select-option value="测试工具">测试工具</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="标签" name="tags" class="form-item">
          <a-select
            v-model:value="formState.tags"
            mode="multiple"
            placeholder="请选择标签"
            :options="availableTags"
            :maxTagCount="4"
            class="custom-select"
          />
        </a-form-item>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import { submitTool } from '@/utils/api'

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'submit', formData: any): void
}>()

const formRef = ref<FormInstance>()
const loading = ref(false)

const formState = reactive({
  toolName: '',
  linkAddress: '',
  functionDescription: '',
  category: undefined,
  tags: []
})

const rules = {
  name: [{ required: true, message: '请输入工具名称', trigger: 'blur' }],
  url: [{ required: true, message: '请输入链接地址', trigger: 'blur' }],
  description: [{ required: true, message: '请输入功能介绍', trigger: 'blur' }],
  type: [{ required: true, message: '请选择工具类型', trigger: 'change' }],
  tags: [{ required: true, message: '请选择标签', type: 'array', trigger: 'change' }]
}

const tagOptions = {
  'AI应用': [
    { label: 'AI Agent', value: 'AI Agent' },
    { label: 'AI 工具', value: 'AI 工具' },
    { label: '缺陷预测', value: '缺陷预测' },
    { label: '代码分析', value: '代码分析' }
  ],
  '测试工具': [
    { label: '性能测试', value: '性能测试' },
    { label: '接口测试', value: '接口测试' },
    { label: '自动化测试', value: '自动化测试' },
    { label: '安全测试', value: '安全测试' },
    { label: '代码分析', value: '代码分析' }
  ],
  '数据统计': [
    { label: '数据分析', value: '数据分析' },
    { label: '数据可视化', value: '数据可视化' },
    { label: '报表统计', value: '报表统计' }
  ]
}

const availableTags = computed(() => {
  return formState.category ? tagOptions[formState.category] : []
})

watch(() => props.visible, (newVal) => {
  if (!newVal) {
    formRef.value?.resetFields()
  }
})

const handleTypeChange = () => {
  formState.tags = []
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true
    const submitData = {
      ...formState,
      tags: formState.tags.join(',')
    }
    const response = await submitTool(submitData)
    console.log(response)
    if (response) {
      message.success('发布成功')
      emit('submit', { ...formState })
      emit('update:visible', false)
    } else {
      message.error(response.message || '发布失败')
    }
  } catch (error) {
    message.error('发布失败，请检查表单内容或网络连接')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.publish-tool-modal :deep(.ant-modal-content) {
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
}

.publish-tool-modal :deep(.ant-modal-header) {
  padding: 24px 24px 0;
  border-bottom: none;
}

.publish-tool-modal :deep(.ant-modal-title) {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.publish-tool-modal :deep(.ant-modal-body) {
  padding: 20px;
  max-height: calc(90vh - 110px);
  overflow-y: auto;
}

.publish-form {
  max-width: 100%;
}

.form-item {
  margin-bottom: 16px;
}

.form-item :deep(.ant-form-item-label) {
  padding-bottom: 8px;
}

.form-item :deep(.ant-form-item-label > label) {
  font-size: 15px;
  font-weight: 500;
  color: #374151;
}

.custom-input,
.custom-textarea,
.custom-select :deep(.ant-select-selector) {
  border-radius: 8px;
  border-color: #e5e7eb;
  transition: all 0.3s ease;
}

.custom-input:hover,
.custom-textarea:hover,
.custom-select:hover :deep(.ant-select-selector) {
  border-color: #60a5fa;
}

.custom-input:focus,
.custom-textarea:focus,
.custom-select:focus :deep(.ant-select-selector) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.custom-select :deep(.ant-select-selection-item) {
  background-color: #f3f4f6;
  border-radius: 4px;
  border: none;
  color: #4b5563;
}

.publish-tool-modal :deep(.ant-modal-footer) {
  padding: 16px 24px;
  border-top: 1px solid #f3f4f6;
}

.publish-tool-modal :deep(.ant-btn) {
  height: 38px;
  padding: 0 20px;
  font-size: 14px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.publish-tool-modal :deep(.ant-btn-primary) {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.publish-tool-modal :deep(.ant-btn-primary:hover) {
  background-color: #2563eb;
  border-color: #2563eb;
}
</style>
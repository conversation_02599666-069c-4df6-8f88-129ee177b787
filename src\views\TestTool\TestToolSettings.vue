<template>
  <div class="min-h-screen bg-gray-50 p-4">
    <router-view v-if="$route.name === 'user-management' || $route.name === 'feature-management'" />
    <div v-else>
      <div class="mb-6 flex justify-start items-center">
        <a-input-search
          v-model:value="searchQuery"
          placeholder="搜索设置项"
          class="w-64"
        />
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div v-for="card in filteredCards" :key="card.title" class="bg-white rounded-lg shadow-sm p-6 flex flex-col h-full">
          <div class="flex items-center mb-4">
            <div :class="[`p-3 rounded-lg ${card.iconBgColor}`]">
              <svg class="w-6 h-6" :class="card.iconColor" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="card.icon" />
              </svg>
            </div>
            <h3 class="text-lg font-semibold ml-4">{{ card.title }}</h3>
          </div>
          <p class="text-gray-600 mb-4">{{ card.description }}</p>
          <div class="flex space-x-2 mt-auto">
            <template v-for="(button, index) in card.buttons" :key="index">
              <a-button :type="button.type" class="flex items-center" :ghost="button.ghost" @click="button.onClick && button.onClick()">
                <template #icon>
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="button.icon" />
                  </svg>
                </template>
                {{ button.text }}
              </a-button>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';

const searchQuery = ref('');
const router = useRouter();

const iconConfig = {
  userManagement: {
    icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z',
    iconBgColor: 'bg-blue-100',
    iconColor: 'text-blue-600'
  },
  featureManagement: {
    icon: 'M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z',
    extraIcon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z',
    iconBgColor: 'bg-purple-100',
    iconColor: 'text-purple-600'
  },
  systemStatus: {
    icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
    iconBgColor: 'bg-green-100',
    iconColor: 'text-green-600'
  }
};

const buttonIconConfig = {
  list: 'M4 6h16M4 12h16M4 18h16',
  settings: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z',
  details: 'M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z',
  refresh: 'M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
};

const cards = [
  {
    title: '用户管理',
    description: '管理系统用户权限、角色分配及访问控制，确保系统安全性',
    ...iconConfig.userManagement,
    buttons: [
      {
        text: '管理列表',
        icon: buttonIconConfig.list,
        type: 'default',
        onClick: () => handleViewDetails('/test-tools/settings/user-management')
      }
    ]
  },
  {
    title: '特性管理',
    description: '配置和管理系统特性，优化功能体验，提升系统性能',
    ...iconConfig.featureManagement,
    buttons: [
      {
        text: '配置中心',
        icon: buttonIconConfig.settings,
        type: 'default',
        onClick: () => handleViewDetails('/test-tools/settings/feature-management')
      }
    ]
  },
  {
    title: '系统状态',
    description: '监控系统运行状态，查看性能指标，保障系统稳定性',
    ...iconConfig.systemStatus,
    buttons: [
      {
        text: '查看详情',
        icon: buttonIconConfig.details,
        type: 'primary',
        ghost: true
      },
      {
        text: '刷新状态',
        icon: buttonIconConfig.refresh,
        type: 'default'
      }
    ]
  }
];

const handleViewDetails = (path: string) => {
  router.push(path);
};

const filteredCards = computed(() => {
  const query = searchQuery.value.toLowerCase();
  if (!query) return cards;
  return cards.filter(card => 
    card.title.toLowerCase().includes(query) || 
    card.description.toLowerCase().includes(query)
  );
});
</script>
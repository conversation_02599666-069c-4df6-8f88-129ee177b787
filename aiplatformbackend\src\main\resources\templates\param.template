package ${package}.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import ${package}.util.common.BaseParam;
import java.io.Serializable;
${imports}

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "${className}Param", description = "${className}参数对象")
public class ${className}Param extends BaseParam implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
${fields}
} 
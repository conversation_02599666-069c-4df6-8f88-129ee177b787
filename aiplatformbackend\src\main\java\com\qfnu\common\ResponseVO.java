package com.qfnu.common;/*
 * Copyright, 2024-2024, <PERSON><PERSON> Culture Co. Ltd. All rights reserved.
 */


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @date 2024/11/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResponseVO<T> {

    /**
     * 响应编码,0为正常,其他为错误
     */
    private int code;

    /**
     * 响应提示信息
     */
    private String msg;

    /**
     * 响应内容
     */
    private T data;

    public static <T> ResponseVO<T> validFail(String msg) {
        return validFail(HttpStatus.INTERNAL_SERVER_ERROR.value(), msg);
    }

    public static <T> ResponseVO<T> validFail(Integer code, String msg) {
        return validFail(code, null, msg);
    }

    public static <T> ResponseVO<T> validFail(T result, String msg) {
        return validFail(0, result, msg);
    }

    public static <T> ResponseVO<T> validFail(Integer code, T result, String msg) {
        return ResponseVO.<T>builder()
                .code(code)
                .data(result)
                .msg(msg)
                .build();
    }

    public static <T> ResponseVO<T> success() {
        return success(null);
    }

    public static <T> ResponseVO<T> success(T result) {
        return success(result, "empty");
    }



    public static <T> ResponseVO<T> success(T result, Integer code) {
        return success(result, null, code);
    }


    public static <T> ResponseVO<T> success(T result, String msg) {
        return success(result, msg, 0);
    }

    public static <T> ResponseVO<T> success(String msg, Integer code) {
        return success(null, msg, code);
    }

    public static <T> ResponseVO<T> success(T result, String msg, Integer code) {
        return ResponseVO.<T>builder()
                .code(code)
                .data(result)
                .msg(msg)
                .build();
    }

    public Boolean isSuccessful() {
        return this.code == 0;
    }

}

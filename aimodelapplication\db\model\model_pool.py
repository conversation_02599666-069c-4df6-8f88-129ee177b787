import threading
import requests
from typing import Dict, Any, Optional
import re
import os

class ModelInfo:
    """
    存储模型元信息
    """
    def __init__(self, name: str, model: str, details: dict):
        self.name = name
        self.model = model
        self.details = details

class OllamaOpenAIClient:
    """
    Ollama OpenAI兼容API客户端，支持深度思考参数和自动清洗<think>标签
    """
    def __init__(self, base_url: str, model: str, default_deep_thought: bool = False):
        self.base_url = base_url.rstrip('/')
        self.model = model
        self.default_deep_thought = default_deep_thought

    @staticmethod
    def _clean_think_tags(text: str) -> str:
        return re.sub(r'</?think>', '', text)

    def chat(self, messages, deep_thought: Optional[bool] = None, clean: bool = True, **kwargs) -> Any:
        url = f"{self.base_url}/v1/chat/completions"
        payload = {
            "model": self.model,
            "messages": messages
        }
        # 优先使用调用时的deep_thought参数，否则用默认
        use_deep_thought = self.default_deep_thought if deep_thought is None else deep_thought
        if use_deep_thought:
            payload["deep_thought"] = True
        payload.update(kwargs)
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        # 自动清洗输出
        if clean and isinstance(result, dict) and 'choices' in result:
            for choice in result['choices']:
                if 'message' in choice and 'content' in choice['message']:
                    choice['message']['content'] = self._clean_think_tags(choice['message']['content'])
        return result

class ModelPool:
    """
    模型连接池，支持Ollama的OpenAI兼容API。
    单例模式。
    """
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'clients'):
            self.clients: Dict[str, OllamaOpenAIClient] = {}
            self.models: Dict[str, ModelInfo] = {}

    def register_model(self, model_info: dict, base_url: str, default_deep_thought: bool = False):
        name = model_info['name']
        model = model_info['model']
        details = model_info.get('details', {})
        self.models[name] = ModelInfo(name, model, details)
        self.clients[name] = OllamaOpenAIClient(base_url, model, default_deep_thought=default_deep_thought)

    def get_client(self, name: str) -> Optional[OllamaOpenAIClient]:
        return self.clients.get(name)

    def get_model_info(self, name: str) -> Optional[ModelInfo]:
        return self.models.get(name)

# 注册qwen3:32b模型并返回client
def register_qwen3_model() -> Optional[OllamaOpenAIClient]:
    """
    根据 .env 配置注册 qwen3 模型
    """
    # 从环境变量中获取参数
    model_name = os.getenv("QWEN3_MODEL_NAME", "qwen3:32b")
    base_url = os.getenv("MODEL_URL", "http://10.113.54.98:11434")
    deep_thought_str = os.getenv("QWEN3_DEEP_THOUGHT", "False").lower()
    deep_thought = deep_thought_str in ("true", "1", "t")

    # 构建模型信息
    qwen3_info = {
        "name": model_name,
        "model": model_name,
        "modified_at": os.getenv("QWEN3_MODIFIED_AT", "2025-04-29T09:45:47.299972406+08:00"),
        "size": int(os.getenv("QWEN3_SIZE", "20201253588")),
        "digest": os.getenv(
            "QWEN3_DIGEST",
            "e1c9f234c6eb37545e991032e3d674e0762763cddab4e69009a2f0d8739deaaf"
        ),
        "details": {
            "parent_model": os.getenv("QWEN3_PARENT_MODEL", ""),
            "format": os.getenv("QWEN3_FORMAT", "gguf"),
            "family": os.getenv("QWEN3_FAMILY", "qwen3"),
            "families": os.getenv("QWEN3_FAMILIES", "qwen3").split(","),
            "parameter_size": os.getenv("QWEN3_PARAMETER_SIZE", "32.8B"),
            "quantization_level": os.getenv("QWEN3_QUANTIZATION_LEVEL", "Q4_K_M")
        }
    }

    pool = ModelPool()
    pool.register_model(qwen3_info, base_url=base_url, default_deep_thought=deep_thought)
    return pool.get_client(model_name)



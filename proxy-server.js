const http = require('http');
const httpProxy = require('http-proxy-middleware');
const express = require('express');

const app = express();

// 创建代理中间件
const proxy = httpProxy.createProxyMiddleware({
  target: 'http://10.113.7.86:8080', // 目标地址（您的本地前端）
  changeOrigin: true,
  ws: true, // 支持websocket
  logLevel: 'debug',
  onProxyReq: (proxyReq, req, res) => {
    console.log(`[${new Date().toISOString()}] 代理请求: ${req.method} ${req.url}`);
    console.log(`  -> 转发到: http://10.113.7.86:8080${req.url}`);
  },
  onProxyRes: (proxyRes, req, res) => {
    console.log(`[${new Date().toISOString()}] 代理响应: ${proxyRes.statusCode} ${req.url}`);
  },
  onError: (err, req, res) => {
    console.error(`[${new Date().toISOString()}] 代理错误:`, err.message);
    res.status(500).send('代理服务器错误');
  }
});

// 使用代理中间件
app.use('/', proxy);

// 启动代理服务器，监听80端口（对应10.113.53.108）
const PORT = 80;
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log('='.repeat(60));
  console.log('🚀 OAuth回调代理服务器已启动');
  console.log('='.repeat(60));
  console.log(`📍 监听地址: http://0.0.0.0:${PORT}`);
  console.log(`🎯 目标地址: http://10.113.7.86:8080`);
  console.log('');
  console.log('📋 使用说明:');
  console.log('1. 确保此服务器运行在 10.113.53.108 机器上');
  console.log('2. TAM回调地址配置为: http://10.113.53.108/oauth-callback');
  console.log('3. 本地前端运行在: http://10.113.7.86:8080');
  console.log('4. 代理会自动转发OAuth回调请求');
  console.log('='.repeat(60));
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('\n📴 收到关闭信号，正在关闭代理服务器...');
  server.close(() => {
    console.log('✅ 代理服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('\n📴 收到中断信号，正在关闭代理服务器...');
  server.close(() => {
    console.log('✅ 代理服务器已关闭');
    process.exit(0);
  });
});

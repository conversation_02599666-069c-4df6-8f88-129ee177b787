package com.qfnu.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import java.io.Serializable;
import java.util.Date;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@ApiModel(value = "ZtProjectDTO", description = "ZtProject传输对象")
public class ZtProjectDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 
     */
    @ApiModelProperty("")
    private String id;

    /**
     * 
     */
    @ApiModelProperty("")
    private String type;

    /**
     * 
     */
    @ApiModelProperty("")
    private String path;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long grade;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long project;

    /**
     * 
     */
    @ApiModelProperty("")
    private String name;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long parent;

    /**
     * 
     */
    @ApiModelProperty("")
    private String status;

    /**
     * 
     */
    @ApiModelProperty("")
    private String substatus;

    /**
     * 
     */
    @ApiModelProperty("")
    private String pri;

    /**
     * 
     */
    @ApiModelProperty("")
    private String desc;

    /**
     * 
     */
    @ApiModelProperty("")
    private String order;

    /**
     * 
     */
    @ApiModelProperty("")
    private String deleted;

    /**
     * 
     */
    @ApiModelProperty("")
    private Date begin;

    /**
     * 
     */
    @ApiModelProperty("")
    private Date end;

    /**
     * 
     */
    @ApiModelProperty("")
    private String PM;

    /**
     * 
     */
    @ApiModelProperty("")
    private String model;


} 
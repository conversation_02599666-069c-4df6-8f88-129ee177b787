export const roleMapping = {
  super: '超级管理员',
  admin: '管理员',
  common: '普通用户'
};

export const typeMapping = {
  other: '其他',
  usm_test: 'USM测试',
  tgfw_test: 'TGFW测试',
  cloud_test: '云盾测试',
  waf_test: 'WAF测试',
  trainee_test: '测试实习'
};

export const productMapping = {
  "16": "明御防火墙",
  "17": "明御WEB应用防火墙",
  "18": "明御运维审计与风险控制系统",
  "351": "明御安全网关",
  "352": "数腾V2V数据迁移",
  "353": "明御入侵防御系统",
  "354": "数腾CDP持续数据保护",
  "355": "盛科交换机",
  "356": "明御网络安全审计系统",
  "357": "安恒云(超融合)",
  "391": "明御特权账号安全管理系统",
  "422": "明御数据库运维安全网关"
};

export const getTypeName = (type: string) => {
  return typeMapping[type as keyof typeof typeMapping] || type;
};

export const getProductName = (productId: string) => {
  return productMapping[productId as keyof typeof productMapping] || productId;
};

export const getRoleName = (role: string) => {
  return roleMapping[role as keyof typeof roleMapping] || role;
};
package com.qfnu.mapper.primary;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qfnu.model.dto.SysReviewDTO;
import com.qfnu.model.param.SysFeatureParam;
import com.qfnu.model.po.SysFeaturePO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qfnu.model.vo.SysFeatureVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysFeatureMapper extends BaseMapper<SysFeaturePO> {

    IPage<SysFeatureVO> getPageList(Page<SysFeatureVO> page, @Param("param")SysFeatureParam param);

    Integer getPageListCount(Page<SysFeatureVO> page,  @Param("param")SysFeatureParam param);

    List<SysFeaturePO> getFeaturesByProductId(@Param("param")SysFeatureParam param);

    boolean myUpdate(@Param("param")SysFeatureParam param);
}
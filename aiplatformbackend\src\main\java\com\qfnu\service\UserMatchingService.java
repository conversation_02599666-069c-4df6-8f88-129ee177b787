package com.qfnu.service;

import com.qfnu.model.dto.ADUserInfoDTO;
import com.qfnu.model.dto.AuthUserDto;

public interface UserMatchingService {

    /**
     * 根据AD用户信息匹配本地用户
     * 匹配优先级：邮箱 > 工号 > 用户名
     *
     * @param adUserInfo AD用户信息
     * @return 匹配到的用户信息，如果没有匹配到返回null
     */
    AuthUserDto matchUser(ADUserInfoDTO adUserInfo);

    /**
     * 根据邮箱查找用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    AuthUserDto findByEmail(String email);

    /**
     * 根据工号查找用户
     *
     * @param jobNo 工号
     * @return 用户信息
     */
    AuthUserDto findByJobNo(String jobNo);

    /**
     * 根据用户名查找用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    AuthUserDto findByUsername(String username);

    /**
     * 判断是否应该自动创建用户
     *
     * @param adUserInfo AD用户信息
     * @return true表示应该创建，false表示不应该创建
     */
    boolean shouldCreateUser(ADUserInfoDTO adUserInfo);

    /**
     * 根据AD用户信息确定用户角色
     *
     * @param adUserInfo AD用户信息
     * @return 用户角色
     */
    String determineUserRole(ADUserInfoDTO adUserInfo);

    /**
     * 根据AD用户信息确定产品权限
     *
     * @param adUserInfo AD用户信息
     * @return 产品权限
     */
    String determineUserProduct(ADUserInfoDTO adUserInfo);
}

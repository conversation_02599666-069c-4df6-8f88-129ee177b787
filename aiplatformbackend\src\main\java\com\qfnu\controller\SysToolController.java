package com.qfnu.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qfnu.common.ResponseVO;
import com.qfnu.common.annotation.RequiredPermission;
import com.qfnu.common.enums.DeleteStatusEnum;
import com.qfnu.common.enums.PublishStatusEnum;
import com.qfnu.common.enums.ReviewStatusEnum;
import com.qfnu.model.param.SysToolQueryParam;
import com.qfnu.model.po.SysToolPO;
import com.qfnu.model.po.SysUserManagementPO;
import com.qfnu.service.SysToolService;
import com.qfnu.model.vo.SysToolVO;
import com.qfnu.model.dto.SysToolDTO;
import com.qfnu.model.param.SysToolParam;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(tags = "SysTool接口，工具上架")
@RestController
@RequestMapping("/api/SysTool")
public class SysToolController {
    
    @Resource
    private SysToolService sysToolService;
    
    @ApiOperation("新增工具")
    @PostMapping("/save")
    public ResponseVO<Integer> save(@RequestBody SysToolParam param,HttpServletRequest request) {
        String userId = (String)request.getAttribute("id");
        param.setIsPublished(String.valueOf(PublishStatusEnum.UNPUBLISHED.getCode()));
        param.setIsDeleted(String.valueOf(DeleteStatusEnum.NOT_DELETED.getCode()));
        param.setReviewStatus(String.valueOf(ReviewStatusEnum.PENDING.getCode()));
        return ResponseVO.success(sysToolService.save(param,userId));
    }
    
//    @ApiOperation("批量新增")
//    @PostMapping("/saveBatch")
//    public ResponseVO<Integer> saveBatch(@RequestBody List<SysToolParam> paramList) {
//        return ResponseVO.success(sysToolService.saveBatch(paramList));
//    }
    
//    @ApiOperation("更新")
//    @PostMapping("/update")
//    public ResponseVO<Integer> update(@RequestBody SysToolParam param) {
//        return ResponseVO.success(sysToolService.update(param));
//    }

    @RequiredPermission({"super,admin"})
    @ApiOperation("删除工具")
    @PostMapping("/delete")
    public ResponseVO<Integer> delete(@RequestBody SysToolParam param) {
        return ResponseVO.success(sysToolService.delete(param.getToolId()));
    }
    
//    @ApiOperation("根据ID查询")
//    @PostMapping("/getById")
//    public ResponseVO<SysToolVO> getById(@RequestBody SysToolParam param) {
//        return ResponseVO.success(sysToolService.getById(param.getToolId()));
//    }
    
//    @ApiOperation("列表查询")
//    @PostMapping("/list")
//    public ResponseVO<List<SysToolVO>> getList(@RequestBody SysToolParam param) {
//        return ResponseVO.success(sysToolService.getList(param));
//    }
    
//    @ApiOperation("批量新增或更新")
//    @PostMapping("/batchAddOrUpdate")
//    public ResponseVO<List<SysToolVO>> batchAddOrUpdate(@RequestBody List<SysToolParam> paramList) {
//        return ResponseVO.success(sysToolService.batchAddOrUpdate(paramList));
//    }

    @ApiOperation("分页查询工具列表")
    @PostMapping("/toolsList")
    public ResponseVO<IPage<SysToolVO>> approvedAndPublishedTools(@RequestBody SysToolQueryParam sysToolQueryParam) {
        return ResponseVO.success(sysToolService.getToolsList(sysToolQueryParam),200);
    }


    @ApiOperation("获取当前登录用户的提交记录")
    @PostMapping("/myToolList")
    public  ResponseVO<IPage<SysToolVO>> myToolList(@RequestBody SysToolQueryParam sysToolQueryParam, HttpServletRequest httpServletRequest){
        String id = (String) httpServletRequest.getAttribute("id");
        sysToolQueryParam.setCreators(id);
        return ResponseVO.success(sysToolService.getMyToolList(sysToolQueryParam),200);
    }

    @RequiredPermission({"super,admin"})
    @ApiOperation("上架工具")
    @PostMapping("/publish/{toolId}")
    public ResponseVO<Integer> publishTool(@PathVariable String toolId) {
        return ResponseVO.success(sysToolService.publishTool(toolId),200);
    }

    @RequiredPermission({"super,admin"})
    @ApiOperation("下架工具")
    @PostMapping("/unpublish/{toolId}")
    public ResponseVO<Integer> unpublishTool(@PathVariable String toolId) {
        return ResponseVO.success(sysToolService.unpublishTool(toolId),200);
    }
}
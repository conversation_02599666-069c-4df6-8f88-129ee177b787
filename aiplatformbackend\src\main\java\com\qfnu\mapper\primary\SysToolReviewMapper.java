package com.qfnu.mapper.primary;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qfnu.model.dto.SysReviewDTO;
import com.qfnu.model.po.SysReviewPO;
import com.qfnu.model.param.SysReviewParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SysToolReviewMapper {
    
    /**
     * 提交工具审核
     * @param reviewPO 审核记录
     * @return 影响行数
     */
    int insertToolReview(SysReviewPO reviewPO);
    
    /**
     * 更新工具审核状态
     * @param reviewPO 审核记录
     * @return 影响行数
     */
    int updateReviewStatus(SysReviewPO reviewPO);
    
    /**
     * 分页查询审核记录
     * @param page 分页参数
     * @param param 查询条件
     * @return 分页结果
     */
    IPage<SysReviewDTO> selectReviewPage(IPage<SysReviewDTO> page, @Param("param") SysReviewParam param);
    
    /**
     * 获取审核详情
     * @param reviewId 审核ID
     * @return 审核详情
     */
    SysReviewDTO selectReviewDetail(@Param("reviewId") String reviewId);
    
    /**
     * 更新工具状态
     * @param toolId 工具ID
     * @param status 状态
     * @return 影响行数
     */
    int updateToolStatus(@Param("toolId") String toolId, @Param("status") String status);

    /**
     * 更新工具删除状态
     * @param toolId 工具ID
     * @param status 状态
     * @return 影响行数
     */
    int updateToolDeleteStatus(@Param("toolId") String toolId, @Param("status") String status);

    /**
     * 更新工具上架状态
     * @param toolId 工具ID
     * @param publish 状态
     * @return 影响行数
     */
    int updatePublish(@Param("toolId") String toolId, @Param("status") String publish);
}
package com.qfnu.model.vo;

import lombok.Data;

import java.util.List;

@Data
public class ToolDistributionVO {
    /**
     * 工具类别占比数据
     */
    private List<CategoryData> categoryDistribution;

    /**
     * 月度工具上线趋势数据
     */
    private List<MonthlyTrendData> monthlyTrend;

    @Data
    public static class CategoryData {
        /**
         * 类别名称
         */
        private String name;

        /**
         * 工具数量
         */
        private Integer value;
    }

    @Data
    public static class MonthlyTrendData {
        /**
         * 月份
         */
        private String month;

        /**
         * 新上线工具数量
         */
        private Integer count;
    }
}
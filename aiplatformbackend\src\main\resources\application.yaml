server:
  port: 8083
  # 增加请求头大小限制
  max-http-header-size: 64KB
#  # 增加请求体大小限制
#  max-http-post-size: 2097152

spring:
  devtools:
    restart:
      enabled: true
      additional-paths: src/main/java
      exclude: static/**,public/**
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  # HTTP请求配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  # Tomcat配置
  server:
    tomcat:
      max-http-header-size: 64KB
      max-http-post-size: 2MB
  datasource:
    primary:
      type: com.alibaba.druid.pool.DruidDataSource
      url: jdbc:mysql://*************:3306/ai_module?useSSL=false&serverTimezone=UTC
      username: root
      password: 123456
      driver-class-name: com.mysql.cj.jdbc.Driver
    secondary:
      type: com.alibaba.druid.pool.DruidDataSource
      url: jdbc:mysql://************:3306/true_zentao_copy?useSSL=false&serverTimezone=UTC
      username: root
      password: ngfw123!@#
      driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      # 初始连接数
      initial-size: 5
      # 最小连接池数量
      min-idle: 5
      # 最大连接池数量
      max-active: 20
      # 获取连接等待超时时间
      max-wait: 60000
      # 检测连接是否有效的间隔时间
      time-between-eviction-runs-millis: 60000
      # 连接在池中最小生存的时间
      min-evictable-idle-time-millis: 300000
      # 连接在池中最大生存的时间
      max-evictable-idle-time-millis: 900000
      # 用来检测连接是否有效的sql
      validation-query: SELECT 1
      # 建议配置为true，不影响性能，并且保证安全性
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 是否缓存preparedStatement
      pool-prepared-statements: true
      # 要启用PSCache，必须配置大于0
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters
      filters: stat,wall
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500
      # 配置DruidStatFilter
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      # 配置DruidStatViewServlet
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        # IP白名单(没有配置或者为空，则允许所有访问)
        allow: 127.0.0.1
        # IP黑名单 (存在共同时，deny优先于allow)
        deny:
        # 禁用HTML页面上的"Reset All"功能
        reset-enable: false
        # 登录名
        login-username: admin
        # 登录密码
        login-password: 123456

# 自定义配置
app:
  security:
    bcrypt:
      salt: $2a$10$abcdefghijklmnopqrstuv

# TAM零信任身份服务中心OAuth2.0认证配置
oauth:
  auth:
    # TAM认证服务器配置 - 使用实际的服务器地址
    base-url: ${OAUTH_BASE_URL:https://sso.das-security.cn}
    token-url: ${OAUTH_TOKEN_URL:https://sso.das-security.cn/iam/auth/oauth2/accessToken}
    userinfo-url: ${OAUTH_USERINFO_URL:https://sso.das-security.cn/iam/auth/oauth2/userInfo}

    # 应用认证信息（已获取的实际值）
    client-id: ${OAUTH_CLIENT_ID:UK59v8}
    client-secret: ${OAUTH_CLIENT_SECRET:M02KuH}

    # 回调地址
    redirect-uri: ${OAUTH_REDIRECT_URI:http://***********:8080}

    # 请求超时时间（毫秒）
    timeout: 10000

    # 用户匹配规则配置
    user-matching:
      # 是否自动创建不存在的用户
      auto-create-user: true

      # 默认用户角色
      default-role: user

      # 默认产品权限
      default-product: default

      # 用户类型标识
      user-type: oauth_user

mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.qfnu.model.po
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
    logging.level.com.qfnu.mapper: DEBUG
    global-config:
      db-config:
        id-type: auto

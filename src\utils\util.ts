// 似于 qs.stringify方法, 格式化一个对象为 query string.
export function queryStringify(obj: any, prefix = ""): string {
  const pairs: string[] = [];
  for (let key in obj) {
    if (!Object.prototype.hasOwnProperty.call(obj, key)) continue;
    const value = obj[key];
    const enKey = encodeURIComponent(key);
    let pair;
    if (typeof value === "object" && value !== null) {
      pair = queryStringify(value, prefix ? prefix + "[" + enKey + "]" : enKey);
    } else {
      pair = (prefix ? prefix + "[" + enKey + "]" : enKey) + "=" + encodeURIComponent(value);
    }
    pairs.push(pair);
  }
  return pairs.join("&");
}

export const ls = {
  setItem(key: string, value: string) {
    window.localStorage.setItem(key, value);
  },
  getItem(key: string) {
    return window.localStorage.getItem(key);
  },
  remove(key: string) {
    window.localStorage.removeItem(key);
  },
  clear() {
    window.localStorage.clear();
  },
}; 
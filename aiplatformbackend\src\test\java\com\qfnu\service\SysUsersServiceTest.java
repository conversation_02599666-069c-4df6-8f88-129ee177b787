//package com.qfnu.service;
//
//import com.qfnu.model.param.SysUsersParam;
//import com.qfnu.model.vo.SysUsersVO;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import java.util.Date;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.assertNotNull;
//import static org.junit.jupiter.api.Assertions.assertTrue;
//
//@SpringBootTest
//public class SysUsersServiceTest {
//
//    @Autowired
//    private SysUsersService sysUsersService;
//
//    @Test
//    public void testSaveUser() {
//        // 创建测试用户参数
//        SysUsersParam userParam = new SysUsersParam();
//        userParam.setUsername("admin");
//        userParam.setPassword("123456");
//        userParam.setEmail("<EMAIL>");
//        userParam.setFirstName("youlu");
//        userParam.setLastName("luo");
//        userParam.setIsSuperuser("1");
//        userParam.setIsStaff("1");
//        userParam.setIsActive("1");
//        userParam.setDateJoined(new Date());
//        userParam.setLastLogin(new Date());
//
//
//
//        // 验证保存是否成功
//        assertEquals(1, result);
//
//        // 查询并验证用户信息
//        SysUsersVO savedUser = sysUsersService.getList(userParam).get(0);
//        assertNotNull(savedUser);
//        assertEquals("admin", savedUser.getUsername());
//        // 验证密码已被加密（BCrypt格式以$2a$开头）
//        assertNotNull(savedUser.getPassword());
//        assertTrue(savedUser.getPassword().startsWith("$2a$"));
//        assertEquals("<EMAIL>", savedUser.getEmail());
//        assertEquals("youlu", savedUser.getFirstName());
//        assertEquals("luo", savedUser.getLastName());
//        assertEquals("1", savedUser.getIsSuperuser());
//        assertEquals("1", savedUser.getIsStaff());
//        assertEquals("1", savedUser.getIsActive());
//        assertNotNull(savedUser.getDateJoined());
//        assertNotNull(savedUser.getLastLogin());
//    }
//}
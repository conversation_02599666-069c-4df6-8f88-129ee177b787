#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试rerank阈值问题
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from aimodelapplication.service.rerank_service import PrerequisiteMatchingService


async def debug_rerank_threshold():
    """调试rerank阈值问题"""
    
    print("=== 调试Rerank阈值问题 ===")
    
    api_key = "sk-fdfd70db7f9b48b4b8cad01c56c3fe99"
    
    # 模拟实际的API summaries（从日志中提取）
    api_summaries = [
        {"id": "a3b201d8-7eb2-48e8-a126-fb925bc4ad01", "summary": "审批规则列表", "method": "GET"},
        {"id": "dac401c9-0842-4fbe-b122-3514ab689bba", "summary": "审批规则获取", "method": "GET"},
        {"id": "12f0bbeb-c202-4385-ac40-d1f3caadc8b8", "summary": "审批规则创建", "method": "POST"},
        {"id": "3c2ef112-ff3b-4772-b7f4-5a7cd96bbe90", "summary": "资产账号列表", "method": "GET"},
        {"id": "ecf7c383-f3c8-41a4-911a-165e1debc17a", "summary": "资产账号获取", "method": "GET"},
        {"id": "test_project_1", "summary": "项目创建", "method": "POST"},
        {"id": "test_project_2", "summary": "项目列表", "method": "GET"},
        {"id": "test_project_3", "summary": "项目管理", "method": "PUT"},
        {"id": "test_asset_1", "summary": "资产创建", "method": "POST"},
        {"id": "test_user_1", "summary": "用户创建", "method": "POST"}
    ]
    
    # 前置依赖
    prerequisite = "创建项目"
    
    print(f"前置依赖: {prerequisite}")
    print(f"API summaries数量: {len(api_summaries)}")
    print("API summaries:")
    for i, api in enumerate(api_summaries, 1):
        print(f"  {i}. [{api['method']}] {api['summary']} (ID: {api['id']})")
    
    try:
        # 初始化匹配服务
        matching_service = PrerequisiteMatchingService(api_key)
        
        print(f"\n当前阈值设置:")
        print(f"  语义相似度阈值: {matching_service.semantic_threshold}")
        print(f"  动词相似度阈值: {matching_service.verb_threshold}")
        
        # 测试单个匹配
        print(f"\n开始测试单个匹配...")
        result = await matching_service.find_best_matching_api(prerequisite, api_summaries)
        
        if result:
            print(f"✓ 匹配成功:")
            print(f"  匹配API: {result.get('summary')}")
            print(f"  语义相似度: {result.get('semantic_score', 0):.4f}")
            print(f"  动词相似度: {result.get('verb_score', 0):.4f}")
            print(f"  综合相似度: {result.get('final_score', 0):.4f}")
            print(f"  风险级别: {result.get('risk_level')}")
        else:
            print("✗ 未找到匹配")
        
        # 测试并行处理
        print(f"\n开始测试并行处理...")
        results = await matching_service.process_multiple_prerequisites([prerequisite], api_summaries)
        
        print(f"并行处理结果: {len(results)} 个匹配")
        
        for i, result in enumerate(results, 1):
            print(f"  {i}. {result.get('prerequisite')} → {result.get('summary')} "
                  f"(综合分数: {result.get('final_score', 0):.4f})")
        
        # 如果没有结果，尝试降低阈值
        if not results:
            print(f"\n没有匹配结果，尝试降低阈值...")
            
            # 临时降低阈值
            original_semantic_threshold = matching_service.semantic_threshold
            original_verb_threshold = matching_service.verb_threshold
            
            matching_service.semantic_threshold = 0.05  # 非常低的阈值
            matching_service.verb_threshold = 0.1
            
            print(f"临时阈值设置:")
            print(f"  语义相似度阈值: {matching_service.semantic_threshold}")
            print(f"  动词相似度阈值: {matching_service.verb_threshold}")
            
            result = await matching_service.find_best_matching_api(prerequisite, api_summaries)
            
            if result:
                print(f"✓ 降低阈值后匹配成功:")
                print(f"  匹配API: {result.get('summary')}")
                print(f"  语义相似度: {result.get('semantic_score', 0):.4f}")
                print(f"  动词相似度: {result.get('verb_score', 0):.4f}")
                print(f"  综合相似度: {result.get('final_score', 0):.4f}")
            else:
                print("✗ 即使降低阈值也未找到匹配")
            
            # 恢复原始阈值
            matching_service.semantic_threshold = original_semantic_threshold
            matching_service.verb_threshold = original_verb_threshold
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_direct_rerank():
    """直接测试rerank API"""
    
    print(f"\n=== 直接测试Rerank API ===")
    
    from aimodelapplication.service.rerank_service import RerankService
    
    api_key = "sk-fdfd70db7f9b48b4b8cad01c56c3fe99"
    
    try:
        rerank_service = RerankService(api_key)
        
        query = "创建项目"
        summaries = [
            "审批规则列表",
            "审批规则获取", 
            "审批规则创建",
            "资产账号列表",
            "资产账号获取",
            "项目创建",
            "项目列表",
            "项目管理",
            "资产创建",
            "用户创建"
        ]
        
        print(f"查询: {query}")
        print(f"候选文档: {summaries}")
        
        results = await rerank_service.rerank_summaries(query, summaries, top_k=10)
        
        print(f"\n✓ Rerank结果 (共{len(results)}个):")
        for i, result in enumerate(results, 1):
            print(f"  {i}. {result['summary']} (分数: {result['score']:.4f}, 索引: {result['index']})")
        
        # 分析分数分布
        if results:
            scores = [r['score'] for r in results]
            print(f"\n分数分析:")
            print(f"  最高分: {max(scores):.4f}")
            print(f"  最低分: {min(scores):.4f}")
            print(f"  平均分: {sum(scores)/len(scores):.4f}")
            
            # 建议阈值
            max_score = max(scores)
            if max_score > 0.5:
                suggested_threshold = max_score * 0.3
            elif max_score > 0.2:
                suggested_threshold = max_score * 0.5
            else:
                suggested_threshold = max_score * 0.8
            
            print(f"  建议阈值: {suggested_threshold:.4f}")
        
    except Exception as e:
        print(f"✗ 直接测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 运行调试测试
    asyncio.run(test_direct_rerank())
    asyncio.run(debug_rerank_threshold())

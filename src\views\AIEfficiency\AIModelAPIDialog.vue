<template>
  <a-modal
    :visible="visible"
    @update:visible="emit('update:visible', $event)"
    :title="`${modelData.name} API 文档`"
    @cancel="handleCancel"
    width="800px"
    class="api-dialog"
    :bodyStyle="{ padding: '24px' }"
    :footer="null"
  >
    <div class="api-content">
      <div class="api-section">
        <h3>基本信息</h3>
        <div class="info-item">
          <div class="label">接口地址：</div>
          <div class="value">
            <a-tag>{{ modelData.url }}</a-tag>
            <a-button type="link" size="small" @click="copyUrl">
              <template #icon><CopyOutlined /></template>
              复制
            </a-button>
          </div>
        </div>
      </div>


      <div class="api-section">
        <h3>请求方法</h3>
        <a-tag color="blue">POST</a-tag>
      </div>



      <div class="api-section">
        <h3>请求参数</h3>
        <a-table :dataSource="requestParams" :columns="paramColumns" :pagination="false" size="small">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'required'">
              <a-tag :color="record.required ? 'red' : 'default'">
                {{ record.required ? '是' : '否' }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </div>



      <div class="api-section">
        <h3>示例请求</h3>
        <div class="code-section">
          <div class="code-header">
            <h4>JSON格式</h4>
            <a-button type="link" size="small" @click="copyJsonExample">
              <template #icon><CopyOutlined /></template>
              复制
            </a-button>
          </div>
          <a-card class="code-block">
            <pre>{
  "model": "{{ modelData.name }}",
  "messages": [
    {
      "role": "user",
      "content": "你好"
    }
  ],
  "max_tokens": 1000,
  "temperature": 0.7
}</pre>
        </a-card>
      </div>
    </div>

      <div class="api-section">
        <div class="code-header">
          <h4>curl命令</h4>
          <a-button type="link" size="small" @click="copyCurlExample">
            <template #icon><CopyOutlined /></template>
            复制
          </a-button>
        </div>
        <a-card class="code-block">
          <pre>curl {{ modelData.url }}/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "{{ modelData.name }}",
    "messages": [
      {
        "role": "user",
        "content": "你好"
      }
    ],
    "max_tokens": 1000,
    "temperature": 0.7
  }'</pre>
        </a-card>
      </div>

      <div class="api-section">
        <h3>响应格式</h3>
        <a-card class="code-block">
          <pre>{
  "id": "string",
  "model": "{{ modelData.name }}",
  "created": 1703862000,
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "你好！有什么我可以帮你的吗？"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 10,
    "completion_tokens": 15,
    "total_tokens": 25
  }
}</pre>
        </a-card>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { CopyOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

interface ModelData {
  name: string;
  url: string;
}

const props = defineProps<{
  visible: boolean;
  modelData: ModelData;
}>();

const emit = defineEmits(['update:visible']);

const handleCancel = () => {
  emit('update:visible', false);
};

const copyUrl = () => {
  navigator.clipboard.writeText(props.modelData.url);
  message.success('已复制到剪贴板');
};

const copyJsonExample = () => {
  const jsonExample = JSON.stringify({
    model: props.modelData.name,
    messages: [
      {
        role: 'user',
        content: '你好'
      }
    ],
    max_tokens: 1000,
    temperature: 0.7
  }, null, 2);
  navigator.clipboard.writeText(jsonExample);
  message.success('已复制JSON示例到剪贴板');
};

const copyCurlExample = () => {
  const curlCmd = `curl ${props.modelData.url}/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "${props.modelData.name}",
    "messages": [
      {
        "role": "user",
        "content": "你好"
      }
    ],
    "max_tokens": 1000,
    "temperature": 0.7
  }'`;
  navigator.clipboard.writeText(curlCmd);
  message.success('已复制curl命令到剪贴板');
};

const requestParams = [
  {
    key: '1',
    param: 'model',
    type: 'string',
    required: true,
    description: '模型名称',
  },
  {
    key: '2',
    param: 'messages',
    type: 'array',
    required: true,
    description: '对话消息数组，包含role和content字段',
  },
  {
    key: '3',
    param: 'max_tokens',
    type: 'number',
    required: false,
    description: '生成文本的最大长度',
  },
  {
    key: '4',
    param: 'temperature',
    type: 'number',
    required: false,
    description: '采样温度，控制输出的随机性（0-1）',
  },
];

const paramColumns = [
  {
    title: '参数名',
    dataIndex: 'param',
    key: 'param',
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '是否必需',
    dataIndex: 'required',
    key: 'required',
  },
  {
    title: '说明',
    dataIndex: 'description',
    key: 'description',
  },
];
</script>

<style scoped>
.api-content {
  max-height: 70vh;
  overflow-y: auto;
}

.api-section {
  margin-bottom: 24px;
}

.api-section h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.label {
  width: 80px;
  color: #8c8c8c;
}

.value {
  display: flex;
  align-items: center;
  gap: 8px;
}

.code-block {
  background: #f5f5f5;
}

.code-block pre {
  margin: 0;
  padding: 8px;
  font-family: 'Courier New', Courier, monospace;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
package com.qfnu.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("sys_feature")
@ApiModel(value = "SysFeaturePO", description = "SysFeature实体类")
public class SysFeaturePO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 
     */
    @TableId(value = "feature_id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "")
    private String featureId;

    /**
     * 
     */
    @TableField(value = "description")
    @ApiModelProperty(value = "")
    private String description;

    /**
     * 
     */
    @TableField(value = "creator")
    @ApiModelProperty(value = "")
    private String creator;

    /**
     * 
     */
    @TableField(value = "modifier")
    @ApiModelProperty(value = "")
    private String modifier;

    /**
     * 
     */
    @TableField(value = "dept_belong_id")
    @ApiModelProperty(value = "")
    private String deptBelongId;

    /**
     * 
     */
    @TableField(value = "update_datetime")
    @ApiModelProperty(value = "")
    private Date updateDatetime;

    /**
     * 
     */
    @TableField(value = "create_datetime")
    @ApiModelProperty(value = "")
    private Date createDatetime;

    /**
     * 
     */
    @TableField(value = "module_name")
    @ApiModelProperty(value = "")
    private String moduleName;

    /**
     * 
     */
    @TableField(value = "person_responsible")
    @ApiModelProperty(value = "")
    private String personResponsible;

    /**
     * 
     */
    @TableField(value = "group_responsible")
    @ApiModelProperty(value = "")
    private String groupResponsible;

    /**
     * 
     */
    @TableField(value = "level")
    @ApiModelProperty(value = "")
    private Long level;

    /**
     * 
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "")
    private String isDeleted;

    /**
     * 
     */
    @TableField(value = "parent_id")
    @ApiModelProperty(value = "")
    private Long parentId;

    /**
     * 
     */
    @TableField(value = "product_id")
    @ApiModelProperty(value = "")
    private Long productId;



    @TableField(value = "parent_name")
    private String parentName;

    @TableField(exist = false)
    @ApiModelProperty(value = "子节点列表")
    private List<SysFeaturePO> children;
}
package com.qfnu.common;

import com.qfnu.common.exception.custom.PermissionDeniedException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseVO<String> handleIllegalArgumentException(IllegalArgumentException e) {
        return ResponseVO.validFail(400, "参数错误：" + e.getMessage());
    }
    @ExceptionHandler(PermissionDeniedException.class)
    public ResponseVO<String> handlePermissionDeniedException(PermissionDeniedException e) {
        return ResponseVO.validFail(403, "没有权限操作");
    }
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseVO<String> handleException(Exception e) {
        return ResponseVO.validFail(500, "服务器内部错误：" + e.getMessage());
    }
}

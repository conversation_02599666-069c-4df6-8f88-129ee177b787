<!-- The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work. -->
<template>
  <div class="min-h-screen bg-gray-50">
    <div class="flex min-h-screen">
      <SubNavigation v-model:isCollapsed="isCollapsed" />
      <!-- 主内容区域 -->
      <main :class="['flex-1 p-6', isCollapsed ? 'ml-16' : 'ml-48', 'transition-all duration-300']">
        <Breadcrumb />
        <router-view />
      </main>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import SubNavigation from '@/components/navigation/SubNavigation.vue';
import Breadcrumb from '@/components/navigation/Breadcrumb.vue';

const isCollapsed = ref(false);
</script>
<style scoped>
:deep(.ant-input) {
  border: 1px solid #d9d9d9;
}

:deep(.ant-input:focus) {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

:deep(.ant-input:hover) {
  border-color: #40a9ff;
}

:deep(input[type="number"]::-webkit-inner-spin-button),
:deep(input[type="number"]::-webkit-outer-spin-button) {
  -webkit-appearance: none;
  margin: 0;
}
</style>
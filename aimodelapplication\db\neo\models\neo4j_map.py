from typing import List, Dict, Any, Optional
from db.neo.neo4j_pool import Neo4jPool
from db.mysql.models.knowledge_base import KnowledgeBaseModel
from logger import logging
import json

logger = logging()

class Neo4jModel:
    def __init__(self, labels: List[str], uid: Optional[str] = None):
        """
        初始化Neo4j模型
        
        Args:
            labels: 节点标签列表
            uid: 知识库ID，用于查询知识库信息
        """
        self.pool = None
        self.driver = None
        self.kb_info = None
        self.uid = uid
        self.labels = labels  # 临时保存原始标签
        
    async def initialize(self):
        """
        初始化Neo4j连接池和知识库信息
        """
        kb_model = None
        try:
            # 如果提供了uid，则先初始化知识库信息
            if self.uid:
                kb_model = KnowledgeBaseModel()
                await kb_model.initialize()
                self.kb_info = await kb_model.find_by_id(self.uid)
                
            # 从知识库信息中获取Neo4j连接参数
            if self.kb_info:
                host = self.kb_info.get('host')
                port = self.kb_info.get('port')
                user = self.kb_info.get('user')
                password = self.kb_info.get('password')
                db_name = self.kb_info.get('db')
                tags = self.kb_info.get('tags', None)
                
                # 处理tags为标签列表
                if tags:
                    # 将tags字符串按、分割，只清理特殊字符，保持原始大小写
                    self.labels = [self._clean_label(tag) for tag in tags.split('、') if tag.strip()]
                else:
                    # 如果没有tags，使用默认标签
                    self.labels = None

                # 初始化Neo4j连接池
                self.pool = Neo4jPool(
                    host=host,
                    port=port,
                    user=user,
                    password=password,
                    db_name=db_name
                )
            else:
                # 如果没有知识库信息，使用默认标签
                self.labels = None
                self.pool = Neo4jPool()
                
            await self.pool.initialize()
            self.driver = await self.pool._get_driver()
            
        except Exception as e:
            logger.error(f"初始化Neo4j连接失败: {str(e)}")
            raise
        finally:
            # 确保关闭MySQL连接
            if kb_model:
                await kb_model.close()
        
    async def _init_kb_info(self, uid: str):
        """
        初始化知识库信息
        
        Args:
            uid: 知识库ID
        """
        kb_model = None
        try:
            kb_model = KnowledgeBaseModel()
            await kb_model.initialize()
            self.kb_info = await kb_model.find_by_id(uid)
        except Exception as e:
            logger.error(f"初始化知识库信息失败: {str(e)}")
            raise
        finally:
            if kb_model:
                await kb_model.close()
        
    def _clean_label(self, label: str) -> str:
        """
        清理标签名称，只处理特殊字符，保持原始大小写
        
        Args:
            label: 原始标签名称
            
        Returns:
            str: 清理后的标签名称
        """
        # 只替换特殊字符为下划线，保持原始大小写
        return label.strip().replace('-', '_').replace('.', '_').replace(':', '_')
        
    def _process_properties(self, properties: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理属性，将复杂对象转换为字符串
        
        Args:
            properties: 原始属性字典
            
        Returns:
            Dict[str, Any]: 处理后的属性字典
        """
        processed = {}
        for key, value in properties.items():
            if isinstance(value, (dict, list)):
                # 将复杂对象转换为JSON字符串
                processed[key] = json.dumps(value, ensure_ascii=False)
            else:
                processed[key] = value
        return processed
        
    async def close(self):
        """
        关闭Neo4j连接池
        """
        if self.pool:
            await self.pool.close()
            
    def _build_label_str(self):
        """
        构建标签字符串
        
        Returns:
            str: 标签字符串
        """
        if self.labels:
            return ':'.join(self.labels)
        else:
            return None

        
    def _build_properties(self, properties: Dict[str, Any]) -> str:
        """
        构建属性字符串
        
        Args:
            properties: 属性字典
            
        Returns:
            str: 属性字符串
        """
        return ', '.join(f"{k}: ${k}" for k in properties.keys())
        
    def _build_set_clause(self, properties: Dict[str, Any]) -> str:
        """
        构建SET子句
        
        Args:
            properties: 属性字典
            
        Returns:
            str: SET子句
        """
        return ', '.join(f"n.{k} = ${k}" for k in properties.keys())
            
    async def create(self, properties: Dict[str, Any]) -> bool:
        """
        创建节点
        
        Args:
            properties: 节点属性
            
        Returns:
            bool: 是否创建成功
        """
        try:
            # 处理属性
            processed_properties = self._process_properties(properties)
            
            # 构建Cypher查询
            query = f"""
                CREATE (n:{self._build_label_str()} {{{self._build_properties(processed_properties)}}})
                RETURN n
            """
            
            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query, processed_properties)
                return await result.single() is not None
            
        except Exception as e:
            logger.error(f"创建节点失败: {str(e)}")
            return False
            
    async def create_relationship(
        self,
        source_id: str,
        target_id: str,
        relation_type: str,
        properties: Dict[str, Any] = None
    ) -> bool:
        """
        创建关系
        
        Args:
            source_id: 源节点ID
            target_id: 目标节点ID
            relation_type: 关系类型
            properties: 关系属性
            
        Returns:
            bool: 是否创建成功
        """
        try:
            # 处理属性
            processed_properties = self._process_properties(properties or {})
            
            # 构建Cypher查询
            query = f"""
                MATCH (source:{self._build_label_str()} {{id: $source_id}})
                MATCH (target:{self._build_label_str()} {{id: $target_id}})
                CREATE (source)-[r:{relation_type} {{{self._build_properties(processed_properties)}}}]->(target)
                RETURN r
            """
            
            # 添加节点ID到参数
            params = {
                "source_id": source_id,
                "target_id": target_id,
                **processed_properties
            }
            
            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query, params)
                return await result.single() is not None
            
        except Exception as e:
            logger.error(f"创建关系失败: {str(e)}")
            return False
            
    async def find_by_property(self, property_name: str, value: Any) -> List[Dict[str, Any]]:
        """
        根据属性查找节点
        
        Args:
            property_name: 属性名
            value: 属性值
            
        Returns:
            List[Dict[str, Any]]: 节点列表
        """
        try:
            # 处理属性值
            processed_value = self._process_properties({property_name: value})[property_name]
            
            # 构建Cypher查询
            query = f"""
                MATCH (n:{self._build_label_str()} {{{property_name}: $value}})
                RETURN n
            """
            
            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query, {"value": processed_value})
                return [dict(record["n"]) async for record in result]
            
        except Exception as e:
            logger.error(f"根据属性查找节点失败: {str(e)}")
            return []
            
    async def find_all(self) -> List[Dict[str, Any]]:
        """
        查找所有节点
        
        Returns:
            List[Dict[str, Any]]: 节点列表
        """
        try:
            # 构建Cypher查询
            if self._build_label_str():
                query = f"""
                    MATCH (n:{self._build_label_str()})
                    RETURN n
                """
            else:
                query = f"""MATCH (n) RETURN n"""
            
            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query)
                records = [dict(record["n"]) async for record in result]
                # 处理返回的数据
                return [self._process_neo4j_value(record) for record in records]
            
        except Exception as e:
            logger.error(f"查找所有节点失败: {str(e)}")
            return []

    async def find_all_custom(self, cypher) -> List[Dict[str, Any]]:
        """
        页面构建全自定义查找

        Returns:
            List[Dict[str, Any]]: 节点列表
        """
        try:

            query = cypher

            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query)
                records = [dict(record["n"]) async for record in result]
                # 处理返回的数据
                return [self._process_neo4j_value(record) for record in records]

        except Exception as e:
            logger.error(f"查找自定义节点失败: {str(e)}")
            return []


    async def find_custom(self) -> List[Dict[str, Any]]:
        """
        自定义查找

        Returns:
            List[Dict[str, Any]]: 节点列表
        """
        try:
            # 构建Cypher查询
            if self._build_label_str():
                query = f"""
                    MATCH (n:{self._build_label_str()})
                    RETURN n
                """
            else:
                query = f"""MATCH (n) RETURN n"""

            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query)
                records = [dict(record["n"]) async for record in result]
                # 处理返回的数据
                return [self._process_neo4j_value(record) for record in records]

        except Exception as e:
            logger.error(f"查找所有节点失败: {str(e)}")
            return []

    async def update(self, node_id: str, properties: Dict[str, Any]) -> bool:
        """
        更新节点
        
        Args:
            node_id: 节点ID
            properties: 要更新的属性
            
        Returns:
            bool: 是否更新成功
        """
        try:
            # 处理属性
            processed_properties = self._process_properties(properties)
            
            # 构建Cypher查询
            query = f"""
                MATCH (n:{self._build_label_str()} {{id: $node_id}})
                SET {self._build_set_clause(processed_properties)}
                RETURN n
            """
            
            # 添加节点ID到参数
            params = {"node_id": node_id, **processed_properties}
            
            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query, params)
                return await result.single() is not None
            
        except Exception as e:
            logger.error(f"更新节点失败: {str(e)}")
            return False
            
    async def delete_relationship(self, relationship_id: str) -> bool:
        """
        根据ID删除关系
        
        Args:
            relationship_id: 关系ID
            
        Returns:
            bool: 是否删除成功
        """
        query = """
        MATCH ()-[r]->()
        WHERE r.id = $relationship_id
        DELETE r
        RETURN count(r) as deleted_count
        """
        parameters = {"relationship_id": relationship_id}
        try:
            async with self.driver.session() as session:
                result = await session.run(query, parameters)
                summary = await result.consume()
                # 检查是否有关系被删除
                if summary.counters.relationships_deleted > 0:
                    logger.info(f"成功删除关系，ID: {relationship_id}")
                    return True
                else:
                    logger.warning(f"未找到要删除的关系，ID: {relationship_id}")
                    return False
        except Exception as e:
            logger.error(f"删除关系失败，ID: {relationship_id}, 错误: {e}", exc_info=True)
            return False
            
    async def delete(self, node_id: str) -> bool:
        """
        删除节点及其相关的所有关系
        
        Args:
            node_id: 节点ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 构建Cypher查询，先删除与节点相关的所有关系，再删除节点本身
            query = f"""
                MATCH (n:{self._build_label_str()} {{id: $node_id}})
                OPTIONAL MATCH (n)-[r]-()  // 匹配所有与节点相关的关系（包括入边和出边）
                DELETE r, n  // 先删除关系，再删除节点
            """
            
            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query, {"node_id": node_id})
                # 检查是否有节点被删除
                summary = await result.consume()
                return summary.counters.nodes_deleted > 0
            
        except Exception as e:
            logger.error(f"删除节点及其关系失败: {str(e)}")
            return False
            
    # async def find_relationships(self) -> List[Dict[str, Any]]:
    #     """
    #     查找所有关系
    #
    #     Returns:
    #         List[Dict[str, Any]]: 关系列表
    #     """
    #     try:
    #         # 构建Cypher查询
    #         if self._build_label_str():
    #             query = f"""
    #                 MATCH (source:{self._build_label_str()})-[r]->(target:{self._build_label_str()})
    #                 RETURN
    #                     r.id as id,
    #                     source.id as source,
    #                     target.id as target,
    #                     type(r) as type,
    #                     r.description as description,
    #                     r.version as version
    #             """
    #         else:
    #             query = f"""
    #                 MATCH (source)-[r]->(target)
    #                 RETURN
    #                     r.id as id,
    #                     source.id as source,
    #                     target.id as target,
    #                     type(r) as type,
    #                     r.description as description,
    #                     r.version as version
    #             """
    #
    #         # 执行查询
    #         async with self.driver.session() as session:
    #             result = await session.run(query)
    #             records = [{
    #                 "id": record["id"],
    #                 "source": record["source"],
    #                 "target": record["target"],
    #                 "type": record["type"],
    #                 "description": record["description"] if record["description"] else "",
    #                 "version": record["version"] if record["version"] else "V1"
    #             } async for record in result]
    #             # 处理返回的数据
    #             return [self._process_neo4j_value(record) for record in records]
    #
    #     except Exception as e:
    #         logger.error(f"查找关系失败: {str(e)}")
    #         return []

    # async def find_relationships_custom(self, nodes: List[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    #     """
    #     查找自定义节点关系，可以基于指定的节点列表查找关系
    #
    #     Args:
    #         nodes (List[Dict[str, Any]], optional): 节点列表，如果提供则只查找这些节点之间的关系
    #
    #     Returns:
    #         List[Dict[str, Any]]: 关系列表
    #     """
    #     try:
    #         # 构建Cypher查询
    #         if nodes:
    #             # 从节点列表中提取所有节点ID
    #             node_ids = [node.get('id') for node in nodes if node.get('id')]
    #             if not node_ids:
    #                 return []
    #
    #             # 构建节点ID列表的字符串表示
    #             node_ids_str = ', '.join([f"'{id}'" for id in node_ids])
    #
    #             query = f"""
    #                 MATCH (source)-[r]->(target)
    #                 WHERE source.id IN [{node_ids_str}] AND target.id IN [{node_ids_str}]
    #                 RETURN
    #                     r.id as id,
    #                     source.id as source,
    #                     target.id as target,
    #                     type(r) as type,
    #                     r.description as description,
    #                     r.version as version
    #             """
    #         else:
    #             if self._build_label_str():
    #                 query = f"""
    #                     MATCH (source:{self._build_label_str()})-[r]->(target:{self._build_label_str()})
    #                     RETURN
    #                         r.id as id,
    #                         source.id as source,
    #                         target.id as target,
    #                         type(r) as type,
    #                         r.description as description,
    #                         r.version as version
    #                 """
    #             else:
    #                 query = f"""
    #                     MATCH (source)-[r]->(target)
    #                     RETURN
    #                         r.id as id,
    #                         source.id as source,
    #                         target.id as target,
    #                         type(r) as type,
    #                         r.description as description,
    #                         r.version as version
    #                 """
    #
    #         # 执行查询
    #         async with self.driver.session() as session:
    #             result = await session.run(query)
    #             records = [{
    #                 "id": record["id"],
    #                 "source": record["source"],
    #                 "target": record["target"],
    #                 "type": record["type"],
    #                 "description": record["description"] if record["description"] else "",
    #                 "version": record["version"] if record["version"] else "V1"
    #             } async for record in result]
    #             # 处理返回的数据
    #             return [self._process_neo4j_value(record) for record in records]
    #
    #     except Exception as e:
    #         logger.error(f"查找关系失败: {str(e)}")
    #         return []
    async def find_relationships(self) -> List[Dict[str, Any]]:
        """
        查找所有关系，并返回关系上的全部属性信息

        Returns:
            List[Dict[str, Any]]: 关系列表，每个关系包含所有键值对
        """
        try:
            # 构建Cypher查询，使用 properties(r) 获取所有属性
            if self._build_label_str():
                query = f"""
                    MATCH (source:{self._build_label_str()})-[r]->(target:{self._build_label_str()})
                    RETURN
                        r.id as id,
                        source.id as source,
                        target.id as target,
                        type(r) as type,
                        properties(r) as properties  /* 返回关系的所有属性 */
                """
            else:
                query = f"""
                    MATCH (source)-[r]->(target)
                    RETURN
                        r.id as id,
                        source.id as source,
                        target.id as target,
                        type(r) as type,
                        properties(r) as properties  /* 返回关系的所有属性 */
                """

            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query)
                records = [{
                    "id": record["id"],
                    "source": record["source"],
                    "target": record["target"],
                    "type": record["type"],
                    **record["properties"]  # 展开所有属性
                } async for record in result]

                # 处理Neo4j特殊类型（如DateTime）
                return [self._process_neo4j_value(record) for record in records]

        except Exception as e:
            logger.error(f"查找关系失败: {str(e)}")
            return []

    async def find_relationships_custom(self, nodes: List[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        查找自定义节点关系，可以基于指定的节点列表查找关系

        Args:
            nodes (List[Dict[str, Any]], optional): 节点列表，如果提供则只查找这些节点之间的关系

        Returns:
            List[Dict[str, Any]]: 关系列表，每个关系包含所有键值对
        """
        try:
            # 构建Cypher查询
            if nodes:
                # 从节点列表中提取所有节点ID
                node_ids = [node.get('id') for node in nodes if node.get('id')]
                if not node_ids:
                    return []

                # 构建节点ID列表的字符串表示
                node_ids_str = ', '.join([f"'{id}'" for id in node_ids])

                query = f"""
                    MATCH (source)-[r]->(target)
                    WHERE source.id IN [{node_ids_str}] AND target.id IN [{node_ids_str}]
                    RETURN 
                        r.id as id,
                        source.id as source,
                        target.id as target,
                        type(r) as type,
                        properties(r) as properties  
                """
            else:
                if self._build_label_str():
                    query = f"""
                        MATCH (source:{self._build_label_str()})-[r]->(target:{self._build_label_str()})
                        RETURN 
                            r.id as id,
                            source.id as source,
                            target.id as target,
                            type(r) as type,
                            properties(r) as properties  
                    """
                else:
                    query = f"""
                        MATCH (source)-[r]->(target)
                        RETURN 
                            r.id as id,
                            source.id as source,
                            target.id as target,
                            type(r) as type,
                            properties(r) as properties  
                    """

            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query)
                records = [{
                    "id": record["id"],
                    "source": record["source"],
                    "target": record["target"],
                    "type": record["type"],
                    **record["properties"]  # 展开所有属性
                } async for record in result]
                # 处理返回的数据
                return [self._process_neo4j_value(record) for record in records]

        except Exception as e:
            logger.error(f"查找关系失败: {str(e)}")
            return []

    async def get_categories(self) -> List[str]:
        """
        获取所有类型
        
        Returns:
            List[str]: 类型列表
        """
        try:
            # 构建Cypher查询
            if self._build_label_str():
                query = f"""
                    MATCH (n:{self._build_label_str()})
                    RETURN DISTINCT n.type as type
                """
            else:
                query = f"""
                    MATCH (n)
                    RETURN DISTINCT n.type as type
                """
                
            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query)
                return [record["type"] async for record in result if record["type"]]
            
        except Exception as e:
            logger.error(f"获取类型失败: {str(e)}")
            return []

    async def get_categories_custom(self, nodes: List[Dict[str, Any]] = None) -> List[str]:
        """
        获取自定义节点类型，可以基于指定的节点列表获取类型

        Args:
            nodes (List[Dict[str, Any]], optional): 节点列表，如果提供则只获取这些节点的类型

        Returns:
            List[str]: 类型列表
        """
        try:
            if nodes:
                # 从节点列表中提取所有类型
                types = set()
                for node in nodes:
                    if isinstance(node, dict) and 'type' in node:
                        types.add(node['type'])
                return list(types)
            else:
                # 构建Cypher查询
                if self._build_label_str():
                    query = f"""
                        MATCH (n:{self._build_label_str()})
                        RETURN DISTINCT n.type as type
                    """
                else:
                    query = f"""
                        MATCH (n)
                        RETURN DISTINCT n.type as type
                    """

                # 执行查询
                async with self.driver.session() as session:
                    result = await session.run(query)
                    return [record["type"] async for record in result if record["type"]]

        except Exception as e:
            logger.error(f"获取类型失败: {str(e)}")
            return []


    async def delete_all_by_label(self) -> int:
        """
        根据当前模型的标签，删除所有节点及其关系
        
        Returns:
            int: 删除的节点数量
        """
        try:
            # 构建Cypher查询，先删除与节点相关的所有关系，再删除节点本身
            query = f"""
                MATCH (n:{self._build_label_str()})
                OPTIONAL MATCH (n)-[r]-()
                DELETE r, n
            """
            
            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query)
                # 获取删除的节点数量
                summary = await result.consume()
                return summary.counters.nodes_deleted
            
        except Exception as e:
            logger.error(f"批量删除节点及关系失败: {str(e)}")
            return 0
            
    async def get_node_properties(self) -> Dict[str, Dict[str, str]]:
        """
        获取所有节点的属性及其类型
        
        Returns:
            Dict[str, Dict[str, str]]: 属性类型字典，格式为：
            {
                "属性名": {
                    "type": "类型",
                    "description": "描述"
                }
            }
        """
        try:
            # 构建Cypher查询，获取所有节点的属性
            if self._build_label_str():
                query = f"""
                    MATCH (n:{self._build_label_str()})
                    WITH n
                    RETURN properties(n) as props
                """
            else:
                query = f"""
                    MATCH (n)
                    WITH n
                    RETURN properties(n) as props
                """
            
            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query)
                property_types = {}
                
                # 收集所有节点的属性
                async for record in result:
                    props = record["props"]
                    if not props:
                        continue
                        
                    # 分析每个属性的类型
                    for key, value in props.items():
                        if key in property_types:
                            continue  # 如果属性已经分析过，跳过
                            
                        if value is None:
                            property_types[key] = {
                                "type": "null",
                                "description": "空值"
                            }
                        elif isinstance(value, bool):
                            property_types[key] = {
                                "type": "boolean",
                                "description": "布尔值"
                            }
                        elif isinstance(value, int):
                            property_types[key] = {
                                "type": "integer",
                                "description": "整数"
                            }
                        elif isinstance(value, float):
                            property_types[key] = {
                                "type": "float",
                                "description": "浮点数"
                            }
                        elif isinstance(value, str):
                            property_types[key] = {
                                "type": "string",
                                "description": "字符串"
                            }
                        elif isinstance(value, list):
                            property_types[key] = {
                                "type": "array",
                                "description": "数组"
                            }
                        elif isinstance(value, dict):
                            property_types[key] = {
                                "type": "object",
                                "description": "对象"
                            }
                        else:
                            property_types[key] = {
                                "type": str(type(value).__name__),
                                "description": "其他类型"
                            }
                            
                return property_types
            
        except Exception as e:
            logger.error(f"获取节点属性类型失败: {str(e)}")
            return {}
            
    async def get_node_types(self) -> List[str]:
        """
        获取所有节点的类型
        
        Returns:
            List[str]: 节点类型列表
        """
        try:
            # 构建Cypher查询，获取所有节点的类型
            query = f"""
                MATCH (n:{self._build_label_str()})
                RETURN DISTINCT n.type as type
                ORDER BY type
            """
            
            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query)
                return [record["type"] async for record in result if record["type"]]
            
        except Exception as e:
            logger.error(f"获取节点类型失败: {str(e)}")
            return []

    async def search_entities(self, field: str, value: str, method: str = "cover", topk: int = 10, target_types: List[str] = None) -> List[Dict]:
        """
        根据字段和匹配方式查找实体
        
        Args:
            field: 要搜索的字段名
            value: 要搜索的值
            method: 匹配方式，可选值：
                - cover: 包含匹配
                - head: 开头匹配
                - end: 结尾匹配
                - precise: 精确匹配
            topk: 返回的最大结果数量
            target_types: 目标实体类型列表，如果提供则只返回这些类型的实体
            
        Returns:
            List[Dict]: 匹配的实体列表
        """
        try:
            # 构建匹配条件
            if method == "cover":
                condition = f"n.{field} CONTAINS $value"
            elif method == "head":
                condition = f"n.{field} STARTS WITH $value"
            elif method == "end":
                condition = f"n.{field} ENDS WITH $value"
            elif method == "precise":
                condition = f"n.{field} = $value"
            else:
                raise ValueError(f"不支持的匹配方式: {method}")
            
            # 添加类型过滤条件
            type_condition = ""
            if target_types:
                type_condition = f"AND n.type IN $target_types"
            
            # 构建查询
            query = f"""
            MATCH (n:{self._build_label_str()})
            WHERE {condition} {type_condition}
            RETURN n
            LIMIT $topk
            """
            
            # 准备参数
            params = {
                "value": value,
                "topk": topk
            }
            if target_types:
                params["target_types"] = target_types
            
            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query, params)
                return [dict(record["n"]) async for record in result]
            
        except Exception as e:
            logger.error(f"搜索实体失败: {str(e)}")
            return []

    async def find_entity_relations(self, entity_id: str) -> List[Dict]:
        """
        查找实体的所有一级关系
        
        Args:
            entity_id: 实体ID
            
        Returns:
            List[Dict]: 实体列表，每个实体包含：
                - entity: 目标实体的完整数据
                - relation: 关系类型
        """
        try:
            # 构建查询
            query = f"""
            MATCH (source:{self._build_label_str()} {{id: $entity_id}})
            MATCH (source)-[r]->(target:{self._build_label_str()})
            RETURN target as entity, type(r) as relation
            """
            
            entities = []
            
            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query, {
                    "entity_id": entity_id
                })
                
                # 收集结果
                async for record in result:
                    entity_data = dict(record["entity"])
                    entity_data['relation'] = record["relation"]
                    entities.append(entity_data)
            return entities
            
        except Exception as e:
            logger.error(f"查找实体关系失败: {str(e)}")
            return []

    def _process_neo4j_value(self, value: Any) -> Any:
        """
        处理Neo4j返回的值，将特殊类型转换为可序列化的格式
        
        Args:
            value: Neo4j返回的值
            
        Returns:
            处理后的值
        """
        from neo4j.time import DateTime
        from datetime import datetime
        
        if isinstance(value, DateTime):
            return value.isoformat()
        elif isinstance(value, datetime):
            return value.isoformat()
        elif isinstance(value, dict):
            return {k: self._process_neo4j_value(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [self._process_neo4j_value(item) for item in value]
        return value

    async def find_nodes_by_type(self, node_type: str):
        """
        查询指定类型的节点并转换为标准字典列表

        Args:
            node_type: 节点类型，如 "API"

        Returns:
            List[Dict]: 标准字典格式的节点列表
        """
        query = f"MATCH (n:{self.labels[0]}) WHERE n.type = $node_type RETURN n"
        #
        # # 打印调试信息
        # print(f"Neo4j查询: {query}")
        # print(f"查询参数: node_type={node_type}")
        # print(f"使用的标签: {self.labels[0]}")

        # 正确解构execute_query的返回值
        records, summary, keys = await self.driver.execute_query(
            query,
            node_type=node_type
        )

        # print(f"Neo4j查询结果数量: {len(records)}")

        result = [
            self._process_neo4j_value(dict(record["n"])) for record in records
        ]

        # 如果结果为空，尝试查询所有节点看看有什么
        if not result:
            print("查询结果为空，尝试查询所有节点...")
            all_query = f"MATCH (n:{self.labels[0]}) RETURN n.type, count(*) as count"
            all_records, _, _ = await self.driver.execute_query(all_query)
            print("数据库中的节点类型统计:")
            for record in all_records:
                print(f"  类型: {record['n.type']} - 数量: {record['count']}")

        return result

    def _process_neo4j_value(self, value: Any) -> Any:
        from neo4j.time import DateTime
        from datetime import datetime

        if isinstance(value, DateTime):
            return value.isoformat()
        elif isinstance(value, datetime):
            return value.isoformat()
        elif isinstance(value, dict):
            return {k: self._process_neo4j_value(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [self._process_neo4j_value(item) for item in value]
        return value

    async def find_node_by_method_and_path(self, method: str, path: str):
        query = (
            f"MATCH (n:{self.labels[0]}) "
            "WHERE n.method = $method AND n.path = $path "
            "RETURN n LIMIT 1"
        )
        result = await self.driver.execute_query(query, method=method, path=path)
        return result.records[0].data()['n'] if result.records else None

    async def find_orphaned_entities(self) -> List[Dict[str, Any]]:
        """
        查找没有关系的实体节点（孤儿实体）

        Returns:
            List[Dict[str, Any]]: 孤儿实体列表
        """
        try:
            query = f"""
                MATCH (n:{self._build_label_str()})
                WHERE NOT (n)--()
                RETURN n
            """

            async with self.driver.session() as session:
                result = await session.run(query)
                records = [dict(record["n"]) async for record in result]
                return [self._process_neo4j_value(record) for record in records]

        except Exception as e:
            logger.error(f"查找孤儿实体失败: {str(e)}")
            return []

    async def count_orphaned_entities(self) -> int:
        """
        查询没有关系的实体节点（孤儿实体）的数量

        Returns:
            int: 孤儿实体的数量
        """
        try:
            query = f"""
                MATCH (n:{self._build_label_str()})
                WHERE NOT (n)--()
                RETURN COUNT(n) AS count
            """

            async with self.driver.session() as session:
                result = await session.run(query)
                record = await result.single()
                return record["count"] if record else 0

        except Exception as e:
            logger.error(f"查询孤儿实体数量失败: {str(e)}")
            return 0

    async def get_api_record_by_id(self, api_id: str) -> Optional[Dict[str, Any]]:
        """
        根据API ID获取API记录

        Args:
            api_id (str): API的唯一标识符

        Returns:
            Optional[Dict[str, Any]]: API记录的字典表示，如果未找到则返回None
        """
        try:
            # 构建Cypher查询语句，根据ID查找节点
            query = f"""
                MATCH (n:{self._build_label_str()} {{id: $api_id}})
                RETURN n
            """

            # 执行查询
            async with self.driver.session() as session:
                result = await session.run(query, {"api_id": api_id})
                record = await result.single()

                if record:
                    # 将Neo4j节点转换为字典并处理特殊值
                    node_data = dict(record["n"])
                    return self._process_neo4j_value(node_data)
                else:
                    return None

        except Exception as e:
            logger.error(f"根据ID查找API记录失败: {str(e)}")
            return None




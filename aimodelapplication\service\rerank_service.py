#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Rerank和向量化服务
提供gte-rerank-v2模型的重排序功能和文本向量化功能
"""

import json
import requests
import jieba
import jieba.posseg as pseg
from typing import List, Dict, Tuple, Optional
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

logger = logging.getLogger(__name__)


class RerankService:
    """重排序服务"""
    
    def __init__(self, api_key: str):
        """
        初始化重排序服务
        
        Args:
            api_key: 阿里百炼API密钥
        """
        self.api_key = api_key
        self.rerank_url = "https://dashscope.aliyuncs.com/api/v1/services/rerank/text-rerank/text-rerank"
        self.embedding_url = "https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding"
        self.rerank_model = "gte-rerank-v2"
        self.embedding_model = "text-embedding-v2"
        
        # 初始化jieba（延迟初始化，在第一次使用时自动初始化）
        # jieba.initialize()  # 注释掉，让jieba在第一次使用时自动初始化
    
    async def rerank_summaries(self, query: str, summaries: List[str], top_k: int = 5) -> List[Dict]:
        """
        使用gte-rerank-v2模型对summaries进行重排序
        
        Args:
            query: 查询文本（前置依赖）
            summaries: 待排序的summary列表
            top_k: 返回前k个结果
            
        Returns:
            List[Dict]: 排序后的结果，包含summary、score、index
        """
        if not summaries:
            return []
        
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                "model": self.rerank_model,
                "input": {
                    "query": query,
                    "documents": summaries
                },
                "parameters": {
                    "top_k": min(top_k, len(summaries))
                }
            }
            
            logger.info(f"调用rerank服务，查询: {query}, 文档数量: {len(summaries)}")
            
            # 使用asyncio.to_thread进行异步HTTP请求
            response = await asyncio.to_thread(
                requests.post, 
                self.rerank_url, 
                headers=headers, 
                json=payload,
                timeout=30
            )
            
            if response.status_code != 200:
                raise Exception(f"Rerank API调用失败: HTTP {response.status_code}, {response.text}")
            
            result = response.json()
            logger.debug(f"Rerank API原始响应: {result}")

            # 检查响应格式并适配不同的API版本
            rerank_results = []

            # 尝试不同的响应格式
            if 'output' in result and 'results' in result['output']:
                # 标准格式
                results_data = result['output']['results']
            elif 'results' in result:
                # 简化格式
                results_data = result['results']
            elif 'data' in result:
                # 另一种格式
                results_data = result['data']
            else:
                raise Exception(f"Rerank API返回格式不支持: {result}")

            # 解析结果 - 阿里百炼rerank API返回的是索引和分数，需要根据索引获取原始文档
            for item in results_data:
                try:
                    # 获取索引和分数
                    index = item.get('index', 0)
                    score = item.get('relevance_score', 0.0)

                    # 根据索引获取原始文档
                    if 0 <= index < len(summaries):
                        document = summaries[index]
                    else:
                        logger.warning(f"索引 {index} 超出范围，跳过该结果")
                        continue

                    rerank_results.append({
                        'summary': document,
                        'score': float(score),
                        'index': int(index)
                    })

                except Exception as item_error:
                    logger.error(f"解析rerank结果失败: {str(item_error)}, 原始数据: {item}")
                    continue
            
            logger.info(f"Rerank完成，返回 {len(rerank_results)} 个结果")
            return rerank_results
            
        except Exception as e:
            logger.error(f"Rerank服务调用失败: {str(e)}")
            raise
    
    def extract_verbs_with_jieba(self, text: str) -> List[str]:
        """
        使用jieba提取文本中的动词
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 动词列表，按出现顺序排列
        """
        try:
            # 使用jieba进行词性标注
            words = pseg.cut(text)
            verbs = []
            
            for word, flag in words:
                # 提取动词（v开头的词性标记）
                if flag.startswith('v') and len(word) > 1:  # 过滤单字动词
                    verbs.append(word)
            
            # 去重但保持顺序
            unique_verbs = []
            for verb in verbs:
                if verb not in unique_verbs:
                    unique_verbs.append(verb)
            
            logger.debug(f"从文本 '{text}' 中提取到动词: {unique_verbs}")
            return unique_verbs
            
        except Exception as e:
            logger.error(f"动词提取失败: {str(e)}")
            return []
    
    async def get_text_embedding(self, text: str) -> List[float]:
        """
        获取文本的向量表示
        
        Args:
            text: 输入文本
            
        Returns:
            List[float]: 文本向量
        """
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                "model": self.embedding_model,
                "input": {
                    "texts": [text]
                }
            }
            
            # 使用asyncio.to_thread进行异步HTTP请求
            response = await asyncio.to_thread(
                requests.post,
                self.embedding_url,
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code != 200:
                raise Exception(f"Embedding API调用失败: HTTP {response.status_code}, {response.text}")
            
            result = response.json()
            
            if 'output' not in result or 'embeddings' not in result['output']:
                raise Exception(f"Embedding API返回格式错误: {result}")
            
            embedding = result['output']['embeddings'][0]['embedding']
            logger.debug(f"获取文本 '{text}' 的向量，维度: {len(embedding)}")
            
            return embedding
            
        except Exception as e:
            logger.error(f"向量化服务调用失败: {str(e)}")
            raise
    
    async def calculate_verb_similarity(self, verb1: str, verb2: str) -> float:
        """
        计算两个动词的语义相似度
        
        Args:
            verb1: 第一个动词
            verb2: 第二个动词
            
        Returns:
            float: 相似度分数 (0-1)
        """
        try:
            # 获取两个动词的向量
            embedding1 = await self.get_text_embedding(verb1)
            embedding2 = await self.get_text_embedding(verb2)
            
            # 计算余弦相似度
            vec1 = np.array(embedding1).reshape(1, -1)
            vec2 = np.array(embedding2).reshape(1, -1)
            
            similarity = cosine_similarity(vec1, vec2)[0][0]
            
            logger.debug(f"动词相似度: '{verb1}' vs '{verb2}' = {similarity:.4f}")
            return float(similarity)
            
        except Exception as e:
            logger.error(f"计算动词相似度失败: {str(e)}")
            return 0.0


class PrerequisiteMatchingService:
    """前置依赖匹配服务"""
    
    def __init__(self, api_key: str):
        """
        初始化匹配服务
        
        Args:
            api_key: 阿里百炼API密钥
        """
        self.rerank_service = RerankService(api_key)
        self.semantic_threshold = 0.1   # 语义相似度阈值（降低以便调试）
        self.verb_threshold = 0.2      # 动词相似度阈值（降低以便调试）
    
    async def find_best_matching_api(self, prerequisite: str, api_summaries: List[Dict]) -> Optional[Dict]:
        """
        为单个前置依赖找到最匹配的API
        
        Args:
            prerequisite: 前置依赖描述
            api_summaries: API summary列表，格式: [{"id": "1", "summary": "xxx", ...}, ...]
            
        Returns:
            Optional[Dict]: 最匹配的API信息，包含风险级别
        """
        if not api_summaries:
            return None
        
        try:
            # 1. 提取所有summary文本
            summaries = [api.get('summary', '') for api in api_summaries]
            
            # 2. 使用rerank模型进行语义相似度排序
            logger.info(f"开始为前置依赖 '{prerequisite}' 进行rerank匹配")
            rerank_results = await self.rerank_service.rerank_summaries(
                query=prerequisite,
                summaries=summaries,
                top_k=5
            )
            
            if not rerank_results:
                logger.warning(f"前置依赖 '{prerequisite}' 未找到任何匹配结果")
                return None

            # 显示前几个rerank结果用于调试
            logger.info(f"前置依赖 '{prerequisite}' 的rerank结果:")
            for i, result in enumerate(rerank_results[:5], 1):
                logger.info(f"  {i}. {result['summary']} (分数: {result['score']:.4f})")

            # 3. 检查最高分是否超过语义阈值
            best_result = rerank_results[0]
            semantic_score = best_result['score']

            logger.info(f"前置依赖 '{prerequisite}' 最高语义相似度: {semantic_score:.4f}, 阈值: {self.semantic_threshold}")

            if semantic_score < self.semantic_threshold:
                logger.warning(f"前置依赖 '{prerequisite}' 最高语义相似度 {semantic_score:.4f} 低于阈值 {self.semantic_threshold}，跳过匹配")
                return None
            
            # 4. 提取前置依赖和最匹配summary的动词
            prerequisite_verbs = self.rerank_service.extract_verbs_with_jieba(prerequisite)
            summary_verbs = self.rerank_service.extract_verbs_with_jieba(best_result['summary'])
            
            if not prerequisite_verbs or not summary_verbs:
                logger.warning(f"无法提取动词: 前置依赖='{prerequisite}', summary='{best_result['summary']}'")
                verb_score = 0.0
            else:
                # 5. 计算动词相似度（取第一个动词）
                verb_score = await self.rerank_service.calculate_verb_similarity(
                    prerequisite_verbs[0], summary_verbs[0]
                )
            
            # 6. 计算综合相似度
            final_score = (semantic_score + verb_score) / 2
            
            # 7. 确定风险级别
            risk_level = self._calculate_risk_level(semantic_score, verb_score, final_score)
            
            # 8. 获取对应的API信息
            matched_api = api_summaries[best_result['index']]
            
            result = {
                **matched_api,
                'semantic_score': semantic_score,
                'verb_score': verb_score,
                'final_score': final_score,
                'risk_level': risk_level,
                'prerequisite': prerequisite,
                'matched_summary': best_result['summary'],
                'prerequisite_verbs': prerequisite_verbs,
                'summary_verbs': summary_verbs
            }
            
            logger.info(f"前置依赖 '{prerequisite}' 匹配成功: {matched_api.get('summary', '')} "
                       f"(语义: {semantic_score:.4f}, 动词: {verb_score:.4f}, 综合: {final_score:.4f}, 风险: {risk_level})")
            
            return result
            
        except Exception as e:
            logger.error(f"前置依赖匹配失败: {str(e)}")
            return None
    
    def _calculate_risk_level(self, semantic_score: float, verb_score: float, final_score: float) -> str:
        """
        计算风险级别
        
        Args:
            semantic_score: 语义相似度
            verb_score: 动词相似度
            final_score: 综合相似度
            
        Returns:
            str: 风险级别 (低/中/高)
        """
        # 风险级别判断逻辑
        if final_score >= 0.8 and semantic_score >= 0.7 and verb_score >= 0.6:
            return "低"
        elif final_score >= 0.6 and semantic_score >= 0.5:
            return "中"
        else:
            return "高"
    
    async def process_multiple_prerequisites(self, prerequisites: List[str], api_summaries: List[Dict]) -> List[Dict]:
        """
        并行处理多个前置依赖
        
        Args:
            prerequisites: 前置依赖列表
            api_summaries: API summary列表
            
        Returns:
            List[Dict]: 匹配结果列表
        """
        if not prerequisites:
            return []
        
        logger.info(f"开始并行处理 {len(prerequisites)} 个前置依赖")
        
        # 使用asyncio.gather并行处理
        tasks = [
            self.find_best_matching_api(prerequisite, api_summaries)
            for prerequisite in prerequisites
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤掉异常和None结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"前置依赖 '{prerequisites[i]}' 处理失败: {str(result)}")
            elif result is not None:
                valid_results.append(result)
        
        logger.info(f"并行处理完成，成功匹配 {len(valid_results)} 个前置依赖")
        return valid_results

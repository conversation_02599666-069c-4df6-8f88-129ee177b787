<template>
  <div class="recall-test-container">
    <!-- 左侧容器：检索设置 -->
    <div class="left-panel">
      <div class="panel-header">
        <h3 class="panel-title">检索设置</h3>
      </div>
      <div class="panel-content">
        <a-card
                title="检索模式"
                :bordered="false"
                :headStyle="{ height: '20px', backgroundColor: '#eaecf0' }"
                :style="{ border: '1px solid blue' }"
                style="width: 100%;"
            >
                <div style="background-color: #fffbe6; padding: 8px; border-radius: 4px; border: 1px solid #ffe58f; margin-bottom: 16px;">
                  <span style="display: flex; align-items: center;">
                    <info-circle-outlined style="color: #faad14; margin-right: 8px;" />
                    <span>当前设置仅临时修改，只用于本次召回测试</span>
                  </span>
                </div>
                
                <div class="setting-section">
                    <div class="setting-title" style="text-align: left; margin-bottom: 12px;">检索模式</div>
                    <a-radio-group 
                        v-model:value="retrievalMode" 
                        button-style="solid" 
                        style="display: flex; width: 50%; border: 1px solid #d9d9d9; border-radius: 4px; overflow: hidden;"
                    >
                        <a-radio-button value="exact" style="flex: 1; text-align: center; border-right: 1px solid #d9d9d9; border-top: none; border-bottom: none; border-left: none;">
                            模糊匹配
                        </a-radio-button>
                        <a-radio-button value="vector" style="flex: 1; text-align: center; border: none;" class="disabled-btn" disabled>
                            向量检索
                        </a-radio-button>
                    </a-radio-group>
                </div>
                
                <a-card 
                    :bordered="false" 
                    :headStyle="{ height: '20px', backgroundColor: '#eaecf0' }"
                    :style="{ marginTop: '16px', border: '1px solid #d9d9d9' }"
                    style="width: 100%;"
                >
                    <div style="display: flex; gap: 16px;">
                        <div style="flex: 1;">
                            <div class="setting-title" style="text-align: left; margin-bottom: 8px;">TopK</div>
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                <a-input-number 
                                    v-model:value="retrievalCount" 
                                    :min="1" 
                                    :max="50" 
                                    style="width: 80px;" 
                                    @change="handleTopKChange"
                                />
                                <a-slider 
                                    v-model:value="retrievalCount" 
                                    :min="1" 
                                    :max="50" 
                                    style="flex: 1;" 
                                    @change="handleTopKChange"
                                />
                            </div>
                            <div style="font-size: 12px; color: rgba(0, 0, 0, 0.45);">
                                返回的最大结果数量
                            </div>
                        </div>
                        <div style="flex: 1;" v-if="retrievalMode === 'vector'">
                            <div class="setting-title" style="text-align: left; margin-bottom: 8px;">Score阈值</div>
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                <a-input-number 
                                    v-model:value="similarityThresholdPercent" 
                                    :min="0" 
                                    :max="100" 
                                    :formatter="value => `${value}%`"
                                    :parser="value => value.replace('%', '')"
                                    style="width: 80px;" 
                                    @change="handleThresholdPercentChange"
                                />
                                <a-slider 
                                    v-model:value="similarityThreshold" 
                                    :min="0" 
                                    :max="1" 
                                    :step="0.01" 
                                    style="flex: 1;" 
                                    @change="handleThresholdChange"
                                />
                            </div>
                            <div style="font-size: 12px; color: rgba(0, 0, 0, 0.45); display: flex; justify-content: space-between;">
                                <span>低相关性</span>
                                <span>高相关性</span>
                            </div>
                        </div>
                    </div>
                </a-card>
                
                <!-- 模糊匹配检索维度卡片 -->
                <div v-if="retrievalMode === 'exact'" class="setting-title" style="text-align: left; margin-bottom: 12px; margin-top: 12px;">检索设置</div>
                <a-card 
                    v-if="retrievalMode === 'exact'"
                    :bordered="false" 
                    :headStyle="{ height: '20px', backgroundColor: '#eaecf0' }"
                    :style="{ marginTop: '16px', border: '1px solid #d9d9d9' }"
                    style="width: 100%;"
                >
                    <div class="setting-title" style="text-align: left; margin-bottom: 12px;">匹配字段</div>
                    <div style="margin-bottom: 20px;">
                        <div style="display: flex; gap: 12px; margin-bottom: 12px;">
                            <!-- 实体类型选择 -->
                            <div style="flex: 1;">
                                <a-select 
                                    v-model:value="selectedMatchEntityType" 
                                    placeholder="选择实体类型" 
                                    style="width: 100%"
                                    @change="onMatchEntityTypeChange"
                                >
                                    <a-select-option 
                                        v-for="type in entityTypes" 
                                        :key="type.value" 
                                        :value="type.value"
                                    >
                                        {{ type.label }}
                                    </a-select-option>
                                </a-select>
                            </div>
                        </div>
                    </div>

                    <div class="setting-title" style="text-align: left; margin-bottom: 12px;">匹配方式</div>
                    <a-radio-group v-model:value="fuzzyMatchType" style="width: 100%">
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <a-radio value="contains">
                                <span>包含（检索结果包含查询文本的任意部分）</span>
                            </a-radio>
                            <a-radio value="startsWith">
                                <span>开头匹配（检索结果以查询文本开头）</span>
                            </a-radio>
                            <a-radio value="endsWith">
                                <span>结尾匹配（检索结果以查询文本结尾）</span>
                            </a-radio>
                            <a-radio value="exact">
                                <span>精确匹配（检索结果与查询文本完全一致）</span>
                            </a-radio>
                        </div>
                    </a-radio-group>
                </a-card>
                
                <!-- 返回类型卡片 -->
                <div class="setting-title" style="text-align: left; margin-bottom: 12px; margin-top: 12px;">返回类型</div>
                <a-card 
                    :bordered="false" 
                    :headStyle="{ height: '20px', backgroundColor: '#eaecf0' }"
                    :style="{ marginTop: '16px', border: '1px solid #d9d9d9' }"
                    style="width: 100%;"
                >
                    <div style="margin-bottom: 16px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>实体类型</span>
                            <a @click="selectAllEntityTypes" style="color: #1890ff; cursor: pointer;">{{ isAllEntityTypesSelected ? '取消全选' : '全选' }}</a>
                        </div>
                        <a-checkbox-group v-model:value="selectedEntityTypes" style="width: 100%">
                            <div style="display: flex; flex-wrap: wrap; gap: 12px; max-height: 120px; overflow-y: auto; padding: 8px; border: 1px solid #f0f0f0; border-radius: 4px;">
                                <a-checkbox 
                                    v-for="entityType in nodeTypes" 
                                    :key="entityType.value" 
                                    :value="entityType.value"
                                    style="min-width: 90px; margin-right: 8px;"
                                >
                                    {{ entityType.label }}
                                </a-checkbox>
                            </div>
                        </a-checkbox-group>
                    </div>
                </a-card>
            </a-card>
            
            <!-- 模型设置卡片，仅在向量检索模式下显示 -->
            <a-card
                v-if="retrievalMode === 'vector'"
                title="模型设置"
                :bordered="false"
                :headStyle="{ height: '20px', backgroundColor: '#eaecf0' }"
                :style="{ border: '1px solid blue', marginTop: '16px' }"
                style="width: 100%;"
            >
                <div style="background-color: #fffbe6; padding: 8px; border-radius: 4px; border: 1px solid #ffe58f; margin-bottom: 16px;">
                  <span style="display: flex; align-items: center;">
                    <info-circle-outlined style="color: #faad14; margin-right: 8px;" />
                    <span>当前模型选择仅临时修改，只用于本次召回测试</span>
                  </span>
                </div>
                
                <div class="setting-section">
                    <div class="setting-title" style="text-align: left; margin-bottom: 12px;">Embedding模型</div>
                    <a-select 
                        v-model:value="embeddingModel" 
                        style="width: 100%;"
                        placeholder="选择Embedding模型"
                    >
                        <a-select-option value="bge-base-zh">BGE Base ZH</a-select-option>
                        <a-select-option value="bge-large-zh">BGE Large ZH</a-select-option>
                        <a-select-option value="bge-large-en">BGE Large EN</a-select-option>
                        <a-select-option value="text2vec">Text2Vec</a-select-option>
                        <a-select-option value="m3e-base">M3E Base</a-select-option>
                    </a-select>
                    <div style="font-size: 12px; color: rgba(0, 0, 0, 0.45); margin-top: 4px;">
                        用于将文本转换为向量的模型
                    </div>
                </div>
                
                <div v-if="hybridMode === 'rerank'" class="setting-section" style="margin-top: 20px;">
                    <div class="setting-title" style="text-align: left; margin-bottom: 12px;">Rerank模型</div>
                    <a-select 
                        v-model:value="rerankModel" 
                        style="width: 100%;"
                        placeholder="选择Rerank模型"
                    >
                        <a-select-option value="bge-reranker-base">BGE Reranker Base</a-select-option>
                        <a-select-option value="bge-reranker-large">BGE Reranker Large</a-select-option>
                        <a-select-option value="bce-reranker-base">BCE Reranker Base</a-select-option>
                        <a-select-option value="cohere-rerank">Cohere Rerank</a-select-option>
                    </a-select>
                    <div style="font-size: 12px; color: rgba(0, 0, 0, 0.45); margin-top: 4px;">
                        用于重排检索结果的模型，仅在Rerank模式下生效
                    </div>
                    
                </div>
            </a-card>
      </div>
    </div>
    
    <!-- 中间容器 -->
    <div class="middle-panel">
      <!-- 上方容器：召回测试 -->
      <div class="test-panel">
        <div class="panel-header">
          <h3 class="panel-title">召回测试</h3>
        </div>
        <div class="test-content">
          <a-textarea 
            v-model:value="queryText" 
            placeholder="请输入要测试的文本查询..." 
            :auto-size="{ minRows: 4, maxRows: 8 }"
            class="query-input"
          />
          <div class="action-bar">
            <a-button 
              type="primary" 
              @click="performRecall" 
              :loading="loading"
            >
              开始召回
            </a-button>
          </div>
          
          <!-- 历史记录部分 -->
          <div class="history-section">
            <div class="history-header">
              <div class="header-title">历史记录</div>
            </div>
            
            <div class="history-list" v-if="historyRecords.length > 0">
              <div 
                v-for="(item, index) in historyRecords" 
                :key="index" 
                class="history-item"
                @click="loadHistoryRecord(item)"
              >
                <div class="history-item-content">
                  <div class="history-query">{{ item.query }}</div>
                  <div class="history-meta">
                    <span>{{ item.time }}</span>
                    <span>{{ item.source || '本地知识库' }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="empty-history" v-else>
              <a-empty description="暂无历史记录" />
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 右侧容器：召回展示 -->
    <div class="right-panel">
      <div class="panel-header">
        <h3 class="panel-title">召回结果</h3>
        <div class="result-meta" v-if="recallResults.length > 0">
          共召回 {{ recallResults.length }} 条内容
        </div>
      </div>
      <div class="result-container">
        <div v-if="recallResults.length > 0" class="result-list">
          <div 
            v-for="(result, index) in recallResults" 
            :key="index" 
            class="result-item"
          >
            <a-descriptions :column="2" bordered>
              <!-- 动态展示主数据的所有字段 -->
              <template v-for="(value, key) in result" :key="key">
                <template v-if="key !== 'relations' && value !== undefined && value !== null && value !== ''">
                  <a-descriptions-item 
                    :label="formatLabel(key)" 
                    :span="shouldSpanFullWidth(value) ? 2 : 1"
                  >
                    <template v-if="typeof value === 'object'">
                      <pre>{{ JSON.stringify(value, null, 2) }}</pre>
                          </template>
                    <template v-else>
                      {{ value }}
                          </template>
                  </a-descriptions-item>
                </template>
                </template>
                
              <!-- 关联关系展示 -->
              <a-descriptions-item label="关联关系" :span="2">
                <div v-if="result.relations && result.relations.length > 0">
                  <div v-for="(relation, relIndex) in result.relations" :key="relIndex" class="relation-item">
                    <a-descriptions :column="2" bordered size="small">
                      <template v-for="(value, key) in relation" :key="key">
                        <template v-if="value !== undefined && value !== null && value !== ''">
                          <a-descriptions-item 
                            :label="formatLabel(key)"
                            :span="shouldSpanFullWidth(value) ? 2 : 1"
                          >
                            <template v-if="typeof value === 'object'">
                              <pre>{{ JSON.stringify(value, null, 2) }}</pre>
                          </template>
                            <template v-else>
                              {{ value }}
                </template>
                  </a-descriptions-item>
                          </template>
                </template>
              </a-descriptions>
            </div>
            </div>
                <div v-else class="no-relations">
                  暂无关联关系
                </div>
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </div>
        <div v-else-if="loading" class="loading-results">
          <a-spin tip="正在召回中...">
          </a-spin>
        </div>
        <div v-else class="empty-results">
          <inbox-outlined style="font-size: 48px; color: #d9d9d9; margin-bottom: 16px;" />
          <p>暂无召回结果，请输入查询内容进行测试</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { 
  InboxOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import http from '@/utils/ai/http'

// 检索设置
const retrievalCount = ref(8)
const similarityThreshold = ref(0.7)
const similarityThresholdPercent = ref(70) // 百分比形式的相似度阈值
const retrievalMode = ref('exact') // 默认为精确检索，可选值：exact(模糊匹配), vector(向量检索)
const hybridMode = ref('weight') // 默认为权重模式，可选值：weight(权重模式), rerank(Rerank模式)

// 模糊匹配设置
const fuzzyMatchType = ref('contains') // 默认为包含匹配

// 匹配字段设置
const selectedMatchEntityType = ref(''); // 当前选择的实体类型
const selectedMatchParameter = ref(''); // 当前选择的实体参数
const entityParameters = ref([]); // 当前实体类型的参数列表
const matchFields = ref([]); // 已添加的匹配字段

// 查询文本
const queryText = ref('')
const loading = ref(false)

// 历史记录
const historyRecords = ref([])

// 获取召回历史记录
const fetchRecallHistory = async () => {
  try {
    // 从当前URL中获取relation_id
    const currentPath = window.location.pathname;
    const relationId = currentPath.split('/relation/')[1];
    
    if (!relationId) {
      message.error('无法获取知识库ID');
      return;
    }

    // 获取用户信息
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    if (!userInfo.userId || !userInfo.username) {
      message.error('用户信息获取失败');
      return;
    }

    // 调用获取召回历史接口
    const res = await http.get(`/knowledge_api/${relationId}/recall_history`, {
      params: {
        username: userInfo.username,
        user_id: userInfo.userId
      }
    });

    if (res.data.code === 200) {
      // 处理返回的数据，将 time 转换为 date
      historyRecords.value = (res.data.data?.history || []).map(record => ({
        query: record.content,
        time: record.time,
        count: 0, // 由于API响应中没有count字段，暂时设置为0
        source: record.type || '本地知识库'
      }));
    } else {
      message.error(res.data.message || '获取召回历史失败');
    }
  } catch (error) {
    console.error('获取召回历史失败:', error);
    message.error('获取召回历史失败');
  }
};


// 加载历史记录
const loadHistoryRecord = (item) => {
  queryText.value = item.query
  performRecall()
}

// 召回结果
const recallResults = ref([])


// 执行召回
const performRecall = async () => {
  
    // 提示用户填写必要信息
    let errorMessage = "无法执行召回测试，请检查以下内容：";
    const reasons = [];
    
    if (!queryText.value.trim()) {
      reasons.push("查询文本不能为空");
    }
    
      if (!selectedMatchEntityType.value) {
    reasons.push("至少选择一种匹配字段");
    }
    
  // 如果有错误，显示提示并返回
  if (reasons.length > 0) {
    errorMessage += reasons.join("、");
    message.error(errorMessage);
    return;
  }
  
  loading.value = true;
  
  try {
    // 从当前URL中获取relation_id
    const currentPath = window.location.pathname;
    const relationId = currentPath.split('/relation/')[1];
    
    if (!relationId) {
      message.error('无法获取知识库ID');
      return;
    }

    // 获取用户信息
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    if (!userInfo.userId || !userInfo.username) {
      message.error('用户信息获取失败');
      return;
    }

    // 构建请求参数
    const params = {
      content: queryText.value,
      topk: retrievalCount.value,
      field: selectedMatchEntityType.value,
      method: fuzzyMatchType.value === 'contains' ? 'cover' : 
              fuzzyMatchType.value === 'startsWith' ? 'head' : 
              fuzzyMatchType.value === 'endsWith' ? 'end' : 'precise',
      target_type: selectedEntityTypes.value,
      type: retrievalMode.value === 'exact' ? 'txt' : 'embedding'
    };

    // 如果是向量模式，添加额外的参数
    if (retrievalMode.value === 'vector') {
      params.threshold = similarityThreshold.value;
      params.model = embeddingModel.value;
    }

    // 调用搜索接口
    const res = await http.post(`/knowledge_api/${relationId}/search_relations`, params, {
        params: {
        username: userInfo.username,
        user_id: userInfo.userId
      }
    });

    if (res.data.code === 200) {
      // 处理返回的数据
      recallResults.value = res.data.data.map(item => ({
        title: item.name,
        content: item.description,
        source: item.id,
        section: item.version,
        score: 1.0, // 由于API响应中没有score字段，暂时设置为1.0
        type: item.type,
        relations: item.relations.map(relation => ({
          title: relation.name,
          content: relation.description,
          source: relation.id,
          section: relation.version,
          score: 1.0,
          type: relation.type,
          relation: relation.relation
        }))
      }));
    
    // 添加到历史记录
    historyRecords.value.unshift({
      query: queryText.value,
      time: new Date().toLocaleString(),
      count: recallResults.value.length,
      source: retrievalMode.value === 'exact' ? '模糊匹配' : '向量检索'
      });
    } else {
      message.error(res.data.message || '搜索失败');
    }
  } catch (error) {
    console.error('搜索失败:', error);
    message.error('搜索失败');
  } finally {
    loading.value = false;
  }
}

// 根据分数获取标签颜色
const getScoreColor = (score) => {
  if (score >= 0.8) return 'success'
  if (score >= 0.6) return 'processing'
  if (score >= 0.4) return 'warning'
  return 'default'
}

// 处理TopK变化
const handleTopKChange = (value) => {
  retrievalCount.value = value
  // 可以在这里添加TopK与阈值的关联逻辑
  // 例如：当TopK增大时，可以适当降低阈值
  if (value > 20 && similarityThreshold.value > 0.8) {
    similarityThreshold.value = 0.8
    similarityThresholdPercent.value = 80
  }
}

// 处理阈值变化（从滑块）
const handleThresholdChange = (value) => {
  similarityThreshold.value = value
  // 更新百分比形式的值
  similarityThresholdPercent.value = Math.round(value * 100)
}

// 处理阈值百分比变化（从输入框）
const handleThresholdPercentChange = (value) => {
  similarityThresholdPercent.value = value
  // 更新小数形式的值
  similarityThreshold.value = value / 100
}

// 权重设置
const semanticWeight = ref(50)
const keywordWeight = ref(50)

// 处理语义权重变化
const handleSemanticWeightChange = (value) => {
  semanticWeight.value = value
  // 自动计算关键词权重
  keywordWeight.value = 100 - value
}

// 模型设置
const embeddingModel = ref('bge-large-zh')
const rerankModel = ref('bge-reranker-base')
const initialRetrievalCount = ref(50)

// 实体类型列表
const entityTypes = ref([]);
const nodeTypes = ref([]);

// 获取节点属性
const fetchNodeProperties = async () => {
  try {
    // 从当前URL中获取relation_id
    const currentPath = window.location.pathname;
    const relationId = currentPath.split('/relation/')[1];
    
    if (!relationId) {
      message.error('无法获取知识库ID');
      return;
    }

    // 获取用户信息
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    if (!userInfo.userId || !userInfo.username) {
      message.error('用户信息获取失败');
      return;
    }

    // 调用获取节点属性接口
    const res = await http.get(`/knowledge_api/${relationId}/node_properties`, {
      params: {
        username: userInfo.username,
        user_id: userInfo.userId
      }
    });

    if (res.data.code === 200) {
      // 将properties转换为entityTypes格式（用于匹配字段）
      const properties = res.data.data.properties;
      entityTypes.value = Object.keys(properties).map(key => ({
        value: key,
        label: key
      }));
      
      // 设置node_types（用于返回类型）
      nodeTypes.value = res.data.data.node_types.map(type => ({
        value: type,
        label: type
      }));
      
      // 初始化selectedEntityTypes为所有node_types
      selectedEntityTypes.value = nodeTypes.value.map(item => item.value);
    } else {
      message.error(res.data.message || '获取节点属性失败');
    }
  } catch (error) {
    console.error('获取节点属性失败:', error);
    message.error('获取节点属性失败');
  }
};

// 组件挂载时加载节点属性
onMounted(() => {
  fetchNodeProperties();
  fetchRecallHistory();
});

// 已选择的实体类型和关系类型
const selectedEntityTypes = ref([]);

// 计算是否全部实体类型被选中
const isAllEntityTypesSelected = computed(() => {
  return selectedEntityTypes.value.length === nodeTypes.value.length;
});

// 全选/取消全选实体类型
const selectAllEntityTypes = () => {
  if (isAllEntityTypesSelected.value) {
    selectedEntityTypes.value = [];
  } else {
    selectedEntityTypes.value = nodeTypes.value.map(item => item.value);
  }
};

// 根据实体类型获取参数
const onMatchEntityTypeChange = (value) => {
  selectedMatchParameter.value = '';
  
  // 动态获取参数列表
  if (value === 'api') {
    entityParameters.value = [
      { value: 'url', label: 'URL' },
      { value: 'method', label: '请求方法' },
      { value: 'summary', label: '接口描述' },
      { value: 'path', label: '路径' }
    ];
  } else if (value === 'param') {
    entityParameters.value = [
      { value: 'type', label: '参数类型' },
      { value: 'required', label: '是否必填' },
      { value: 'description', label: '参数描述' },
      { value: 'defaultValue', label: '默认值' }
    ];
  } else {
    entityParameters.value = [];
  }
};

// 计算是否可以添加匹配字段
const canAddMatchField = computed(() => {
  return selectedMatchEntityType.value && selectedMatchParameter.value && 
    !matchFields.value.some(
      field => field.entityType === selectedMatchEntityType.value && 
              field.paramValue === selectedMatchParameter.value
    );
});

// 添加匹配字段
const addMatchField = () => {
  if (!canAddMatchField.value) return;
  
  const entityType = entityTypes.value.find(type => type.value === selectedMatchEntityType.value);
  const param = entityParameters.value.find(param => param.value === selectedMatchParameter.value);
  
  if (entityType && param) {
    matchFields.value.push({
      entityType: entityType.value,
      entityLabel: entityType.label,
      paramValue: param.value,
      paramLabel: param.label
    });
    
    // 清空选择，方便下次选择
    selectedMatchParameter.value = '';
  }
};

// 移除匹配字段
const removeMatchField = (index) => {
  matchFields.value.splice(index, 1);
};

// 清空所有匹配字段
const clearMatchFields = () => {
  matchFields.value = [];
  selectedMatchEntityType.value = '';
  selectedMatchParameter.value = '';
};

// 根据实体类型获取卡片颜色
const getEntityTypeColor = (type) => {
  switch(type) {
    case 'api':
      return '#e6f7ff'; // 浅蓝色背景，适合接口
    case 'param':
      return '#f6ffed'; // 浅绿色背景，适合参数
    case 'relation':
      return '#fff1f0'; // 浅红色背景，适合关系
    default:
      return '#f5f9ff'; // 默认背景色
  }
}

// 根据实体类型获取边框颜色
const getEntityTypeBorderColor = (type) => {
  switch(type) {
    case 'api':
      return '#91d5ff'; // 蓝色边框，适合接口
    case 'param':
      return '#b7eb8f'; // 绿色边框，适合参数
    case 'relation':
      return '#ffccc7'; // 红色边框，适合关系
    default:
      return '#e6f4ff'; // 默认边框色
  }
}

// 根据实体类型获取标签颜色
const getEntityTypeTagColor = (type) => {
  switch(type) {
    case 'api':
      return 'blue';
    case 'param':
      return 'green';
    case 'relation':
      return 'red';
    default:
      return 'default';
  }
}

// 获取HTTP方法对应的颜色
const getMethodColor = (method) => {
  switch(method) {
    case 'GET':
      return 'green';
    case 'POST':
      return 'blue';
    case 'PUT':
      return 'orange';
    case 'DELETE':
      return 'red';
    case 'PATCH':
      return 'purple';
    default:
      return 'default';
  }
}

// 获取参数表格列
const getParamColumns = (params) => {
  if (!params || !params.length) return [];
  
  // 获取第一个参数对象的所有键
  const firstParam = params[0];
  const columns = Object.keys(firstParam).map(key => {
    // 转换列名为更友好的显示
    let title = key;
    if (key === 'name') title = '参数名';
    else if (key === 'type') title = '类型';
    else if (key === 'required') title = '必填';
    else if (key === 'description') title = '描述';
    else if (key === 'in') title = '位置';
    else if (key === 'default') title = '默认值';
    
    return {
      title,
      dataIndex: key
    };
  });
  
  return columns;
};

// 检查请求数组中是否有描述字段
const hasDescriptionInRequests = (requests) => {
  if (!requests || !requests.length) return false;
  
  return requests.some(req => req.description && req.description.trim() !== '');
};

// 格式化标签名称
const formatLabel = (key) => {
  const labelMap = {
    'title': '名称',
    'content': '描述',
    'source': 'ID',
    'type': '类型',
    'section': '版本',
    'score': '分数',
    'method': '请求方法',
    'path': '路径',
    'params': '参数',
    'paramName': '参数名',
    'dataType': '数据类型',
    'isRequired': '是否必填',
    'description': '描述',
    'usedIn': '使用位置',
    'sourceEntity': '源实体',
    'targetEntity': '目标实体',
    'apis': '关联接口'
  };
  return labelMap[key] || key;
};

// 判断是否应该占满整行
const shouldSpanFullWidth = (value) => {
  if (typeof value === 'object' && value !== null) {
    return true;
  }
  
  if (typeof value === 'string') {
    // 如果文本长度超过30个字符，或者包含换行符，则占满整行
    return value.length > 30 || value.includes('\n');
  }
  
  return false;
};
</script>

<style scoped>
.recall-test-container {
  margin-top: 0.5%;
  margin-left: 0.5%;
  margin-right: 0.5%;
  display: flex;
  height: 81vh;
  background-color: #f0f2f5;
  gap: 12px;
}

.left-panel,
.middle-panel,
.right-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-height: 81vh;
}

.left-panel {
  width: 30%;
  min-width: 300px;
}

.middle-panel {
  width: 30%;
  display: flex;
  flex-direction: column;
}

.right-panel {
  width: 40%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid #f0f0f0;
}

.panel-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 8px 8px 0 0;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  margin: 0;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* 左侧检索设置样式 */
.setting-section {
  margin-bottom: 20px;
}

.setting-title {
  margin-bottom: 8px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.save-settings-btn {
  margin-top: 16px;
}

/* 中间面板样式 */
.test-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.test-content {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
}

.query-input {
  margin-bottom: 16px;
  border-radius: 4px;
}

.action-bar {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-bottom: 20px;
}

/* 历史记录样式 */
.history-section {
  margin-top: 20px;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.header-title {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.history-list {
  max-height: none;
  overflow-y: auto;
  margin-bottom: 12px;
  flex: 1;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 4px;
}

.history-item {
  padding: 10px 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fafafa;
  border-radius: 4px;
  margin-bottom: 8px;
  border: 1px solid #e9e9e9;
}

.history-item:last-child {
  margin-bottom: 0;
}

.history-item:hover {
  background-color: #f0f7ff;
}

.history-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-query {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 60%;
}

.history-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  white-space: nowrap;
}

.empty-history {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: rgba(0, 0, 0, 0.45);
}

/* 右侧结果展示样式 */
.result-container {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.result-meta {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 4px;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.result-item {
  margin-bottom: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s;
}

.result-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.relation-item {
  padding: 8px;
}

.relation-item:first-child {
  margin-top: 0;
}

.relation-item:last-child {
  margin-bottom: 0;
}

.no-relations {
  color: rgba(0, 0, 0, 0.45);
  text-align: center;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
}

pre {
  margin: 0;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  white-space: pre-wrap;
  word-wrap: break-word;
  border: 1px solid #f0f0f0;
}

/* 添加描述列表的自定义样式 */
:deep(.ant-descriptions) {
  background-color: #fff;
}

:deep(.ant-descriptions-item-label) {
  width: 100px;
  min-width: 100px;
  max-width: 200px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

:deep(.ant-descriptions-item-content) {
  word-break: break-word;
  white-space: pre-wrap;
  color: rgba(0, 0, 0, 0.65);
}

:deep(.ant-descriptions-bordered .ant-descriptions-item-label) {
  background-color: #fafafa;
}

:deep(.ant-descriptions-bordered .ant-descriptions-item-content) {
  padding: 16px 24px;
}

.loading-results {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background-color: #fafafa;
  border-radius: 8px;
}

.empty-results {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  border-radius: 8px;
  color: rgba(0, 0, 0, 0.45);
}

.disabled-btn {
  position: relative;
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    z-index: 1;
  }

  &::before {
    content: '🚫';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    opacity: 0;
    transition: opacity 0.2s;
    z-index: 3;
  }

  &:hover::before {
    opacity: 1;
  }

  span {
    position: relative;
    z-index: 2;
  }
}
</style> 
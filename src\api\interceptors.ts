import axios from "axios";
import { queryStringify } from "../utils/util";
import IMessage from "../components/iMessage/index";
import router from "../router";
import { ls } from "../utils/util";
import { notification } from "ant-design-vue";
notification.config({ maxCount: 1 });

function validateRepeat(config: any) {
  let lastUrl = "";
  let time = 0;
  const cancelSource = axios.CancelToken.source();
  const url = [config.method, config.url, queryStringify(config.params), queryStringify(config.data)].join("&");
  const intervalTime = Date.now() - time;
  if (url === lastUrl && intervalTime < 500) {
    config.cancelToken = cancelSource.token;
    cancelSource.cancel(`cancel token${config.url}`);
  } else {
    lastUrl = url;
    time = Date.now();
  }
}

export function reqResolve(config: any) {
  const token = ls.getItem("TOKEN");
  if (token) config.headers["Authorization"] = `Bearer ${token}`;
  validateRepeat(config);
  return config;
}

export function reqReject(error: any) {
  return Promise.reject(error);
}

export async function resReject(error: any) {
  if (error.response?.status === 403) {
    IMessage.error(error.response?.data?.message || "无权限操作");
    return Promise.reject(error);
  }
  if (error?.response?.status === 401) {
    notification.error({ message: error.response.data.msg, duration: 5 });
    setTimeout(() => {
      let account = ls.getItem("ACCOUNT");
      let password = ls.getItem("PASSWORD");
      let ifRemember = ls.getItem("REMEMBER");
      ls.clear();
      ls.setItem("ACCOUNT", account || "");
      ls.setItem("PASSWORD", password || "");
      ls.setItem("REMEMBER", ifRemember || "");
      router.push("/login");
    }, 1500);
  } else if (error.message.indexOf("30000") !== -1) {
    IMessage.error("请求超时");
  } else if (error.response?.data?.msg) {
    IMessage.destroy();
    IMessage.error(error.response.data.msg);
  } else if (error.request?.responseType === "blob" && error.response?.data) {
    const errText = await error.response.data.text();
    IMessage.error(JSON.parse(errText)?.msg);
  } else {
    IMessage.error("请求发生错误");
  }
}

export function resResolve(response: any) {
  return response;
} 
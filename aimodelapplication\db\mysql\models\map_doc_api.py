from typing import Dict, List, Any, Optional
from db.mysql.mysql_pool import MySQLPool
from logger import logging
import json
import uuid


class MapDocApiModel:
    """API文档映射模型类 - 专门处理 map_doc_api 表"""

    def __init__(self):
        self.table = "map_doc_api"
        self.primary_key = "id"
        self.logger = logging()
        self.db_pool = MySQLPool()

    async def initialize(self):
        """初始化连接池"""
        await self.db_pool.initialize()

    async def close(self):
        """关闭连接池，释放资源"""
        await self.db_pool.close()

    async def delete_doc_mapping_by_name(self, document_name: str, knowledge_base_uid: str) -> bool:
        """
        根据知识库uid和文档名称删除API映射记录

        Args:
            document_name: 文档名称
            knowledge_base_uid: 知识库UID

        Returns:
            bool: 删除是否成功
        """
        try:
            sql = f"""
                DELETE FROM {self.table}
                WHERE document_name = %s 
                AND knowledge_base_uid = %s
            """
            affected_rows = await self.db_pool.execute(sql, (document_name, knowledge_base_uid))

            if affected_rows > 0:
                self.logger.info(f"成功删除知识库 {knowledge_base_uid} 下的API映射记录: {document_name}")
                return True
            self.logger.warning(f"未找到知识库 {knowledge_base_uid} 下的API映射记录: {document_name}")
            return False

        except Exception as e:
            self.logger.error(f"删除API映射记录失败: {str(e)}")
            raise

    async def check_doc_name(self, document_name: str, knowledge_base_uid: str) -> bool:
        """
        检查知识库中是否存在同名API文档

        Args:
            document_name: 文档名称
            knowledge_base_uid: 知识库UID

        Returns:
            bool: 是否存在同名文档
        """
        try:
            sql = f"""
                SELECT COUNT(*) as count 
                FROM {self.table}
                WHERE document_name = %s 
                AND knowledge_base_uid = %s
            """
            result = await self.db_pool.fetch_one(sql, (document_name, knowledge_base_uid))

            if result and result.get("count", 0) > 0:
                self.logger.info(f"知识库 {knowledge_base_uid} 下存在同名API文档: {document_name}")
                return True
            return False

        except Exception as e:
            self.logger.error(f"检查同名API文档失败: {str(e)}")
            raise

    async def create_api_record(
            self,
            knowledge_base_uid: str,
            document_name: str,
            entity_name: str,
            path: str,
            method: str,
            tags: Optional[List[str]] = None,
            summary: Optional[str] = None,
            parameters: Optional[List[Dict]] = None,
            request_body: Optional[Dict] = None,
            responses: Optional[Dict] = None,
            status: str = "未嵌入"
    ) -> int:
        """
        创建API记录

        Args:
            knowledge_base_uid: 知识库UID
            document_name: 文档名称
            entity_name: 实体名称
            path: API路径
            method: HTTP方法
            tags: 标签列表
            summary: API摘要
            parameters: 参数列表
            request_body: 请求体
            responses: 响应
            status: 实体状态

        Returns:
            int: 插入记录的主键ID
        """
        try:
            sql = f"""
                INSERT INTO {self.table} (
                    knowledge_base_uid, document_name, entity_name,
                    entity_type, path, method, tags,
                    summary, parameters, request_body,
                    responses, status
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                knowledge_base_uid,
                document_name,
                entity_name[:100] if entity_name else "Unnamed API",  # 限制长度
                "API",
                path,
                method.upper(),
                json.dumps(tags) if tags else None,
                summary[:255] if summary else None,  # 限制长度
                json.dumps(parameters) if parameters else None,
                json.dumps(request_body) if request_body else None,
                json.dumps(responses) if responses else None,
                status
            )

            return await self.db_pool.execute(sql, params)

        except Exception as e:
            self.logger.error(f"创建API记录失败: {str(e)}")
            raise

    async def update_api_record(
            self,
            id: int,
            document_name: Optional[str] = None,
            entity_name: Optional[str] = None,
            path: Optional[str] = None,
            method: Optional[str] = None,
            tags: Optional[List[str]] = None,
            summary: Optional[str] = None,
            parameters: Optional[List[Dict]] = None,
            request_body: Optional[Dict] = None,
            responses: Optional[Dict] = None,
            status: Optional[str] = None
    ) -> bool:
        """
        更新API记录

        Args:
            id: 记录ID
            document_name: 文档名称
            entity_name: 实体名称
            path: API路径
            method: HTTP方法
            tags: 标签列表
            summary: API摘要
            parameters: 参数列表
            request_body: 请求体
            responses: 响应
            status: 实体状态

        Returns:
            bool: 是否更新成功
        """
        try:
            update_fields = []
            params = []

            if document_name is not None:
                update_fields.append("document_name = %s")
                params.append(document_name)
            if entity_name is not None:
                update_fields.append("entity_name = %s")
                params.append(entity_name)
            if path is not None:
                update_fields.append("path = %s")
                params.append(path)
            if method is not None:
                update_fields.append("method = %s")
                params.append(method.upper())
            if tags is not None:
                update_fields.append("tags = %s")
                params.append(json.dumps(tags))
            if summary is not None:
                update_fields.append("summary = %s")
                params.append(summary)
            if parameters is not None:
                update_fields.append("parameters = %s")
                params.append(json.dumps(parameters))
            if request_body is not None:
                update_fields.append("request_body = %s")
                params.append(json.dumps(request_body))
            if responses is not None:
                update_fields.append("responses = %s")
                params.append(json.dumps(responses))
            if status is not None:
                update_fields.append("status = %s")
                params.append(status)

            if not update_fields:
                return False

            sql = f"""
                UPDATE {self.table}
                SET {", ".join(update_fields)}
                WHERE id = %s
            """
            params.append(id)

            affected_rows = await self.db_pool.execute(sql, params)
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"更新API记录失败: {str(e)}")
            raise

    async def get_api_record_by_id(self, id: int) -> Dict[str, Any]:
        """
        根据ID获取API记录

        Args:
            id: 记录ID

        Returns:
            Dict[str, Any]: API记录
        """
        try:
            query = f"""
                SELECT * FROM {self.table}
                WHERE id = %s
            """
            result = await self.db_pool.fetch_one(query, (id,))

            if result:
                # 解析JSON字段
                if result.get("tags") and isinstance(result["tags"], str):
                    result["tags"] = json.loads(result["tags"])
                if result.get("parameters") and isinstance(result["parameters"], str):
                    result["parameters"] = json.loads(result["parameters"])
                if result.get("request_body") and isinstance(result["request_body"], str):
                    result["request_body"] = json.loads(result["request_body"])
                if result.get("responses") and isinstance(result["responses"], str):
                    result["responses"] = json.loads(result["responses"])
                return result
            return {}

        except Exception as e:
            self.logger.error(f"获取API记录失败: {str(e)}")
            return {}

    async def get_doc_mappings_by_kb_uid(self, knowledge_base_uid: str) -> list:
        """
        根据知识库uid查询文档，返回去重后的文档名称列表

        Args:
            knowledge_base_uid: 知识库UID

        Returns:
            list: 文档名称列表
        """
        try:
            sql = f"""
                SELECT DISTINCT document_name
                FROM {self.table}
                WHERE knowledge_base_uid = %s
                ORDER BY document_name
            """
            result = await self.db_pool.fetch_all(sql, (knowledge_base_uid,))

            if result:
                doc_list = [row["document_name"] for row in result]
                self.logger.info(f"成功查询知识库 {knowledge_base_uid} 的API文档列表: {doc_list}")
                return doc_list

            self.logger.warning(f"知识库 {knowledge_base_uid} 下没有找到任何API文档")
            return []

        except Exception as e:
            self.logger.error(f"查询知识库API文档列表失败: {str(e)}")
            raise

    async def get_api_mappings_by_entity_name(self, entity_name: str) -> List[Dict[str, Any]]:
        """
        根据实体名称获取API映射记录列表

        Args:
            entity_name: 实体名称

        Returns:
            List[Dict[str, Any]]: API映射记录列表
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE entity_name = %s
            """
            results = await self.db_pool.fetch_all(sql, (entity_name,))

            # 解析所有记录的JSON字段
            for result in results:
                if result.get("tags") and isinstance(result["tags"], str):
                    result["tags"] = json.loads(result["tags"])
                if result.get("parameters") and isinstance(result["parameters"], str):
                    result["parameters"] = json.loads(result["parameters"])
                if result.get("request_body") and isinstance(result["request_body"], str):
                    result["request_body"] = json.loads(result["request_body"])
                if result.get("responses") and isinstance(result["responses"], str):
                    result["responses"] = json.loads(result["responses"])

            return results

        except Exception as e:
            self.logger.error(f"获取API映射记录列表失败: {str(e)}")
            raise

    async def delete_api_mapping(self, id: int) -> bool:
        """
        删除API映射记录

        Args:
            id: 记录ID

        Returns:
            bool: 是否删除成功
        """
        try:
            sql = f"""
                DELETE FROM {self.table}
                WHERE id = %s
            """
            affected_rows = await self.db_pool.execute(sql, (id,))
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"删除API映射记录失败: {str(e)}")
            raise

    async def update_status(self, id: int, status: str) -> bool:
        """
        更新API记录状态

        Args:
            id: 记录ID
            status: 新状态

        Returns:
            bool: 是否更新成功
        """
        try:
            sql = f"""
                UPDATE {self.table}
                SET status = %s
                WHERE id = %s
            """
            affected_rows = await self.db_pool.execute(sql, (status, id))
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"更新API记录状态失败: {str(e)}")
            raise

    async def get_api_mappings_by_status(self, status: str) -> List[Dict[str, Any]]:
        """
        根据状态获取API映射记录列表

        Args:
            status: 状态

        Returns:
            List[Dict[str, Any]]: API映射记录列表
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE status = %s
            """
            results = await self.db_pool.fetch_all(sql, (status,))

            # 解析所有记录的JSON字段
            for result in results:
                if result.get("tags") and isinstance(result["tags"], str):
                    result["tags"] = json.loads(result["tags"])
                if result.get("parameters") and isinstance(result["parameters"], str):
                    result["parameters"] = json.loads(result["parameters"])
                if result.get("request_body") and isinstance(result["request_body"], str):
                    result["request_body"] = json.loads(result["request_body"])
                if result.get("responses") and isinstance(result["responses"], str):
                    result["responses"] = json.loads(result["responses"])

            return results

        except Exception as e:
            self.logger.error(f"获取API映射记录列表失败: {str(e)}")
            raise

    async def get_api_mappings_by_kb_uid_and_doc_name(
            self,
            knowledge_base_uid: str,
            document_name: str
    ) -> List[Dict[str, Any]]:
        """
        根据知识库UID和文档名称查询该文档下的所有API记录

        Args:
            knowledge_base_uid: 知识库UID
            document_name: 文档名称

        Returns:
            List[Dict[str, Any]]: 该文档下的所有API记录
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE knowledge_base_uid = %s AND document_name = %s
                ORDER BY id
            """
            results = await self.db_pool.fetch_all(sql, (knowledge_base_uid, document_name))

            # 解析所有记录的JSON字段
            for result in results:
                if result.get("tags") and isinstance(result["tags"], str):
                    result["tags"] = json.loads(result["tags"])
                if result.get("parameters") and isinstance(result["parameters"], str):
                    result["parameters"] = json.loads(result["parameters"])
                if result.get("request_body") and isinstance(result["request_body"], str):
                    result["request_body"] = json.loads(result["request_body"])
                if result.get("responses") and isinstance(result["responses"], str):
                    result["responses"] = json.loads(result["responses"])

            return results
        except Exception as e:
            self.logger.error(f"根据知识库ID和文档名查询API记录失败: {str(e)}")
            raise

    async def get_api_mappings_by_kb_uid_all(
            self,
            knowledge_base_uid: str
    ) -> List[Dict[str, Any]]:
        """
        根据知识库UID查询该知识库下的所有API记录（不依赖文档名称）

        Args:
            knowledge_base_uid: 知识库UID

        Returns:
            List[Dict[str, Any]]: 该知识库下的所有API记录
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE knowledge_base_uid = %s
                ORDER BY document_name, id
            """
            results = await self.db_pool.fetch_all(sql, (knowledge_base_uid,))

            # 解析所有记录的JSON字段
            for result in results:
                if result.get("tags") and isinstance(result["tags"], str):
                    result["tags"] = json.loads(result["tags"])
                if result.get("parameters") and isinstance(result["parameters"], str):
                    result["parameters"] = json.loads(result["parameters"])
                if result.get("request_body") and isinstance(result["request_body"], str):
                    result["request_body"] = json.loads(result["request_body"])
                if result.get("responses") and isinstance(result["responses"], str):
                    result["responses"] = json.loads(result["responses"])

            return results
        except Exception as e:
            self.logger.error(f"根据知识库ID查询API记录失败: {str(e)}")
            raise

    async def get_total_entity_count(self) -> int:
        """
        查询 map_doc_api 表中的总实体数量

        Returns:
            int: 实体总数
        """
        try:
            sql = f"""
                SELECT COUNT(*) AS total FROM {self.table}
            """
            result = await self.db_pool.fetch_one(sql)
            return result.get("total", 0)

        except Exception as e:
            self.logger.error(f"查询 map_doc_api 表总实体数量失败: {str(e)}")
            raise

    async def get_pending_entities_count_by_kb_uid_and_doc_name(
            self,
            knowledge_base_uid: str,
            document_name: str
    ) -> int:
        """
        根据知识库UID和文档名称查询该文档下 status 为 '未嵌入' 的实体数量

        Args:
            knowledge_base_uid: 知识库UID
            document_name: 文档名称

        Returns:
            int: 待嵌入实体数量
        """
        try:
            sql = f"""
                SELECT COUNT(*) AS count 
                FROM {self.table}
                WHERE knowledge_base_uid = %s 
                  AND document_name = %s 
                  AND status = %s
            """
            result = await self.db_pool.fetch_one(sql, (knowledge_base_uid, document_name, "未嵌入"))

            if result and result.get("count", 0) >= 0:
                self.logger.info(
                    f"知识库 {knowledge_base_uid} 下文档 {document_name} 中待嵌入的API实体数为: {result['count']}")
                return result["count"]

            return 0

        except Exception as e:
            self.logger.error(f"查询待嵌入API实体数量失败: {str(e)}")
            raise

    async def get_api_record_by_name(self, entity_name: str) -> Dict[str, Any]:
        """
        根据 entity_name 获取API记录

        Args:
            entity_name (str): API实体名称

        Returns:
            Dict[str, Any]: API记录，未找到时返回空字典
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE entity_name = %s
            """
            result = await self.db_pool.fetch_one(sql, (entity_name,))

            if result:
                # 解析JSON字段
                if result.get("tags") and isinstance(result["tags"], str):
                    result["tags"] = json.loads(result["tags"])
                if result.get("parameters") and isinstance(result["parameters"], str):
                    result["parameters"] = json.loads(result["parameters"])
                if result.get("request_body") and isinstance(result["request_body"], str):
                    result["request_body"] = json.loads(result["request_body"])
                if result.get("responses") and isinstance(result["responses"], str):
                    result["responses"] = json.loads(result["responses"])
                return result
            return {}

        except Exception as e:
            self.logger.error(f"根据 entity_name 获取API记录失败: {str(e)}")
            raise

    # 修改 MapDocApiModel 方法：直接返回 count
    async def get_total_entity_count_by_kb_uid(self, knowledge_base_uid: str) -> int:
        try:
            sql = f"""
                SELECT COUNT(*) AS total 
                FROM {self.table}
                WHERE knowledge_base_uid = %s
            """
            result = await self.db_pool.fetch_one(sql, (knowledge_base_uid,))
            return result.get("total", 0)
        except Exception as e:
            self.logger.error(f"查询总API实体数量失败: {str(e)}")
            raise

    async def get_pending_entities_count_by_kb_uid(self, knowledge_base_uid: str) -> int:
        try:
            sql = f"""
                SELECT COUNT(*) AS count 
                FROM {self.table}
                WHERE knowledge_base_uid = %s AND status = '未嵌入'
            """
            result = await self.db_pool.fetch_one(sql, (knowledge_base_uid,))
            return result.get("count", 0)
        except Exception as e:
            self.logger.error(f"查询待嵌入API实体数量失败: {str(e)}")
            raise

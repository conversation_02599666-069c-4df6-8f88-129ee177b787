import os

# 项目根目录
ROOT_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 日志配置
LOGS_PATH = os.path.join(ROOT_PATH, 'logs')

# DIFY配置
DIFY_HOST = "*************"

# DIFY管理员
DIFY_ADMINISTRATOR = {
    "email": "<EMAIL>",
    "password": "ngfw123!@#",
}

# 文件上传配置
UPLOAD_CONFIG = {
    # 单次上传文件大小限制（字节）
    "MAX_FILE_SIZE": 100 * 1024 * 1024,  # 100MB
    # 分块上传文件大小限制（字节）
    "MAX_CHUNKED_FILE_SIZE": 500 * 1024 * 1024,  # 500MB
    # 分块大小（字节）
    "CHUNK_SIZE": 1024 * 1024,  # 1MB
    # 临时文件目录
    "TEMP_DIR": "temp_uploads",
    # 允许的文件类型
    "ALLOWED_EXTENSIONS": [".json"],
    # 请求超时时间（秒）
    "REQUEST_TIMEOUT": 300,  # 5分钟
    # 数据库批处理大小
    "BATCH_SIZE": 50,
}
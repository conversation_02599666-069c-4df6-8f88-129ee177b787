#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取API接口文档中的summary字段
"""

import json


def extract_summaries():
    """提取API summary字段"""
    
    print("=== 提取API Summary字段 ===")
    
    # 读取JSON文件
    try:
        with open("api_docs.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
        print("✓ 成功读取api_docs.json")
    except Exception as e:
        print(f"✗ 读取文件失败: {e}")
        return
    
    # 提取API列表
    if "apis" in data:
        apis = data["apis"]
    else:
        print("✗ 未找到apis字段")
        return
    
    print(f"✓ 找到 {len(apis)} 个API")
    
    # 提取summary
    summaries = []
    for i, api in enumerate(apis, 1):
        method = api.get('method', 'N/A')
        path = api.get('path', 'N/A')
        summary = api.get('summary', '').strip()
        
        if summary:
            summaries.append({
                'index': i,
                'method': method,
                'path': path,
                'summary': summary
            })
    
    print(f"✓ 提取到 {len(summaries)} 个有效summary")
    
    # 保存到文件
    output_file = "api_summaries.txt"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# API接口Summary字段提取结果\n")
        f.write(f"# 总数量: {len(summaries)} 个\n\n")
        
        # 完整列表
        f.write("## 完整API列表\n\n")
        for item in summaries:
            f.write(f"{item['index']:3d}. [{item['method']}] {item['summary']}\n")
            f.write(f"     路径: {item['path']}\n\n")
        
        # 纯文本列表
        f.write("\n## 纯Summary文本列表\n\n")
        for item in summaries:
            f.write(f"{item['summary']}\n")
    
    print(f"✓ 结果已保存到: {output_file}")
    
    # 显示前几个
    print("\n前10个Summary:")
    for item in summaries[:10]:
        print(f"  {item['index']}. [{item['method']}] {item['summary']}")
    
    if len(summaries) > 10:
        print(f"  ... 还有 {len(summaries) - 10} 个")
    
    # 统计
    methods = {}
    keywords = ['创建', '查询', '更新', '删除', '列表', '获取']
    keyword_stats = {}
    
    for item in summaries:
        # 统计方法
        method = item['method']
        methods[method] = methods.get(method, 0) + 1
        
        # 统计关键词
        summary = item['summary']
        for keyword in keywords:
            if keyword in summary:
                keyword_stats[keyword] = keyword_stats.get(keyword, 0) + 1
    
    print("\nHTTP方法统计:")
    for method, count in sorted(methods.items()):
        print(f"  {method}: {count} 个")
    
    print("\n关键词统计:")
    for keyword, count in sorted(keyword_stats.items(), key=lambda x: x[1], reverse=True):
        print(f"  {keyword}: {count} 个")


if __name__ == "__main__":
    extract_summaries()

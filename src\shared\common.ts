export const cancelTime = 500;
export const MSG_ERR_TIME = 5000;

export const messageTimeSuccess = 3;
export const messageTimeError = 5;
export const messageTimeWarning = 5;
export const messageTimeInfo = 3;
export const messageTimeLoading = 3;

export const maxHeight = 400;
export const minHeight = 100;
export const SLICE_SIZE = 10 * 1024 * 1024; //10M

export const ERR_MESSAGE = "当前输入内容有误，请确保配置正确再提交";

export const EXECUTION_RESULT = [
  { key: "blocked", label: "阻塞", color: "#f59f00" },
  { key: "fail", label: "失败", color: "#f52222" },
  { key: "pass", label: "通过", color: "#68b92e" },
  { key: "", label: "未执行", color: "#7f8fa4" },
  { key: "unexecuted", label: "自定义——未执行", color: "#294743" },
  { key: "None", label: "--", color: "#2962ff" },
];

export const CASE_TYPE_LIST = {
  all: "总用例",
  feature: "功能测试",
  config: "配置相关",
  performance: "性能测试",
  install: "安装部署",
  security: "安全相关",
  interface: "接口测试",
  coupling: "耦合测试",
  reliability: "可靠性测试",
  other: "其他",
};

export const BOARD_TYPE_LIST = {
  default: "默认方式",
  pri: "bug优先级",
  severity: "bug严重程度",
  module: "bug模块",
  type: "bug类型",
  user: "指派人员",
};
export const SUB_STATUS_LIST = {
  1: "激活",
  102: "测试经理审核",
  103: "研发经理审核",
  104: "问题定位修改",
  105: "修改实施审核",
  106: "问题待决策",
  6: "已解决待回归",
  601: "回归测试",
  602: "问题关闭确认",
  10: "问题解决关闭",
  1002: "非问题关闭",
  1003: "重复问题关闭",
  1004: "决策不解决关闭",
  1005: "转需求关闭",
  "-1": "空",
};

export const SEVERITY_LIST = {
  0: "无",
  1: "致命",
  2: "严重",
  3: "一般",
  4: "建议",
};
export const PRI_LIST = {
  0: "无",
  1: "紧急",
  2: "优先",
  3: "正常",
  4: "暂缓",
};
export const CASE_PRI_LIST = {
  0: "P0",
  1: "P1",
  2: "P2",
  3: "P3",
  4: "P4",
};

export const CONFIG_TYPE_LIST = {
  bug_module: "BUG目录",
  case_testtask: "用例测试单",
  build: "BUG版本",
};

export const RULE_LIST = [
  { value: "strict_merge", label: "严格归并" },
  { value: "latest_merge", label: "最新归并" },
  { value: "non_merge", label: "不归并" },
]; 
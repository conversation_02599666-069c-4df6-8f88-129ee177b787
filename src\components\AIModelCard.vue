<template>
  <div class="card-wrapper" :class="{ 'card-hover': isHovered }" @mouseenter="isHovered = true" @mouseleave="isHovered = false">
    <div class="card-content">
      <div class="card-header">
        <div class="card-icon">
          <component :is="getCardIcon(modelData.type)" class="icon" />
        </div>
        <div class="card-info">
          <h3 class="card-title text-left ml-1">{{ modelData.name }}</h3>
          <div class="card-tags">
            <span v-for="(tag, tagIndex) in modelData.tags" :key="tagIndex" class="card-tag text-left" :style="{ backgroundColor: getTagColor(tagIndex) }">
              {{ tag }}
            </span>
          </div>
        </div>
      </div>
      <p class="card-description text-left">{{ modelData.description }}</p>
      <div class="card-meta">
        <div class="meta-item">
          <UserOutlined class="meta-icon" />
          <span class="meta-label">创建者：</span>
          <span class="meta-value">{{ modelData.creator }}</span>
        </div>
        <div class="meta-item">
          <TeamOutlined class="meta-icon" />
          <span class="meta-label">组别：</span>
          <span class="meta-value">{{ modelData.group }}</span>
        </div>
        <div class="meta-item">
          <ClockCircleOutlined class="meta-icon" />
          <span class="meta-label">创建时间：</span>
          <span class="meta-value">{{ modelData.createTime }}</span>
        </div>
      </div>
      <div class="card-actions">
        <a-button class="card-button card-button-secondary" @click="$emit('apiClick', modelData)">
          <template #icon><api-outlined /></template>
          API 参考
        </a-button>
        <a-button type="primary" class="card-button card-button-primary" @click="$emit('detailsClick', modelData)">
          <template #icon><eye-outlined /></template>
          查看详情
        </a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import {
  ApiOutlined,
  EyeOutlined,
  UserOutlined,
  TeamOutlined,
  ClockCircleOutlined,
  RobotOutlined,
  BarChartOutlined,
  GlobalOutlined
} from '@ant-design/icons-vue';

interface ModelData {
  name: string;
  tags: string[];
  description: string;
  creator: string;
  group: string;
  createTime: string;
  url: string;
  type: string;
}

defineProps<{
  modelData: ModelData
}>();

defineEmits(['apiClick', 'detailsClick']);

const isHovered = ref(false);

const tagColors = [
  '#E9F5FE',
  '#F0F9EB',
  '#FEF6E9',
  '#F5EEFF',
  '#FFF1F0',
  '#F0F0F0'
];

const getTagColor = (index: number): string => {
  return tagColors[index % tagColors.length];
};

const getCardIcon = (type: string) => {
  const iconMap = {
    ai: RobotOutlined,
    data: BarChartOutlined,
    iot: GlobalOutlined
  };
  return iconMap[type as keyof typeof iconMap] || RobotOutlined;
};
</script>

<style scoped>
.card-wrapper {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  width: 360px;
}

.card-hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.card-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.card-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.card-icon {
  width: 32px;
  height: 32px;
  background: #f5f5f5;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-icon .icon {
  font-size: 18px;
  color: #1890ff;
}

.card-info {
  flex: 1;
  min-width: 0;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px;
  line-height: 1.4;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.card-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: #595959;
}

.card-description {
  color: #595959;
  font-size: 13px;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  background-color: #f9fafb;
  border-radius: 6px;
  padding: 8px 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.meta-icon {
  color: #8c8c8c;
  font-size: 14px;
}

.meta-label {
  color: #8c8c8c;
}

.meta-value {
  color: #262626;
  font-weight: 500;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-button {
  flex: 1;
  height: 30px;
  font-size: 13px;
  padding: 0 12px;
}

.card-button-secondary {
  background-color: #f5f5f5;
  color: #595959;
  border-color: #d9d9d9;
}

.card-button-secondary:hover {
  background-color: #ebebeb;
  color: #262626;
}

.card-button-primary:hover {
  opacity: 0.9;
}

@media (max-width: 768px) {
  .card-meta {
    flex-direction: column;
  }
  
  .card-actions {
    flex-direction: column;
  }
}
</style>
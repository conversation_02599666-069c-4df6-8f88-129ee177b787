package com.qfnu.service;

import com.qfnu.model.dto.ADTokenResponseDTO;
import com.qfnu.model.dto.ADUserInfoDTO;
import com.qfnu.model.dto.AuthUserDto;

public interface ADAuthService {

    /**
     * 使用授权码换取OAuth用户信息
     *
     * @param code 授权码
     * @param clientId 客户端ID
     * @param clientSecret 客户端密钥
     * @param redirectUri 重定向URI
     * @return OAuth用户信息
     */
    ADTokenResponseDTO exchangeTokenWithCode(String code, String clientId, String clientSecret, String redirectUri);

    /**
     * 使用授权码换取OAuth用户信息（兼容性方法）
     *
     * @param code 授权码
     * @param clientId 客户端ID
     * @param clientSecret 客户端密钥
     * @return OAuth用户信息
     */
    default ADTokenResponseDTO exchangeTokenWithCode(String code, String clientId, String clientSecret) {
        return exchangeTokenWithCode(code, clientId, clientSecret, null);
    }

    /**
     * 根据AD用户信息匹配或创建本地用户
     *
     * @param adUserInfo AD用户信息
     * @return 本地用户信息
     */
    AuthUserDto matchOrCreateUser(ADUserInfoDTO adUserInfo);

    /**
     * 根据邮箱匹配用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    AuthUserDto findUserByEmail(String email);

    /**
     * 根据工号匹配用户
     *
     * @param jobNo 工号
     * @return 用户信息
     */
    AuthUserDto findUserByJobNo(String jobNo);

    /**
     * 创建新的AD用户
     *
     * @param adUserInfo AD用户信息
     * @return 创建的用户信息
     */
    AuthUserDto createADUser(ADUserInfoDTO adUserInfo);

    /**
     * 更新用户最后登录时间和信息
     *
     * @param userId 用户ID
     * @param adUserInfo AD用户信息
     */
    void updateUserLoginInfo(String userId, ADUserInfoDTO adUserInfo);
}

from typing import Dict, List, Any, Optional
from decimal import Decimal

from db.mysql.mysql_pool import MySQLPool
from logger import logging
import json


class ApiDataModelRelationsModel:
    """API数据模型关系模型类 - 处理 api_data_model_relations 表"""

    def __init__(self):
        self.table = "api_data_model_relations"
        self.primary_key = "id"
        self.logger = logging()
        self.db_pool = MySQLPool()

    async def initialize(self):
        """初始化连接池"""
        await self.db_pool.initialize()

    async def close(self):
        """关闭连接池，释放资源"""
        await self.db_pool.close()

    async def create_relation(
            self,
            knowledge_base_id: str,
            upstream_api: Optional[str] = None,
            data_model: Optional[Dict] = None,
            downstream_api: Optional[str] = None,
            relation_description: Optional[str] = None,
            status: int = 0,
            task_id: Optional[str] = None,
            risk_level: Optional[str] = None,
            semantic_score: Optional[float] = None,
            verb_score: Optional[float] = None,
            final_score: Optional[float] = None
    ):
        """
        创建API数据模型关系记录

        Args:
            knowledge_base_id: 知识库ID
            upstream_api: 上游API（JSON格式字符串）
            data_model: 数据模型（Dict类型，会转换为JSON）
            downstream_api: 下游API（JSON格式字符串）
            relation_description: 关系描述，说明关系产生的原因
            status: 关系状态，默认0（待确认）
            task_id: 任务ID（可选）
            risk_level: 风险等级（低/中/高）
            semantic_score: 语义相似度分数
            verb_score: 动词相似度分数
            final_score: 综合相似度分数
        """
        try:
            sql = f"""
                INSERT INTO {self.table} (
                    knowledge_base_id, upstream_api, data_model, downstream_api, relation_description,
                    status, task_id, risk_level, semantic_score, verb_score, final_score
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                knowledge_base_id,
                json.dumps(upstream_api) if upstream_api else None,
                json.dumps(data_model) if data_model else None,
                json.dumps(downstream_api) if downstream_api else None,
                relation_description,
                status,
                task_id,
                risk_level,
                semantic_score,
                verb_score,
                final_score
            )

            return await self.db_pool.execute(sql, params)

        except Exception as e:
            self.logger.error(f"创建API数据模型关系记录失败: {str(e)}")
            raise

    async def get_relations_by_knowledge_base(self, knowledge_base_id: str) -> List[Dict[str, Any]]:
        """
        根据知识库ID查询所有关系记录
        
        Args:
            knowledge_base_id: 知识库ID
            
        Returns:
            List[Dict]: 查询结果列表（包含解析后的JSON字段）
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE knowledge_base_id = %s
                ORDER BY id DESC
            """
            results = await self.db_pool.fetch_all(sql, (knowledge_base_id,))

            # 解析JSON字段并转换Decimal类型
            processed_results = []
            for result in results:
                # 转换为字典并处理Decimal类型
                result_dict = dict(result)

                # 转换Decimal类型为float
                for key, value in result_dict.items():
                    if isinstance(value, Decimal):
                        result_dict[key] = float(value)

                # 解析JSON字段
                if result_dict.get("upstream_api") and isinstance(result_dict["upstream_api"], str):
                    try:
                        result_dict["upstream_api"] = json.loads(result_dict["upstream_api"])
                    except json.JSONDecodeError:
                        result_dict["upstream_api"] = None

                if result_dict.get("data_model") and isinstance(result_dict["data_model"], str):
                    try:
                        result_dict["data_model"] = json.loads(result_dict["data_model"])
                    except json.JSONDecodeError:
                        result_dict["data_model"] = None

                if result_dict.get("downstream_api") and isinstance(result_dict["downstream_api"], str):
                    try:
                        result_dict["downstream_api"] = json.loads(result_dict["downstream_api"])
                    except json.JSONDecodeError:
                        result_dict["downstream_api"] = None

                processed_results.append(result_dict)

            return processed_results

        except Exception as e:
            self.logger.error(f"查询API数据模型关系记录失败: {str(e)}")
            raise

    async def get_relations_with_pagination(
        self,
        knowledge_base_id: str,
        page: int = 1,
        page_size: int = 10,
        status_filter: List[str] = None,
        task_id: str = None
    ) -> Dict[str, Any]:
        """
        分页查询API数据模型关系记录

        Args:
            knowledge_base_id: 知识库ID
            page: 页码
            page_size: 每页数量
            status_filter: 状态筛选列表
            task_id: 任务ID筛选，只查询指定任务的数据

        Returns:
            Dict: 包含list、total、page、page_size等信息的字典
        """
        try:
            # 构建查询条件
            where_conditions = ["knowledge_base_id = %s"]
            params = [knowledge_base_id]

            # 状态筛选
            if status_filter:
                status_placeholders = ','.join(['%s'] * len(status_filter))
                where_conditions.append(f"status IN ({status_placeholders})")
                params.extend(status_filter)

            # 任务ID筛选
            if task_id:
                where_conditions.append("task_id = %s")
                params.append(task_id)

            where_clause = " AND ".join(where_conditions)

            # 查询总数
            count_sql = f"""
                SELECT COUNT(*) as total
                FROM {self.table}
                WHERE {where_clause}
            """

            count_result = await self.db_pool.fetch_all(count_sql, params)
            total = count_result[0]['total'] if count_result else 0

            # 计算分页
            offset = (page - 1) * page_size
            total_pages = (total + page_size - 1) // page_size

            # 查询数据
            data_sql = f"""
                SELECT id, upstream_api, data_model, downstream_api, relation_description,
                       status, task_id, risk_level, semantic_score, verb_score, final_score,
                       created_at, updated_at
                FROM {self.table}
                WHERE {where_clause}
                ORDER BY updated_at DESC, id DESC
                LIMIT %s OFFSET %s
            """

            data_params = params + [page_size, offset]
            relations = await self.db_pool.fetch_all(data_sql, data_params)

            # 处理返回数据
            result_list = []
            for relation in relations:
                try:
                    # 解析JSON字段
                    upstream_api = json.loads(relation['upstream_api']) if relation['upstream_api'] else None
                    data_model = json.loads(relation['data_model']) if relation['data_model'] else None
                    downstream_api = json.loads(relation['downstream_api']) if relation['downstream_api'] else None

                    # 转换Decimal类型为float
                    semantic_score = relation.get('semantic_score')
                    verb_score = relation.get('verb_score')
                    final_score = relation.get('final_score')

                    if isinstance(semantic_score, Decimal):
                        semantic_score = float(semantic_score)
                    if isinstance(verb_score, Decimal):
                        verb_score = float(verb_score)
                    if isinstance(final_score, Decimal):
                        final_score = float(final_score)

                    result_list.append({
                        "id": relation['id'],
                        "upstream_api": upstream_api,
                        "data_model": data_model,
                        "downstream_api": downstream_api,
                        "relation_description": relation['relation_description'],
                        "status": relation['status'],
                        "task_id": relation['task_id'],
                        "risk_level": relation.get('risk_level'),
                        "semantic_score": semantic_score,
                        "verb_score": verb_score,
                        "final_score": final_score,
                        "created_at": relation['created_at'].isoformat() if relation['created_at'] else None,
                        "updated_at": relation['updated_at'].isoformat() if relation['updated_at'] else None
                    })
                except Exception as e:
                    self.logger.error(f"解析关系数据失败: {str(e)}")
                    continue

            return {
                "list": result_list,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages
            }

        except Exception as e:
            self.logger.error(f"分页查询API数据模型关系记录失败: {str(e)}")
            raise

    async def update_status(self, relation_id: int, status: int) -> bool:
        """
        更新关系状态
        
        Args:
            relation_id: 关系记录ID
            status: 新的状态值
            
        Returns:
            bool: 是否更新成功
        """
        try:
            sql = f"""
                UPDATE {self.table}
                SET status = %s
                WHERE id = %s
            """
            affected_rows = await self.db_pool.execute(sql, (status, relation_id))
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"更新关系状态失败: {str(e)}")
            raise

    async def delete_relation(self, relation_id: int) -> bool:
        """
        删除关系记录
        
        Args:
            relation_id: 关系记录ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            sql = f"""
                DELETE FROM {self.table}
                WHERE id = %s
            """
            affected_rows = await self.db_pool.execute(sql, (relation_id,))
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"删除关系记录失败: {str(e)}")
            raise

    async def get_relation_by_id(self, relation_id: int) -> Optional[Dict[str, Any]]:
        """
        根据ID查询单个关系记录
        
        Args:
            relation_id: 关系记录ID
            
        Returns:
            Optional[Dict]: 查询结果，如果没有找到返回None
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE id = %s
            """
            result = await self.db_pool.fetch_one(sql, (relation_id,))

            if result:
                # 解析JSON字段
                if result.get("upstream_api") and isinstance(result["upstream_api"], str):
                    try:
                        result["upstream_api"] = json.loads(result["upstream_api"])
                    except json.JSONDecodeError:
                        result["upstream_api"] = None

                if result.get("data_model") and isinstance(result["data_model"], str):
                    try:
                        result["data_model"] = json.loads(result["data_model"])
                    except json.JSONDecodeError:
                        result["data_model"] = None

                if result.get("downstream_api") and isinstance(result["downstream_api"], str):
                    try:
                        result["downstream_api"] = json.loads(result["downstream_api"])
                    except json.JSONDecodeError:
                        result["downstream_api"] = None

            return result

        except Exception as e:
            self.logger.error(f"查询关系记录失败: {str(e)}")
            raise

    async def count_relations_by_status(self, knowledge_base_id: str, status: int) -> int:
        """
        统计指定状态的关系数量
        
        Args:
            knowledge_base_id: 知识库ID
            status: 状态值
            
        Returns:
            int: 关系数量
        """
        try:
            sql = f"""
                SELECT COUNT(*) AS count 
                FROM {self.table}
                WHERE knowledge_base_id = %s AND status = %s
            """
            result = await self.db_pool.fetch_one(sql, (knowledge_base_id, status))
            return result.get("count", 0)

        except Exception as e:
            self.logger.error(f"统计关系数量失败: {str(e)}")
            raise

    async def create_prerequisite_relation(
            self,
            knowledge_base_id: str,
            upstream_api: Dict,
            downstream_api: Dict,
            prerequisite: str,
            semantic_score: float,
            verb_score: float,
            final_score: float,
            risk_level: str,
            task_id: Optional[str] = None
    ) -> int:
        """
        创建前置依赖关系记录（专门用于rerank匹配结果）

        Args:
            knowledge_base_id: 知识库ID
            upstream_api: 上游API信息（Dict类型）
            downstream_api: 下游API信息（Dict类型）
            prerequisite: 前置依赖描述
            semantic_score: 语义相似度分数
            verb_score: 动词相似度分数
            final_score: 综合相似度分数
            risk_level: 风险等级（低/中/高）
            task_id: 任务ID（可选）

        Returns:
            int: 创建的关系记录ID
        """
        try:
            # 构建关系描述
            relation_description = f"前置依赖匹配: {prerequisite} -> {downstream_api.get('summary', '')}"

            # 调用原有的create_relation方法
            relation_id = await self.create_relation(
                knowledge_base_id=knowledge_base_id,
                upstream_api=upstream_api,
                data_model=None,  # 前置依赖关系不需要数据模型
                downstream_api=downstream_api,
                relation_description=relation_description,
                status=0,  # 默认待确认状态
                task_id=task_id,
                risk_level=risk_level,
                semantic_score=semantic_score,
                verb_score=verb_score,
                final_score=final_score
            )

            self.logger.info(f"创建前置依赖关系成功: {upstream_api.get('summary', '')} -> {downstream_api.get('summary', '')} "
                           f"(风险级别: {risk_level}, 综合分数: {final_score:.4f})")

            return relation_id

        except Exception as e:
            self.logger.error(f"创建前置依赖关系失败: {str(e)}")
            raise

    async def batch_create_prerequisite_relations(
            self,
            knowledge_base_id: str,
            relations: List[Dict],
            task_id: Optional[str] = None
    ) -> Dict[str, int]:
        """
        批量创建前置依赖关系

        Args:
            knowledge_base_id: 知识库ID
            relations: 关系列表，每个元素包含upstream_api, downstream_api, relation_info
            task_id: 任务ID（可选）

        Returns:
            Dict[str, int]: 创建结果统计 {"success": 成功数量, "failed": 失败数量}
        """
        if not relations:
            return {"success": 0, "failed": 0}

        success_count = 0
        failed_count = 0

        self.logger.info(f"开始批量创建 {len(relations)} 个前置依赖关系")

        for i, relation in enumerate(relations, 1):
            try:
                upstream_api = relation.get("upstream_api")
                downstream_api = relation.get("downstream_api")
                data_model = relation.get("data_model")  # 新增数据模型
                relation_info = relation.get("relation_info")

                if not all([upstream_api, downstream_api, relation_info]):
                    self.logger.warning(f"第 {i} 个关系数据不完整，跳过")
                    failed_count += 1
                    continue

                # 使用原有的create_relation方法，支持数据模型
                relation_id = await self.create_relation(
                    knowledge_base_id=knowledge_base_id,
                    upstream_api=upstream_api,
                    data_model=data_model,  # 传入数据模型
                    downstream_api=downstream_api,
                    relation_description=f"前置依赖匹配: {relation_info.get('prerequisite', '')} -> {downstream_api.get('summary', '')}",
                    status=0,  # 默认待确认状态
                    task_id=task_id,
                    risk_level=relation_info.get("risk_level", "高"),
                    semantic_score=relation_info.get("semantic_score", 0.0),
                    verb_score=relation_info.get("verb_score", 0.0),
                    final_score=relation_info.get("final_score", 0.0)
                )

                success_count += 1

            except Exception as e:
                self.logger.error(f"创建第 {i} 个关系失败: {str(e)}")
                failed_count += 1

        self.logger.info(f"批量创建完成: 成功 {success_count} 个, 失败 {failed_count} 个")

        return {"success": success_count, "failed": failed_count}

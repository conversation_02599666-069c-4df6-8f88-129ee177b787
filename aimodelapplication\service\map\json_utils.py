from typing import Any, List, Dict
import json
from logger import logging
logger = logging()

def extract_json_keys(data):
    """
    提取 JSON 对象中所有层级的键名（支持字符串、嵌套结构）
    """
    keys = []

    # 如果是字符串，尝试解析为 JSON
    if isinstance(data, str):
        try:
            data = json.loads(data)
        except json.JSONDecodeError:
            return []

    if isinstance(data, dict):
        for key, value in data.items():
            keys.append(key)
            keys.extend(extract_json_keys(value))
    elif isinstance(data, list):
        for item in data:
            keys.extend(extract_json_keys(item))

    return keys

def extract_example_keys_from_responses(api_responses):
    """
    从 api_responses 中提取接口响应结构用于测试用例生成
    改进版本：支持输入为字典或JSON字符串
    """
    # 检查输入类型，如果是字符串则尝试解析为字典
    if isinstance(api_responses, str):
        try:
            api_responses = json.loads(api_responses)
        except json.JSONDecodeError:
            logger.error("无法解析 api_responses 字符串为 JSON")
            return []

    # 确保输入是字典类型
    if not isinstance(api_responses, dict):
        logger.error(f"api_responses 应该是字典，实际是 {type(api_responses)}")
        return []

    # 提取所有状态码的响应数据
    status_codes = ["200", "default"] + [code for code in api_responses if code not in ["200", "default"]]

    # 收集所有示例字段
    example_fields = []

    for status_code in status_codes:
        status_data = api_responses.get(status_code, {})
        if not status_data:
            continue

        # 获取 content 数据
        content = status_data.get("content", {})
        if not isinstance(content, dict):
            continue

        # 获取 application/json 数据
        app_json = content.get("application/json", {})
        if not isinstance(app_json, dict):
            continue

        # 获取 example 数据
        example_data = app_json.get("example")
        if not example_data:
            continue

        # 处理 example 数据
        if isinstance(example_data, str):
            try:
                example_data = json.loads(example_data)
            except json.JSONDecodeError:
                logger.warning(f"状态码 {status_code} 的 example 无法解析为 JSON")
                continue

        if isinstance(example_data, dict):
            example_fields.extend(extract_json_keys(example_data))

    return list(set(example_fields))  # 去重后返回


def extract_example_keys(status_data):
    """提取特定状态码的示例键值"""
    example_data = status_data.get("content", {}) \
        .get("application/json", {}) \
        .get("example", "")

    if not example_data:
        return []

    try:
        # 尝试解析 example 字符串为 JSON
        example_json = json.loads(example_data) if isinstance(example_data, str) else example_data
        if isinstance(example_json, dict):
            return list(example_json.keys())
    except json.JSONDecodeError:
        pass

    return []
def safe_json_load(json_str):
    """安全解析JSON字符串（公共工具方法）

    Args:
        json_str: 要解析的JSON字符串或对象

    Returns:
        dict/list: 解析后的对象，失败时返回空字典或空列表
    """
    if isinstance(json_str, (dict, list)):
        return json_str
    try:
        return json.loads(json_str) if json_str else {}
    except json.JSONDecodeError:
        return {} if json_str and json_str.startswith('{') else []
def find_matching_components(field_value: str, components: list) -> list:
    """
    查找匹配指定字段值的组件（仅匹配enum_values和properties）
    """
    matched = []
    for comp in components:
        if not isinstance(comp, dict):
            continue

        # 检查enum_values匹配
        enum_values = safe_json_load(comp.get("enum_values", "[]"))
        if field_value in enum_values:
            matched.append({
                "name": comp["name"],
                "match_type": "enum_value"
            })
            continue

        # 检查properties键匹配
        properties = safe_json_load(comp.get("properties", "{}"))
        if field_value in properties:
            matched.append({
                "name": comp["name"],
                "match_type": "property_key"
            })

    return matched

def find_matching_components_from_data(data: dict, components: list) -> list:
    """
    从数据中查找匹配的组件（仅匹配enum_values和properties）

    Args:
        data: 要匹配的数据字典
        components: 组件列表，每个组件应包含：
            - properties: 属性字典
            - enum_values: 枚举值列表

    Returns:
        list: 匹配结果列表，包含组件名、匹配字段和匹配类型
            匹配类型只有两种：
            - "enum_value": 匹配枚举值
            - "property_key": 匹配属性键
    """
    matched = []
    if not isinstance(data, dict):
        return matched

    for comp in components:
        if not isinstance(comp, dict):
            continue

        matched_fields = []
        comp_name = comp.get("name")
        properties = safe_json_load(comp.get("properties", "{}"))
        enum_values = safe_json_load(comp.get("enum_values", "[]"))

        for field in data.keys():
            # 1. 检查enum_values匹配
            if field in enum_values:
                matched_fields.append({
                    "field": field,
                    "match_type": "enum_value"
                })
                continue

            # 2. 检查properties键匹配
            if field in properties:
                matched_fields.append({
                    "field": field,
                    "match_type": "property_key"
                })

        if matched_fields:
            matched.append({
                "name": comp_name,
                "matched_fields": [m["field"] for m in matched_fields],
                "match_type": "|".join(set(m["match_type"] for m in matched_fields))
            })

    return matched
def extract_example_from_content(content: dict) -> dict:
    """
    从content中提取example数据

    Args:
        content: 包含application/json等内容的字典

    Returns:
        dict: 提取的example数据，解析失败返回空字典
    """
    if not isinstance(content, dict):
        return {}

    # 尝试从各种可能的content类型中获取数据
    for content_type in ["application/json", "application/xml", "text/plain"]:
        content_data = content.get(content_type, {})
        if not content_data:
            continue

        example = content_data.get("example")
        if not example:
            continue

        if isinstance(example, str):
            try:
                return json.loads(example)
            except json.JSONDecodeError:
                continue
        elif isinstance(example, dict):
            return example

    return {}

def safe_get(data, *keys, default=None):
    """
    安全地访问嵌套字典字段
    """
    for key in keys:
        if isinstance(data, dict) and key in data:
            data = data[key]
        else:
            return default
    return data

def clean_example_field(data):
    """
    递归清理数据结构中所有字符串类型的 \n 和 \r 字符，特别针对 example 字段。
    """
    if isinstance(data, dict):
        cleaned = {}
        for key, value in data.items():
            if key == "example" and isinstance(value, str):
                # 替换字符串中的 \n 和 \r
                cleaned[key] = value.replace('\n', '').replace('\r', '')
            else:
                cleaned[key] = clean_example_field(value)
        return cleaned
    elif isinstance(data, list):
        return [clean_example_field(item) for item in data]
    else:
        return data

def safe_str(value, default="UNKNOWN"):
    return value if isinstance(value, str) and value.strip() else default
def sanitize_property_key(key: str) -> str:
    """
    清洗属性名中的非法字符，确保符合 Neo4j 的命名规范。
    """
    return key.replace('/', '_').replace('.', '_').replace('[', '_').replace(']', '_')

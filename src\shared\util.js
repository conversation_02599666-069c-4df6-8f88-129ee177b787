import { getDownLoadFileBlob } from "@/request/index"
import IMessage from "../components/iMessage"
import JSEncrypt from "jsencrypt"
import { configStore } from "@/store"

//似于 qs.stringify方法, 格式化一个对象为 query string.
export function queryStringify(obj, prefix = "") {
  const pairs = []

  for (let key in obj) {
    if (!Object.prototype.hasOwnProperty.call(obj, key)) {
      continue
    }

    const value = obj[key]
    const enKey = encodeURIComponent(key)
    let pair
    if (typeof value === "object") {
      pair = queryStringify(value, prefix ? prefix + "[" + enKey + "]" : enKey)
    } else {
      pair = (prefix ? prefix + "[" + enKey + "]" : enKey) + "=" + encodeURIComponent(value)
    }
    pairs.push(pair)
  }
  return pairs.join("&")
}

//拼接路径 baseUrl/id
export function addQuery(url, id) {
  return id !== undefined ? `${url}/${id}` : url
}

//拼接查询参数 ？key=value
export function addParams(url, data) {
  const paramsStr = Object.entries(data).reduce((pre, cur, index) => {
    pre += `${index === 0 ? "" : "&"}${cur[0]}=${cur[1]}`

    return pre
  }, "")

  return `${url}?${paramsStr}`
}

export function createAToDownload(url, name) {
  const link = document.createElement("a")
  link.style.display = "none"
  link.href = url
  link.setAttribute("download", name)
  link.click()
}

//下载
export async function downloadContent(url, param) {
  const res = await getDownLoadFileBlob(url, param)
  if (!res) return

  const disposition = decodeURI(res.headers["content-disposition"])
  const fileName = disposition.split("'")[1]?.slice(0, -1)

  const blob = new Blob([res.data])
  const blobUrl = window.URL.createObjectURL(blob)

  createAToDownload(blobUrl, fileName)

  IMessage.destroy()
  window.URL.revokeObjectURL(blobUrl)
  IMessage.success("导出报告成功")
}

//相对父级偏移
export function getTop(e, parent) {
  if (parent && parent === e) return 0

  let offset = e?.offsetTop

  if (e?.offsetParent != null) offset += getTop(e?.offsetParent, parent)

  return offset
}

export const ls = {
  setItem(key, value) {
    window.localStorage.setItem(key, value)
  },
  getItem(key) {
    return window.localStorage.getItem(key)
  },
  remove(key) {
    window.localStorage.removeItem(key)
  },
  clear() {
    window.localStorage.clear()
  },
}
window.__PUBLIC_KEY__ =
  "-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCdXenoRdV7W+oLwRtACB1xaFy2\n1euyzSSKdUEseBN1kBWfLZphEP22FSmu7N507Y41R52TRFtWQrsQiOQrhJOhhpeA\nTGZRRKrymYjYl5KKLSmnUmxjDuljYACwe9kfViv1+Agh3Zw2mmKtRDANzHUQ4aAY\nfNiyGKiCN7N+svvJ3wIDAQAB\n-----END PUBLIC KEY-----"

export function encodePassword(password) {
  // if (process.env.NODE_ENV === "development") {
  //   return password
  // }

  // if (!window.__PUBLIC_KEY__) {
  //   return password
  // }
  const encryptor = new JSEncrypt()
  console.log("123123123")
  console.log(window.__PUBLIC_KEY__)
  encryptor.setPublicKey(window.__PUBLIC_KEY__)
  console.log(window.__PUBLIC_KEY__)

  return encryptor.encrypt(password)
}

export function getConfig() {
  const { configProjectState, setProjectConfig } = configStore()

  return {
    set: setProjectConfig,
    config: configProjectState,
    title: "修改项目配置",
  }
}

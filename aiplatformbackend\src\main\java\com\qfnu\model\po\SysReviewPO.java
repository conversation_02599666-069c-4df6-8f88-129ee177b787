package com.qfnu.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import java.io.Serializable;
import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("sys_review")
@ApiModel(value = "SysReviewPO", description = "SysReview实体类")
public class SysReviewPO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 审核ID（主键）
     */
    @TableField(value = "review_id")
    @ApiModelProperty(value = "审核ID（主键）")
    private String reviewId;

    /**
     * 关联工具ID
     */
    @TableField(value = "related_id")
    @ApiModelProperty(value = "关联工具ID")
    private String relatedId;

    /**
     * 提交时间
     */
    @TableField(value = "submission_time")
    @ApiModelProperty(value = "提交时间")
    private Date submissionTime;

    /**
     * 审批时间
     */
    @TableField(value = "approval_time")
    @ApiModelProperty(value = "审批时间")
    private Date approvalTime;

    /**
     * 审批状态（0=未通过, 1=通过, 2=待审核）
     */
    @TableField(value = "approval_status")
    @ApiModelProperty(value = "审批状态（0=未通过, 1=通过, 2=待审核）")
    private String approvalStatus;

    /**
     * 审核人
     */
    @TableField(value = "reviewer")
    @ApiModelProperty(value = "审核人")
    private String reviewer;

}
<template>
    <div class="min-h-screen bg-gray-50">
      <div class="flex min-h-screen">
        <!-- 主内容区域 -->
        <main :class="['flex-1 p-1', isCollapsed ? 'ml-2' : 'ml-6', 'transition-all duration-300']">
        <!-- 页面标题和搜索栏 -->
        <div class="mb-4">
          <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
            <!-- 分类导航 -->
            <div class="flex-grow flex flex-wrap gap-4">
              <div v-for="category in categories" :key="category" :class="['cursor-pointer relative py-2 text-base font-medium transition-colors',
                selectedCategory === category ? 'text-blue-600' : 'text-gray-600 hover:text-blue-600']"
                @click="handleCategorySelect(category)">
                {{ category }}
                <div
                  class="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600 transform origin-left transition-transform duration-300"
                  :class="selectedCategory === category ? 'scale-x-100' : 'scale-x-0'" />
              </div>
            </div>
            <!-- 搜索栏 -->
            <div class="relative w-full sm:w-80">
              <a-input-search v-model:value="searchQuery" placeholder="搜索测试工具" class="w-full"
                :style="{ borderRadius: '9999px' }" />
            </div>
          </div>
        </div>
  
        <!-- 工具列表 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div v-for="tool in filteredTools" :key="tool.id"
            class="group bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 p-4 border border-gray-100 hover:border-blue-200 flex h-64 w-full">
            <div class="flex-1 flex flex-col justify-between gap-2">
              <div>
                <div class="flex items-center gap-3 mb-1">
                  <div class="relative flex-shrink-0">
                    <div
                      class="w-12 h-12 rounded-lg shadow-sm overflow-hidden bg-gradient-to-br from-blue-100 to-white p-0.5">
                      <img :src="tool.icon" :alt="tool.name" class="w-full h-full rounded-xl object-cover">
                    </div>
                    <div v-if="tool.highlight"
                      class="absolute -top-1 -right-1 px-2 py-0.5 bg-gradient-to-r from-green-400 to-blue-500 text-white text-[10px] font-medium rounded-full shadow-sm">
                      {{ tool.highlight }}
                    </div>
                  </div>
                  <div class="flex-1">
                    <div class="flex flex-col">
                      <div class="flex justify-between items-center mb-2 ml-4">
                        <h3 class="text-base font-semibold text-gray-900 line-clamp-1 group-hover:text-blue-600 transition-colors">
                          {{ tool.name }}
                        </h3>
                      </div>
                      <div class="ml-4 text-xs self-start text-gray-500">2024-07-25 12:11:59</div>
                    </div>
                    <div class="flex items-center gap-2 mt-2 justify-end">
                          <div class="inline-block px-2 py-0.5 bg-blue-50 text-blue-600 text-sm font-medium rounded-full group-hover:bg-blue-100 transition-colors">
                            {{ tool.category }}
                          </div>
                          <div class="inline-block px-2 py-0.5 bg-purple-50 text-purple-600 text-sm font-medium rounded-full">
                            {{ tool.group }}
                          </div>
                        </div>
                  </div>
                </div>
                <p class="text-gray-600 text-sm line-clamp-2 mb-2 mt-4">{{ tool.description }}</p>
              </div>
              <div class="flex flex-col gap-1">
                <div class="flex items-center gap-3">
                  <div class="flex items-center text-yellow-500 bg-yellow-50 px-2 py-1 rounded-full text-xs">
                    <span class="i-mdi-star-outline"></span>
                    <span class="font-medium ml-1">{{ tool.score }}分</span>
                  </div>
                  <div class="text-gray-300 text-xs">•</div>
                  <div class="text-gray-500 text-xs bg-gray-50 px-2 py-1 rounded-full">{{ tool.usage }}次使用</div>
                </div>
                <div class="flex items-center justify-between pt-2 border-t border-gray-100">
                  <div class="flex items-center space-x-2">
                    <div class="w-6 h-6 rounded-full ring-1 ring-blue-100 overflow-hidden">
                      <img :src="tool.author.avatar" :alt="tool.author.name" class="w-full h-full object-cover">
                    </div>
                    <span class="text-xs text-gray-700">{{ tool.author.name }}</span>
                  </div>
                  <a-button type="primary"
                    class="!rounded-full !shadow-sm hover:!shadow-md !bg-gradient-to-r !from-blue-500 !to-blue-600 !border-0 !px-4 !text-xs !h-7"
                    @click="handleViewDetails(tool)">
                    查看详情
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>
  
        <!-- 子路由视图 -->
        <router-view></router-view>
  
        <!-- 分页 -->
        <div class="mt-6 flex justify-center">
          <a-pagination v-model:current="currentPage" :total="total" :pageSize="10" @change="handlePageChange" />
        </div>
      </main>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, watch, onMounted } from 'vue';
  import { getRandomAvatar,getRandomTitle } from '@/utils/getRandomAvatar';
  import { getToolsList } from '@/utils/api';
  import $loading from '@/utils/loading';
  
  const isCollapsed = ref(false);
  const searchQuery = ref('');
  const currentPage = ref(1);
  const selectedCategory = ref('全部');
  
  const categories = ['全部', '性能测试', '接口测试', '自动化测试', '安全测试', '代码分析'];
  
  interface Tool {
    id: string;
    name: string;
    icon: string;
    url: string;
    description: string;
    category: string;
    score: number;
    usage: number;
    group: string;
    highlight?: string;
    author: {
      name: string;
      avatar: string;
    };
  }
  
  const tools = ref<Tool[]>([]);
  const total = ref<number>(0);

// 获取工具列表数据
const fetchToolsList = async () => {
  const loadingInstance = $loading.show();
  try {
    const res = await getToolsList({
      pageNum: currentPage.value,
      pageSize: 10,
      category: '测试工具',
      tags: selectedCategory.value === '全部' ? undefined : selectedCategory.value
    })
    if (res) {
      tools.value = res.records.map(tool => ({
        id: tool.toolId,
        name: tool.toolName,
        icon: getRandomTitle(),
        url: tool.linkAddress,
        description: tool.functionDescription,
        category: tool.tags,
        score: tool.rating,
        usage: tool.usageCount,
        group: tool.category,
        author: {
          name: tool.creators,
          avatar: getRandomAvatar()
        }
      }))
      total.value = res.total
    }
  } catch (error) {
    console.error('获取工具列表失败:', error)
  }finally {
    loadingInstance.close();
  }
}
  
  // 过滤工具列表
  const filteredTools = computed(() => {
    return tools.value.filter(tool => {
      const matchSearch = tool.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
                         tool.description.toLowerCase().includes(searchQuery.value.toLowerCase());
      const matchCategory = selectedCategory.value === '全部' || tool.category === selectedCategory.value;
      return matchSearch && matchCategory;
    });
  });

  // 监听搜索和分类变化
  watch([searchQuery, selectedCategory], () => {
    currentPage.value = 1
    fetchToolsList()
  })

  // 监听页码变化
  watch(currentPage, () => {
    fetchToolsList()
  })

  // 初始化加载数据
  onMounted(() => {
    fetchToolsList()
  })

  const handleViewDetails = (tool: any) => {
    window.open(tool.url, '_blank');
  };

  const handlePageChange = (page: number) => {
    currentPage.value = page;
  };

  const handleCategorySelect = (category: string) => {
    selectedCategory.value = category;
  };
  </script>
  
  <style scoped>
  .rounded-button {
    border-radius: 9999px;
  }
  </style>
package com.qfnu.config;

import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class ModelConfig {
    private static final Map<String, String> MODEL_URL_MAP = new HashMap<>();

    static {
        // 初始化模型URL映射
        MODEL_URL_MAP.put("qwen2.5:7b", "http://10.113.6.37:11434/v1/chat/completions");
        MODEL_URL_MAP.put("qwq:latest", "http://10.113.54.98:11434/v1/chat/completions");
        MODEL_URL_MAP.put("deepseek-r1:32b","http://10.113.54.98:11434/v1/chat/completions");
    }

    public static String getModelUrl(String modelName) {
        return MODEL_URL_MAP.getOrDefault(modelName, "http://10.113.6.37:11434/v1");
    }
}
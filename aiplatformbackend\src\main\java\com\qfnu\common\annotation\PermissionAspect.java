package com.qfnu.common.annotation;

import com.qfnu.common.exception.custom.PermissionDeniedException;
import com.qfnu.utils.JwtUtil;
import io.jsonwebtoken.Claims;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Aspect
@Component
public class PermissionAspect {

    private final HttpServletRequest request;

    public PermissionAspect(HttpServletRequest request) {
        this.request = request;
    }

    @Before("@annotation(com.qfnu.common.annotation.RequiredPermission)")
    public void checkPermission(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        RequiredPermission requiredPermission = method.getAnnotation(RequiredPermission.class);

        if (requiredPermission == null) {
            // 如果方法上没有 @RequiredPermission 注解，直接返回
            return;
        }

        String[] allowedRoles = requiredPermission.value();

        // 从请求头中获取 Token
        String token = request.getHeader("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            throw new PermissionDeniedException("未提供有效的 Token");
        }
        token = token.substring(7);

        // 解析 Token 获取用户角色
        Claims claims = JwtUtil.getClaimsFromToken(token);
        if (claims == null) {
            throw new PermissionDeniedException("无法解析 Token");
        }
        String userRole = (String) claims.get("role");
        if (userRole == null) {
            throw new PermissionDeniedException("Token 中未包含角色信息");
        }

        // 处理每个权限字符串
        List<String> allowedRoleList = Arrays.stream(allowedRoles)
                .flatMap(permission -> Arrays.stream(permission.split(",")))
                .map(String::trim)
                .collect(Collectors.toList());

        if (!allowedRoleList.contains(userRole)) {
            throw new PermissionDeniedException("没有权限访问该方法");
        }
    }
}

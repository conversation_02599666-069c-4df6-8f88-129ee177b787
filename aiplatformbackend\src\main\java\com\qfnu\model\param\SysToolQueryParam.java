package com.qfnu.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SysToolQueryParam", description = "SysToolQueryParam查询参数对象")
public class SysToolQueryParam extends BasePage {

    @ApiModelProperty("工具ID")    
    private String toolId;

    @ApiModelProperty("工具类别")
    private String category;

    @ApiModelProperty("工具名称")
    private String toolName;

    @ApiModelProperty("标签")
    private String tags;

    @ApiModelProperty("创建者")
    private String creators;

    @ApiModelProperty("审核状态 0-未审核 1-审核通过 2-审核不通过")
    private String reviewStatus;

    @ApiModelProperty("是否发布 0-未发布 1-已发布")
    private String isPublished;

    @ApiModelProperty("创建时间范围-开始")
    private String createTimeStart;

    @ApiModelProperty("创建时间范围-结束")
    private String createTimeEnd;

    @ApiModelProperty("排序字段")
    private String sortField;

    @ApiModelProperty("排序方式 asc-升序 desc-降序")
    private String sortOrder;
}

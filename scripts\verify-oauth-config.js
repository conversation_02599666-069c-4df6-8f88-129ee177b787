#!/usr/bin/env node

/**
 * OAuth配置验证脚本
 * 用于验证OAuth2.0配置是否正确
 */

const https = require('https');
const { URL } = require('url');

// 配置信息
const config = {
  client_id: 'UK59v8',
  client_secret: 'M02KuH',
  base_url: 'https://sso.das-security.cn',
  authorization_endpoint: '/iam/auth/oauth2/sso',
  token_endpoint: '/iam/auth/oauth2/accessToken',
  userinfo_endpoint: '/iam/auth/oauth2/userinfo'
};

console.log('🔍 开始验证OAuth2.0配置...\n');

// 验证服务器连通性
function checkServerConnectivity() {
  return new Promise((resolve, reject) => {
    console.log('📡 检查服务器连通性...');
    
    const url = new URL(config.base_url);
    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: '/',
      method: 'GET',
      timeout: 5000
    };

    const req = https.request(options, (res) => {
      console.log(`✅ 服务器连通性正常 (状态码: ${res.statusCode})`);
      resolve(true);
    });

    req.on('error', (err) => {
      console.log(`❌ 服务器连接失败: ${err.message}`);
      reject(err);
    });

    req.on('timeout', () => {
      console.log('❌ 服务器连接超时');
      req.destroy();
      reject(new Error('连接超时'));
    });

    req.end();
  });
}

// 验证授权端点
function checkAuthorizationEndpoint() {
  return new Promise((resolve, reject) => {
    console.log('\n🔐 检查授权端点...');
    
    const authUrl = config.base_url + config.authorization_endpoint;
    const testParams = new URLSearchParams({
      client_id: config.client_id,
      redirect_uri: 'http://localhost:3000/login',
      response_type: 'code',
      scope: 'openid profile email',
      state: 'test_state',
      nonce: 'test_nonce'
    });

    const fullUrl = `${authUrl}?${testParams.toString()}`;
    console.log(`📋 授权URL: ${fullUrl}`);

    const url = new URL(fullUrl);
    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname + url.search,
      method: 'GET',
      timeout: 5000
    };

    const req = https.request(options, (res) => {
      if (res.statusCode === 200 || res.statusCode === 302) {
        console.log(`✅ 授权端点可访问 (状态码: ${res.statusCode})`);
        resolve(true);
      } else {
        console.log(`⚠️  授权端点返回状态码: ${res.statusCode}`);
        resolve(false);
      }
    });

    req.on('error', (err) => {
      console.log(`❌ 授权端点访问失败: ${err.message}`);
      reject(err);
    });

    req.on('timeout', () => {
      console.log('❌ 授权端点访问超时');
      req.destroy();
      reject(new Error('访问超时'));
    });

    req.end();
  });
}

// 验证配置完整性
function validateConfig() {
  console.log('\n📋 验证配置完整性...');
  
  const requiredFields = ['client_id', 'client_secret', 'base_url'];
  const missingFields = [];

  requiredFields.forEach(field => {
    if (!config[field] || config[field] === `your_${field}`) {
      missingFields.push(field);
    }
  });

  if (missingFields.length > 0) {
    console.log(`❌ 配置不完整，缺少字段: ${missingFields.join(', ')}`);
    return false;
  } else {
    console.log('✅ 配置完整性验证通过');
    return true;
  }
}

// 显示配置摘要
function showConfigSummary() {
  console.log('\n📊 配置摘要:');
  console.log(`   Client ID: ${config.client_id}`);
  console.log(`   Client Secret: ${config.client_secret.substring(0, 3)}***`);
  console.log(`   服务器地址: ${config.base_url}`);
  console.log(`   授权端点: ${config.authorization_endpoint}`);
  console.log(`   令牌端点: ${config.token_endpoint}`);
  console.log(`   用户信息端点: ${config.userinfo_endpoint}`);
}

// 主验证流程
async function main() {
  try {
    showConfigSummary();
    
    // 验证配置完整性
    if (!validateConfig()) {
      process.exit(1);
    }

    // 验证服务器连通性
    await checkServerConnectivity();

    // 验证授权端点
    await checkAuthorizationEndpoint();

    console.log('\n🎉 OAuth2.0配置验证完成！');
    console.log('\n📝 下一步操作:');
    console.log('   1. 启动前端项目: npm run dev');
    console.log('   2. 启动后端项目');
    console.log('   3. 访问测试页面: http://localhost:3000/ad-auth-test');
    console.log('   4. 测试完整的OAuth认证流程');

  } catch (error) {
    console.log(`\n❌ 验证失败: ${error.message}`);
    console.log('\n🔧 建议检查:');
    console.log('   1. 网络连接是否正常');
    console.log('   2. TAM服务器地址是否正确');
    console.log('   3. client_id和client_secret是否有效');
    console.log('   4. 防火墙设置是否允许访问');
    process.exit(1);
  }
}

// 运行验证
main();

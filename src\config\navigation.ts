import { createRouter, create<PERSON>ebH<PERSON>ory, RouteRecordRaw } from 'vue-router';
import Layout from '../components/layout/BasicLayout.vue';

const routes: RouteRecordRaw[] = [
  // AI 提效
  {
    path: '/ai-efficiency',
    component: Layout,
    redirect: '/ai-efficiency/apps',
    name: 'AiEfficiency',
    meta: { breadcrumb: 'AI提效' },
    children: [
      {
        path: 'apps',
        name: 'AiA<PERSON>',
        component: () => import('../views/AIEfficiency/AIEfficiencyApps.vue'),
        meta: { breadcrumb: 'AI应用' }
      },
      {
        path: 'models',
        name: 'AiModels',
        component: () => import('../views/AIEfficiency/AIEfficiencyModels.vue'),
        meta: { breadcrumb: 'AI模型' }
      },
      {
        path: 'knowledge',
        name: 'AiKnowledge',
        component: () => import('../views/AIEfficiency/AIEfficiencyKnowledge.vue'),
        meta: { breadcrumb: '知识库' }
      },
      {
        path: 'mcp',
        name: 'AiMcp',
        component: () => import('../views/AIEfficiency/AIEfficiencyMCP.vue'),
        meta: { breadcrumb: 'MCP' }
      }
    ]
  },

  // 测试工具
  {
    path: '/test-tools',
    component: Layout,
    redirect: '/test-tools/platform',
    name: 'TestTools',
    meta: { breadcrumb: '测试工具' },
    children: [
      {
        path: 'platform',
        name: 'TestPlatform',
        component: () => import('../views/TestTool/TestToolPlatform.vue'),
        meta: { breadcrumb: '测试平台' }
      },
      {
        path: 'statistics',
        name: 'TestStatistics',
        component: () => import('../views/TestTool/TestToolStatistics.vue'),
        meta: { breadcrumb: '度量看板' }
      },
      {
        path: 'itr',
        name: 'TestITR',
        component: () => import('../views/TestTool/ITR/ITR.vue'),
        redirect: '/test-tools/itr/analysis',
        meta: { breadcrumb: 'ITR' },
        children: [
          {
            path: 'analysis',
            name: 'TestITRAnalysis',
            component: () => import('../views/TestTool/ITR/ITRAnalysis.vue'),
            meta: { breadcrumb: 'ITR分析表' }
          },
          {
            path: 'report',
            name: 'TestITRReport',
            component: () => import('../views/TestTool/ITR/ITRReport.vue'),
            meta: { breadcrumb: '数据填报' }
          },
          {
            path: 'feature-management',
            name: 'TestITRFeatureManagement',
            component: () => import('../views/TestTool/ITR/ITRFeatureManagement.vue'),
            meta: { breadcrumb: '特性管理' }
          }
        ]
      }
    ]
  },

  // 系统配置
  {
    path: '/system',
    component: Layout,
    redirect: '/system/toolManagement',
    name: 'System',
    meta: { breadcrumb: '系统配置' },
    children: [
      {
        path: 'toolManagement',
        name: 'ToolManagement',
        component: () => import('../views/Settings/ToolManagement.vue'),
        meta: { breadcrumb: '工具管理' }
      },
      {
        path: 'basicSettings',
        name: 'BasicSettings',
        component: () => import('../views/Settings/BasicSettings.vue'),
        meta: { breadcrumb: '基本配置' },
        children: [
          {
            path: 'user-management',
            name: 'UserManagement',
            component: () => import('../views/UserManagement/UserList.vue'),
            meta: { breadcrumb: '用户管理' }
          },
          {
            path: 'feature-management',
            name: 'FeatureManagement',
            component: () => import('../views/FeatureManagement/FeatureList.vue'),
            meta: { breadcrumb: '特性管理' }
          }
        ]
      }
    ]
  }
];

export const router = createRouter({
  history: createWebHistory(),
  routes
});

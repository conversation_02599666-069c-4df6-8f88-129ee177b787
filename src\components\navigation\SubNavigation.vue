<template>
  <aside :class="[isCollapsed ? 'w-16' : 'w-48', 'ml-0 bg-white border-r border-gray-200 fixed h-full shadow-sm']">
    <button @click="toggleCollapse"
      class="absolute -right-3 top-6 flex items-center justify-center bg-white border border-gray-200 rounded-full p-1.5 text-gray-500 hover:text-blue-500 hover:border-blue-300 hover:shadow-md transition-all z-50">
      <i
        :class="[isCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left', 'text-xs transition-transform duration-300']"></i>
    </button>
    <div class="py-6 h-full">
      <MenuList :items="currentNavItems" :is-collapsed="isCollapsed" />
    </div>
  </aside>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import { navigationConfig } from '@/config/navigationConfig';
import MenuList from './MenuList.vue';

const route = useRoute();
const props = defineProps<{
  isCollapsed: boolean
}>();

const emit = defineEmits<{
  (e: 'update:isCollapsed', value: boolean): void
}>();

const toggleCollapse = () => {
  emit('update:isCollapsed', !props.isCollapsed);
};

const currentNavItems = computed(() => {
  // 根据当前路由显示对应的子导航
  const currentPath = route.path;
  const parent = navigationConfig.find(item => currentPath.startsWith(item.path));
  return parent?.children || [];
});

const isActive = (path: string) => route.path.startsWith(path);
</script>
<template>
  <a-modal
    :visible="visible"
    :title="title"
    @cancel="handleClose"
    width="90vw"
    :footer="null"
    :maskClosable="false"
    :body-style="{ maxHeight: '70vh', overflowY: 'auto', padding: '24px' }"
  >
    <div class="data-model-section">
      <div class="filter-bar">
        <div style="display: flex; gap: 12px; align-items: center;">
          <a-select
            v-model:value="dataModelStatusFilter"
            mode="multiple"
            placeholder="状态筛选"
            style="width: 200px"
            @change="onDataModelStatusFilterChange"
          >
            <a-select-option 
              v-for="status in dataModelStatusOptions" 
              :key="status.value" 
              :value="status.value"
            >
              {{ status.label }}
            </a-select-option>
          </a-select>

          <a-select
            v-model:value="dataModelTypeFilter"
            mode="multiple"
            placeholder="组件类型筛选"
            style="width: 200px"
            @change="onDataModelTypeFilterChange"
          >
            <a-select-option 
              v-for="type in dataModelTypeOptions" 
              :key="type" 
              :value="type"
            >
              {{ type }}
            </a-select-option>
          </a-select>

          <a-input-search
            v-model:value="dataModelNameSearch"
            placeholder="搜索数据模型名称"
            style="width: 200px"
            @search="onDataModelSearch"
            @input="(e) => debouncedSearch(e.target.value)"
          />
        </div>
        <div style="display: flex; gap: 8px;">
          <a-button 
            :type="selectedDataModelRowKeys.length ? 'danger' : 'primary'" 
            :disabled="!selectedDataModelRowKeys.length"
            @click="handleBatchDeleteDataModel"
            class="batch-delete-btn"
          >
            <template #icon><DeleteOutlined /></template>
            批量删除
          </a-button>
          <a-button 
            type="primary" 
            :disabled="!selectedDataModelRowKeys.length"
            @click="handleDataModelEmbed"
            class="embed-btn"
          >
            <template #icon><ApiOutlined /></template>
            嵌入模型
          </a-button>
          <a-button type="primary" @click="showDataModelColumnSettings" class="column-settings-btn">
            <template #icon><SettingOutlined /></template>
            自定义列
          </a-button>
        </div>
      </div>

      <a-table
        :dataSource="pagedDataModelList"
        :columns="visibleDataModelColumns"
        :pagination="{
          current: dataModelPage,
          pageSize: dataModelPageSize,
          total: dataModelTotal,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条`
        }"
        :rowSelection="dataModelRowSelection"
        @change="onDataModelTableChange"
        :scroll="{ x: 'max-content', y: 'calc(70vh - 200px)' }"
        @row-click="handleDataModelClick"
        rowKey="id"
        bordered
        size="middle"
        :loading="dataModelLoading"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getDataModelStatusColor(record.status)">
              {{ getDataModelStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'schema_type'">
            <a-tag :color="getSchemaTypeColor(record.schema_type)">
              {{ record.schema_type }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 数据模型列设置弹窗 -->
    <a-modal
      v-model:visible="dataModelColumnSettingsVisible"
      title="自定义列"
      width="400px"
      :footer="null"
      @cancel="dataModelColumnSettingsVisible = false"
      class="column-settings-modal"
    >
      <div class="column-settings-content">
        <div class="column-settings-header">
          <a-checkbox
            :indeterminate="isDataModelIndeterminate"
            :checked="isDataModelAllSelected"
            @change="onDataModelCheckAllChange"
          >
            全选
          </a-checkbox>
        </div>
        <div class="column-settings-body">
          <a-checkbox-group v-model:value="selectedDataModelColumns" style="width: 100%">
            <a-row>
              <a-col :span="8" v-for="col in dataModelAllColumns" :key="col.key">
                <a-checkbox :value="col.key">{{ col.title }}</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </div>
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch, onUnmounted } from 'vue';
import { AppstoreOutlined, SettingOutlined, EyeOutlined, ApiOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import http from '@/utils/ai/http';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '数据模型列表'
  }
});

const emit = defineEmits(['update:visible', 'close', 'embedDataModel', 'dataModelSelected']);

// 数据模型相关
const dataModelColumnSettingsVisible = ref(false);
const selectedDataModelColumns = ref(['id', 'entity_name', 'schema_type', 'status', 'title']);
const dataModelList = ref([]); // 数据模型列表数据
const dataModelStatusFilter = ref([]);
const dataModelTypeFilter = ref([]);
const dataModelNameSearch = ref('');
const dataModelPage = ref(1);
const dataModelPageSize = ref(10);
const selectedDataModelRowKeys = ref([]);
const dataModelLoading = ref(false); // 添加加载状态

// 数据模型行选择配置
const dataModelRowSelection = {
  selectedRowKeys: selectedDataModelRowKeys,
  onChange: (keys) => {
    selectedDataModelRowKeys.value = keys;
  }
};

// 获取数据模型列表
const fetchDataModelList = async () => {
  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }
  dataModelLoading.value = true;
  try {
    const res = await http.get(`/knowledge_api/${relationId}/get_data_model_list/`);
    if (res.data.code === 200) {
      const dataArr = res.data.data || [];
      dataModelList.value = dataArr; // 直接使用原始数据，不添加递增ID
    } else {
      dataModelList.value = [];
      message.error(res.data.message || '获取数据模型列表失败');
    }
  } catch (error) {
    dataModelList.value = [];
    message.error('获取数据模型列表失败');
    console.error('获取数据模型列表失败:', error);
  } finally {
    dataModelLoading.value = false;
  }
};

// 数据模型状态选项
const dataModelStatusOptions = computed(() => {
  const statusSet = new Set(dataModelList.value.map(item => item.status || 'pending')); // 将空状态视为待提取
  const statusLabelMap = {
    'active': '已提取',
    'inactive': '未提取到',
    'deprecated': '提取失败',
    'pending': '待提取'
  };
  return Array.from(statusSet)
    .map(status => ({
      value: status || 'pending', // 将空状态视为待提取
      label: statusLabelMap[status || 'pending'] || status
    }))
    .sort((a, b) => {
      // 按照状态优先级排序
      const priority = {
        'active': 1,
        'inactive': 2,
        'pending': 3,
        'deprecated': 4
      };
      return (priority[a.value] || 999) - (priority[b.value] || 999);
    });
});

// 数据模型类型选项
const dataModelTypeOptions = computed(() => {
  const typeSet = new Set(dataModelList.value.map(item => item.schema_type));
  return Array.from(typeSet)
    .filter(type => type) // 过滤掉空值
    .sort(); // 按字母顺序排序
});

// 数据模型列配置
const dataModelColumns = [
  { title: 'ID', dataIndex: 'id', key: 'id', width: 60, ellipsis: true },
  { title: '数据模型', dataIndex: 'entity_name', key: 'entity_name', width: 150, ellipsis: true },
  { title: '组件类型', dataIndex: 'schema_type', key: 'schema_type', width: 80, ellipsis: true },
  { title: '状态', dataIndex: 'status', key: 'status', width: 150, ellipsis: true },
  { 
    title: '描述/标题', 
    dataIndex: 'title', 
    key: 'title', 
    width: 150, 
    ellipsis: true, 
    customRender: ({ text }) => text ? (text.length > 20 ? text.substring(0, 20) + '...' : text) : '-'
  },
  { 
    title: '枚举值列表', 
    dataIndex: 'enum_values', 
    key: 'enum_values', 
    width: 200, 
    ellipsis: true, 
    customRender: ({ text }) => {
      if (!text) return '-';
      const content = JSON.stringify(text);
      return content.length > 50 ? content.substring(0, 50) + '...' : content;
    }
  },
  { 
    title: '属性列表', 
    dataIndex: 'properties', 
    key: 'properties', 
    width: 200, 
    ellipsis: true, 
    customRender: ({ text }) => {
      if (!text) return '-';
      const content = JSON.stringify(text);
      return content.length > 50 ? content.substring(0, 50) + '...' : content;
    }
  },
  { 
    title: '必须属性列表', 
    dataIndex: 'required_properties', 
    key: 'required_properties', 
    width: 200, 
    ellipsis: true, 
    customRender: ({ text }) => {
      if (!text) return '-';
      const content = JSON.stringify(text);
      return content.length > 50 ? content.substring(0, 50) + '...' : content;
    }
  }
];

// 所有数据模型列
const dataModelAllColumns = [
  { title: 'ID', key: 'id' },
  { title: '数据模型', key: 'entity_name' },
  { title: '组件类型', key: 'schema_type' },
  { title: '描述/标题', key: 'title' },
  { title: '枚举值列表', key: 'enum_values' },
  { title: '属性列表', key: 'properties' },
  { title: '必须属性列表', key: 'required_properties' },
  { title: '状态', key: 'status' }
];

// 计算可见的数据模型列
const visibleDataModelColumns = computed(() => {
  return dataModelColumns.filter(col => selectedDataModelColumns.value.includes(col.key));
});

// 数据模型筛选后的列表
const filteredDataModelList = computed(() => {
  let list = dataModelList.value;
  
  if (dataModelStatusFilter.value.length > 0) {
    list = list.filter(item => dataModelStatusFilter.value.includes(item.status));
  }
  
  if (dataModelTypeFilter.value.length > 0) {
    list = list.filter(item => dataModelTypeFilter.value.includes(item.schema_type));
  }
  
  if (dataModelNameSearch.value) {
    const searchText = dataModelNameSearch.value.toLowerCase().trim();
    list = list.filter(item => {
      const entityName = (item.entity_name || '').toLowerCase();
      const title = (item.title || '').toLowerCase();
      const description = (item.description || '').toLowerCase();
      
      return entityName.includes(searchText) || 
             title.includes(searchText) || 
             description.includes(searchText);
    });
  }
  
  return list;
});

// 数据模型分页后的列表
const pagedDataModelList = computed(() => {
  const start = (dataModelPage.value - 1) * dataModelPageSize.value;
  return filteredDataModelList.value.slice(start, start + dataModelPageSize.value);
});

// 数据模型总数
const dataModelTotal = computed(() => filteredDataModelList.value.length);

// 获取数据模型状态文本
const getDataModelStatusText = (status) => {
  if (!status) return '待提取';
  const textMap = {
    'active': '已嵌入',
    'inactive': '未嵌入',
    'deprecated': '已废弃',
    'pending': '待审核'
  };
  return textMap[status] || status;
};

// 获取数据模型状态颜色
const getDataModelStatusColor = (status) => {
  if (!status) return 'warning'; // 待提取状态使用警告色
  const colorMap = {
    'active': 'success',    // 已嵌入 - 绿色
    'inactive': 'default',  // 未嵌入 - 灰色
    'deprecated': 'blue',   // 已废弃 - 蓝色
    'pending': 'blue'       // 待审核 - 蓝色
  };
  return colorMap[status] || 'default';
};

// 获取组件类型颜色
const getSchemaTypeColor = (type) => {
  const colorMap = {
    'string': 'blue',
    'object': 'green',
    'array': 'purple',
    'number': 'orange',
    'boolean': 'cyan',
    'integer': 'gold'
  };
  return colorMap[type?.toLowerCase()] || 'default';
};

// 数据模型相关方法
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

const onDataModelStatusFilterChange = (value) => {
  dataModelStatusFilter.value = value;
  dataModelPage.value = 1;
};

const onDataModelTypeFilterChange = (value) => {
  dataModelTypeFilter.value = value;
  dataModelPage.value = 1;
};

const onDataModelSearch = () => {
  dataModelPage.value = 1;
};

const onDataModelTableChange = (pagination, filters, sorter) => {
  dataModelPage.value = pagination.current;
  dataModelPageSize.value = pagination.pageSize;
};

const showDataModelColumnSettings = () => {
  dataModelColumnSettingsVisible.value = true;
};

const handleColumnSettingsConfirm = () => {
  dataModelColumnSettingsVisible.value = false;
};

const isDataModelIndeterminate = computed(() => {
  return selectedDataModelColumns.value.length > 0 && selectedDataModelColumns.value.length < dataModelAllColumns.length;
});

const isDataModelAllSelected = computed(() => {
  return selectedDataModelColumns.value.length === dataModelAllColumns.length;
});

const onDataModelCheckAllChange = (e) => {
  selectedDataModelColumns.value = e.target.checked ? dataModelAllColumns.map(col => col.key) : [];
};

// 数据模型嵌入相关
const handleDataModelEmbed = async () => {
  if (!selectedDataModelRowKeys.value.length) {
    message.warning('请选择要嵌入的数据模型');
    return;
  }

  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }

  try {
    // 调用嵌入接口
    const res = await http.post(`/knowledge_api/${relationId}/add_components`, {
      entity_list: selectedDataModelRowKeys.value
    });

    if (res.data.code === 200) {
      message.success(res.data.message || '嵌入成功');
      emit('update:visible', false);
      emit('close');
      emit('refresh-graph');
      selectedDataModelRowKeys.value = [];
    } else {
      message.error(res.data.message || '数据模型嵌入失败');
    }
  } catch (error) {
    message.error('数据模型嵌入失败');
    console.error('数据模型嵌入失败:', error);
  }
};

// 添加数据模型点击处理方法
const handleDataModelClick = async (record) => {
  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }

  try {
    const res = await http.get(`/knowledge_api/${relationId}/get_data_model_detail`, {
      params: {
        model_id: record.id
      }
    });

    if (res.data.code === 200) {
      // 将数据传递给父组件
      emit('dataModelSelected', res.data.data);
    } else {
      message.error(res.data.message || '获取数据模型详情失败');
    }
  } catch (error) {
    message.error('获取数据模型详情失败');
    console.error('获取数据模型详情失败:', error);
  }
};

// 批量删除数据模型
const handleBatchDeleteDataModel = async () => {
  if (!selectedDataModelRowKeys.value.length) {
    message.warning('请选择要删除的数据模型');
    return;
  }

  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }

  try {
    // 调整为新接口和参数格式
    const res = await http.delete(`/knowledge_api/${relationId}/delete_data_models`, {
      data: { ids: selectedDataModelRowKeys.value }
    });

    if (res.data.code === 200) {
      message.success(res.data.message || '批量删除成功');
      // 刷新数据模型列表并清除选中状态
      fetchDataModelList();
      selectedDataModelRowKeys.value = [];
    } else {
      message.error(res.data.message || '批量删除失败');
    }
  } catch (error) {
    message.error('批量删除失败');
    console.error('批量删除失败:', error);
  }
};

// 显示数据模型弹窗
const showDataModelWindow = () => {
  fetchDataModelList();
};

// 添加防抖的搜索方法
const debouncedSearch = (() => {
  let timer = null;
  return (value) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      dataModelNameSearch.value = value;
      dataModelPage.value = 1;
    }, 300);
  };
})();

// 暴露方法给父组件
defineExpose({
  showDataModelWindow
});
</script>

<style scoped>
.filter-bar {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 嵌入按钮样式 */
.embed-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.embed-btn :deep(.anticon) {
  display: flex;
  align-items: center;
  margin-right: 4px;
  font-size: 14px;
}

/* 自定义列按钮样式 */
.column-settings-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.column-settings-btn :deep(.anticon) {
  display: flex;
  align-items: center;
  margin-right: 4px;
  font-size: 14px;
}

:deep(.ant-table) {
  background: #fff;
}

:deep(.ant-table-wrapper) {
  background: #fff;
}

:deep(.ant-table-body) {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f0f0f0;
  }
}

/* 组件类型标签样式 */
:deep(.ant-tag) {
  margin: 0;
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
  border-radius: 4px;
  font-size: 12px;
  transition: all 0.3s;
}

:deep(.ant-tag:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 添加可点击行的样式 */
:deep(.clickable-row) {
  cursor: pointer;
}

:deep(.clickable-row:hover) {
  background-color: #f5f5f5;
}

/* 批量删除按钮样式 */
.batch-delete-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.batch-delete-btn.ant-btn-danger {
  background-color: #ff4d4f; /* 红色背景 */
  border-color: #ff4d4f;   /* 红色边框 */
  color: #fff; /* 白色文本 */
}

.batch-delete-btn.ant-btn-danger:hover {
  background-color: #ff7875; /* 悬停时的红色 */
  border-color: #ff7875;   /* 悬停时的红色边框 */
  color: #fff;
}

.batch-delete-btn :deep(.anticon) {
  display: flex;
  align-items: center;
  margin-right: 4px;
  font-size: 14px;
}

.batch-delete-btn.ant-btn-danger :deep(.anticon) {
  color: #fff; /* 白色图标 */
}

/* 自定义列弹窗样式 */
.column-settings-modal :deep(.ant-modal-content) {
  border-radius: 8px;
  overflow: hidden;
}

.column-settings-modal :deep(.ant-modal-header) {
  margin-bottom: 0;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.column-settings-modal :deep(.ant-modal-title) {
  font-size: 16px;
  font-weight: 500;
}

.column-settings-content {
  padding: 16px 24px;
}

.column-settings-header {
  padding-bottom: 12px;
  margin-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.column-settings-body {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 8px;
}

.column-settings-body::-webkit-scrollbar {
  width: 6px;
}

.column-settings-body::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.column-settings-body::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 美化复选框样式 */
.column-settings-modal :deep(.ant-checkbox-wrapper) {
  font-size: 14px;
}

.column-settings-modal :deep(.ant-checkbox) {
  top: 0;
}

.column-settings-modal :deep(.ant-checkbox-inner) {
  border-radius: 4px;
}

.column-settings-modal :deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.column-settings-modal :deep(.ant-checkbox-wrapper:hover .ant-checkbox-inner),
.column-settings-modal :deep(.ant-checkbox:hover .ant-checkbox-inner) {
  border-color: #1890ff;
}
</style>

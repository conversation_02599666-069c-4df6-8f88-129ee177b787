<template>
  <div class="itr-report-container">
    <div class="min-h-screen bg-gray-50 w-full">
      <div class="w-full px-2 sm:px-4 lg:px-6 py-2 sm:py-4">
        <!-- 筛选区域 -->
        <div class="bg-white rounded-lg shadow-sm p-2 sm:p-4 mb-2 sm:mb-4">
          <div class="flex flex-col sm:flex-row flex-wrap items-start sm:items-center gap-2 sm:gap-4">
            <div class="w-full sm:w-auto flex items-center gap-1 flex-shrink-0">
              <span class="text-gray-600 whitespace-nowrap min-w-[1.5rem]">产品：</span>
              <a-select v-model:value="filters.product" mode="multiple" class="min-w-[140px] !rounded-button"
                placeholder="请选择产品" allowClear :options="[
                  { value: 'USM V8', label: 'USM V8' },
                  { value: 'USM V9', label: 'USM V9' },
                  { value: 'TGFW', label: 'TGFW' },
                  { value: 'USM 云安全', label: 'USM 云安全' },
                  { value: 'WAF', label: 'WAF' },
                ]" />
            </div>

            <div class="w-full sm:w-auto flex items-center gap-1 flex-shrink-0">
              <span class="text-gray-600 whitespace-nowrap min-w-[1.5rem]">严重程度：</span>
              <a-select v-model:value="filters.severity" class="min-w-[100px]" mode="multiple" placeholder="选择严重程度"
                allowClear>
                <a-select-option :value="1">紧急</a-select-option>
                <a-select-option :value="2">严重</a-select-option>
                <a-select-option :value="3">一般</a-select-option>
                <a-select-option :value="4">建议</a-select-option>
              </a-select>
            </div>

            <div class="w-full sm:w-auto flex items-center gap-1 flex-shrink-0">
              <span class="text-gray-600 whitespace-nowrap min-w-[1.5rem]">BUG状态：</span>
              <a-select v-model:value="bugStatusArr" class="min-w-[100px]" mode="multiple" placeholder="选择BUG状态"
                allowClear>
                <a-select-option value="resolved">已解决</a-select-option>
                <a-select-option value="active">激活</a-select-option>
                <a-select-option value="closed">已关闭</a-select-option>
              </a-select>
            </div>

            <div class="w-full sm:w-auto flex items-center gap-1 flex-shrink-0">
              <span class="text-gray-600 whitespace-nowrap min-w-[1.5rem]">日期：</span>
              <a-range-picker v-model:value="filters.dateRange" class="flex-1" />
            </div>

            <!-- 将按钮容器移到日期后面，并移除右对齐 -->
            <div class="w-full sm:w-auto flex gap-1 mt-2 sm:mt-0 flex-shrink-0">
              <a-button type="primary" class="flex-1 sm:flex-none !rounded-button" @click="handleSearch">查询</a-button>
              <a-button class="flex-1 sm:flex-none !rounded-button" @click="handleClear">清空</a-button>
              <a-button type="primary" class="flex-1 sm:flex-none !rounded-button" @click="handleExport">导出全表</a-button>
              <a-button @click="toggleAdvancedPanel" class="flex-1 sm:flex-none !rounded-button">
                <template #icon><filter-outlined /></template>
                高级筛选
              </a-button>
            </div>

            <div class="ml-auto flex items-center gap-1 text-gray-500 flex-shrink-0">
              <sync-outlined class="cursor-pointer" @click="refreshData" />
              <span>未复核数：{{ total }}</span>
            </div>
          </div>
        </div>

        <!-- 新增高级筛选弹窗 -->
        <div v-show="showAdvancedPanel" class="border-t border-gray-200 pt-4 mt-4">
          <a-row :gutter="24">
            <!-- 分析责任人 -->
            <a-col :span="6" class="mb-4">
              <div class="flex items-center">
                <label class="text-gray-600 whitespace-nowrap min-w-[120px]">分析责任人：</label>
                <a-select v-model:value="advancedFilters.analyst" mode="multiple" class="!w-[calc(100%-120px)]"
                  placeholder="请选择" show-search :filter-option="filterUserOption" option-filter-prop="label" allowClear>
                  <a-select-option v-for="user in userList" :key="user.user_name" :value="user.user_name"
                    :label="user.real_name">
                    {{ user.real_name }}
                  </a-select-option>
                </a-select>
              </div>
            </a-col>

            <!-- BUG责任人 -->
            <a-col :span="6" class="mb-4">
              <div class="flex items-center">
                <label class="text-gray-600 whitespace-nowrap min-w-[120px]">BUG责任人：</label>
                <a-select v-model:value="advancedFilters.bug_owner" mode="multiple" placeholder="请选择BUG责任人"
                  class="!w-[calc(100%-120px)]"
                  :options="userList.map((u) => ({ value: u.real_name, label: u.real_name }))" allow-clear></a-select>
              </div>
            </a-col>

            <!-- 所属特性 -->
            <a-col :span="6" class="mb-4">
              <div class="flex items-center">
                <label class="text-gray-600 whitespace-nowrap min-w-[120px]">所属特性：</label>
                <a-cascader v-model:value="advancedFilters.feature" :options="categoryOptions" placeholder="请选择所属特性"
                  class="!w-[calc(100%-120px)]" :field-names="{ label: 'module_name', value: 'module_name' }"
                  allow-clear />
              </div>
            </a-col>

            <!-- 是否Bug -->
            <a-col :span="6" class="mb-4">
              <div class="flex items-center">
                <label class="text-gray-600 whitespace-nowrap min-w-[120px]">是否Bug：</label>
                <a-select v-model:value="advancedFilters.isBug" class="!w-[calc(100%-120px)]" placeholder="请选择"
                  allowClear>
                  <a-select-option value="是">是</a-select-option>
                  <a-select-option value="否">否</a-select-option>
                  <a-select-option value="待分析">待分析</a-select-option>
                </a-select>
              </div>
            </a-col>

            <!-- 分析状态 -->
            <a-col :span="6" class="mb-4">
              <div class="flex items-center">
                <label class="text-gray-600 whitespace-nowrap min-w-[120px]">缺陷状态：</label>
                <a-select v-model:value="advancedFilters.analysisStatus" class="!w-[calc(100%-120px)]" placeholder="请选择"
                  allowClear>
                  <a-select-option value="是">是</a-select-option>
                  <a-select-option value="否">否</a-select-option>
                </a-select>
              </div>
            </a-col>

            <!-- 整改状态 -->
            <a-col :span="6" class="mb-4">
              <div class="flex items-center">
                <label class="text-gray-600 whitespace-nowrap min-w-[120px]">整改状态：</label>
                <a-select v-model:value="advancedFilters.rectificationStatus" class="!w-[calc(100%-120px)]"
                  placeholder="请选择" allowClear>
                  <a-select-option value="待分析">待分析</a-select-option>
                  <a-select-option value="已分析">已分析</a-select-option>
                </a-select>
              </div>
            </a-col>

            <!-- 分析状态多选 -->
            <a-col :span="6" class="mb-4">
              <div class="flex items-center">
                <label class="text-gray-600 whitespace-nowrap min-w-[120px]">分析状态：</label>
                <a-select v-model:value="advancedFilters.is_analyzed" mode="multiple" class="!w-[calc(100%-120px)]"
                  placeholder="请选择" allowClear>
                  <a-select-option value="可分析">可分析</a-select-option>
                  <a-select-option value="待分析">待分析</a-select-option>
                  <a-select-option value="不分析-转需求">不分析-转需求</a-select-option>
                  <a-select-option value="不分析-重复问题">不分析-重复问题</a-select-option>
                  <a-select-option value="不分析-已知遗留问题">不分析-已知遗留问题</a-select-option>
                  <a-select-option value="不分析-个体硬件">不分析-个体硬件</a-select-option>
                  <a-select-option value="不分析-非问题">不分析-非问题</a-select-option>
                  <a-select-option value="不分析-不涉及">不分析-不涉及</a-select-option>
                </a-select>
              </div>
            </a-col>

            <!-- 占位 -->
            <a-col :span="6" class="mb-4"> </a-col>

            <!-- 按钮右对齐 -->
            <a-col :span="24" class="flex justify-end gap-4 mt-4">
              <a-button type="primary" @click="handleAdvancedSearch">高级搜索</a-button>
            </a-col>
          </a-row>
        </div>

        <!-- 表格区域 -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
          <div class="w-full overflow-x-auto table-container">
            <div class="table-wrapper">
              <a-table :row-selection="rowSelection" :columns="columns" :data-source="tableData" :loading="loading"
                :pagination="false" :scroll="{ x: 'max-content' }" class="custom-table w-full" row-key="id"
                v-column-resizable>
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'severity'">
                    <a-tag :color="getSeverityColor(record.severity)">
                      {{ ["紧急", "严重", "一般", "建议"][record.severity - 1] }}
                    </a-tag>
                  </template>

                  <template v-else-if="column.key === 'status'">
                    <a-tag :color="getStatusColor(record.status)">
                      {{ { resolved: "已解决", active: "激活", closed: "已关闭" }[record.status] }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'action'">
                    <!-- 根据is_lock状态控制编辑按钮 -->
                    <a-button type="link" @click="handleEdit(record)" :disabled="record.is_locked"
                      :class="{ 'text-gray-400 cursor-not-allowed': record.is_locked }">
                      {{ record.is_locked ? '已锁定' : '编辑' }}
                    </a-button>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
          <!-- 分页部分保持不变 -->
        </div>

        <div v-if="selectedRowKeys.length > 0" class="mb-4" style="margin-top: 10px">
          <div class="flex gap-2">
            <a-button type="primary" @click="showAssignModal"> 分配选中项（{{ selectedRowKeys.length }}项） </a-button>

            <!-- 锁定/解锁按钮，根据选中项状态动态显示 -->
            <a-button v-if="showLockButton" type="primary" :danger="isLockAction" @click="handleLockItems"
              :disabled="lockLoading">
              <template #icon>
                <lock-outlined v-if="isLockAction" />
                <unlock-outlined v-else />
              </template>
              {{ lockLoading ? '处理中...' : (isLockAction ? '锁定选中项' : '解锁选中项') }}
            </a-button>
          </div>
        </div>

        <!-- 多选分配 -->
        <a-modal v-model:visible="assignVisible" title="分配所属特性" @ok="handleAssign" @cancel="handleAssignClose"
          :focusTriggerAfterClose="false" width="600px" :body-style="{ padding: '24px' }">
          <div class="space-y-6">
            <!-- 所属目录 -->
            <div class="flex items-center gap-4 group-form-item">
              <span class="w-20 text-right text-gray-600">所属目录</span>
              <a-cascader v-model:value="editForm.feature" :options="categoryOptions" placeholder="请选择所属目录"
                class="flex-1" :change-on-select="false" :field-names="{ label: 'module_name', value: 'id' }"
                :show-search="{ matchInputWidth: false }" @change="handleFeatureChange"
                :class="{ '!border-red-500': tagValue !== 1 && !editForm.feature?.length }" />
            </div>

            <!-- 分配人员 -->
            <div class="flex items-center gap-4 group-form-item">
              <span class="w-20 text-right text-gray-600">分配人员</span>
              <a-input v-model:value="assignedPerson" placeholder="自动关联责任人" class="flex-1" allow-clear
                :class="{ '!border-red-500': tagValue !== -1 && !assignedPerson.value }" />
            </div>

            <!-- 分配类型 -->
            <div class="flex items-center gap-4 group-form-item">
              <span class="w-20 text-right text-gray-600 flex items-center gap-1">
                分配类型
                <a-tooltip placement="top">
                  <template #title>
                    <div class="max-w-[260px] text-sm leading-6">
                      <div>所属目录不变：该状态主要是用于修改分析人员，在该状态下提交后，只分析人员的修改内容生效</div>
                      <div>默认状态：该状态默认上方填写的两个值都会修改生效</div>
                      <div>分析人员不变：该状态主要用于修改所属目录，在该状态下提交后，只所属目录的修改内容生效</div>
                    </div>
                  </template>
                  <question-circle-outlined class="!text-gray-400 hover:!text-blue-500 cursor-help text-base" />
                </a-tooltip>
              </span>
              <div class="flex-1 flex items-center gap-4">
                <div class="relative w-48 bg-gray-50 rounded-lg p-2 shadow-inner">
                  <a-slider v-model:value="tagValue" :min="-1" :max="1" :step="1" :tooltip-visible="false" class="!m-0"
                    :track-style="{
                      backgroundColor: tagColor,
                      height: '6px',
                      borderRadius: '3px',
                    }" :rail-style="{
                      backgroundColor: '#f3f4f6',
                      height: '6px',
                    }" />
                </div>
                <a-tag :color="tagColor" class="!min-w-[100px] text-center shadow-sm py-1.5 text-sm font-medium">
                  <bulb-outlined class="mr-1" />
                  {{ tagLabel }}
                </a-tag>
              </div>
            </div>
          </div>
        </a-modal>

        <!-- 分页 -->
        <div
          class="w-full px-2 sm:px-4 py-2 sm:py-3 flex flex-col sm:flex-row items-center gap-2 sm:gap-0 border-t border-gray-200">
          <div class="flex items-center gap-4 w-full sm:justify-end">
            <!-- 添加 sm:justify-end 实现右对齐 -->
            <a-pagination v-model:current="pagination.current" :total="pagination.total"
              :page-size="pagination.pageSize" show-quick-jumper show-size-changer @change="handleTableChange"
              :show-total="(total) => `共 ${total} 条`" class="!w-full sm:!w-auto" />
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <a-modal v-model:visible="editVisible" title="缺陷分析" width="800px" :footer="null" @cancel="handleCancel">
      <div class="relative max-h-[70vh] overflow-y-auto pr-4">
        <div class="space-y-8">
          <!-- 第一阶段表单 -->
          <div class="space-y-6">
            <div class="sticky top-0 bg-white z-10 pb-4 flex justify-between items-center border-b">
              <div class="text-lg font-medium">基本信息</div>
              <a-button type="primary" @click="handleSaveStage(1)" :disabled="!validateStage(1)">
                保存基本信息
              </a-button>
            </div>
            <div class="grid grid-cols-2 gap-6">
              <div class="space-y-2">
                <label class="text-sm text-gray-600"> 涉及设备形态 </label>
                <a-input v-model:value="editForm.device_form" placeholder="请输入涉及设备形态" />
              </div>
              <div class="space-y-2">
                <label class="text-sm text-gray-600 flex items-center">
                  是否分析
                  <span class="text-red-500 ml-1">*</span>
                </label>
                <a-select v-model:value="editForm.is_analyzed" class="w-full" placeholder="请选择是否需要分析">
                  <a-select-option value="可分析">可分析</a-select-option>
                  <a-select-option value="待分析">待分析</a-select-option>
                  <a-select-option value="不分析-转需求">不分析-转需求</a-select-option>
                  <a-select-option value="不分析-重复问题">不分析-重复问题</a-select-option>
                  <a-select-option value="不分析-已知遗留问题">不分析-已知遗留问题</a-select-option>
                  <a-select-option value="不分析-个体硬件">不分析-个体硬件</a-select-option>
                  <a-select-option value="不分析-非问题">不分析-非问题</a-select-option>
                  <a-select-option value="不分析-不涉及">不分析-不涉及</a-select-option>
                </a-select>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-6">
              <div class="space-y-2">
                <label class="text-sm text-gray-600 flex items-center">
                  是否BUG
                  <span class="text-red-500 ml-1">*</span>
                </label>
                <a-select v-model:value="editForm.is_bug" class="w-full" placeholder="请选择是否为BUG">
                  <a-select-option value="是">是</a-select-option>
                  <a-select-option value="否">否</a-select-option>
                  <a-select-option value="待分析">待分析</a-select-option>
                </a-select>
              </div>
            </div>

            <div class="space-y-2">
              <label class="text-sm text-gray-600 flex items-center">
                问题根因
                <span class="text-red-500 ml-1">*</span>
              </label>
              <a-textarea v-model:value="editForm.root_cause" :rows="4" placeholder="请详细描述问题产生的具体原因" class="w-full" />
            </div>
          </div>

          <!-- 第二阶段表单 -->
          <div v-if="editForm.is_bug === '是'" class="space-y-6 transition-all duration-300 ease-in-out" :class="{
            'opacity-100 max-h-[2000px]': editForm.is_bug === '是',
            'opacity-0 max-h-0 overflow-hidden': editForm.is_bug !== '是',
          }">
            <div class="sticky top-0 bg-white z-10 pb-4 flex justify-between items-center border-b">
              <div class="text-lg font-medium">缺陷分析</div>
              <a-button type="primary" @click="handleSaveStage(2)" :disabled="!validateStage(2)">
                保存缺陷分析
              </a-button>
            </div>

            <div class="space-y-2">
              <label class="text-gray-600">BUG责任人：</label>
              <a-select v-model:value="editForm.bug_owner" class="w-full" placeholder="选择BUG责任人，不选默认为分析责任人！！"
                mode="multiple" allowClear show-search :filter-option="filterUserOption" option-filter-prop="label">
                <a-select-option v-for="user in userList" :key="user.user_name" :value="user.user_name"
                  :label="user.real_name">
                  {{ user.real_name }}
                </a-select-option>
              </a-select>
            </div>

            <div class="grid grid-cols-2 gap-6">
              <div class="space-y-2">
                <label class="text-sm text-gray-600 flex items-center">
                  <span>是否开发修改引入</span>
                  <span class="text-red-500 ml-1">*</span>
                </label>
                <a-select v-model:value="editForm.is_dev_introduced" class="w-full" placeholder="请选择是否开发修改引入">
                  <a-select-option value="是">是</a-select-option>
                  <a-select-option value="否">否</a-select-option>
                </a-select>
              </div>
              <div class="space-y-2">
                <label class="text-sm text-gray-600">缺陷引入的历史版本</label>
                <a-input v-model:value="editForm.history_version" placeholder="请输入修改引入的具体版本" />
              </div>
            </div>

            <div class="grid grid-cols-2 gap-6">
              <div class="space-y-2">
                <label class="text-sm text-gray-600 flex items-center">
                  <span>是否测试遗漏</span>
                  <span class="text-red-500 ml-1">*</span>
                </label>
                <a-select v-model:value="editForm.is_test_missed" class="w-full" placeholder="请选择是否测试遗漏">
                  <a-select-option value="是">是</a-select-option>
                  <a-select-option value="否">否</a-select-option>
                </a-select>
              </div>
              <div class="space-y-2">
                <label class="text-sm text-gray-600 flex items-center">
                  <span>测试遗漏阶段</span>
                  <span class="text-red-500 ml-1">*</span>
                </label>
                <a-select v-model:value="editForm.test_missed_phase" class="w-full" placeholder="请选择测试漏测的所属阶段">
                  <a-select-option value="测试策略">测试策略</a-select-option>
                  <a-select-option value="测试设计">测试设计</a-select-option>
                  <a-select-option value="测试执行">测试执行</a-select-option>
                </a-select>
              </div>
            </div>

            <div class="space-y-2">
              <label class="text-sm text-gray-600 flex items-center">
                <span>测试漏测分析</span>
                <span class="text-red-500 ml-1">*</span>
              </label>
              <a-textarea v-model:value="editForm.test_missed_analysis" :rows="4" placeholder="请详细描述具体的问题漏测原因分析"
                class="w-full" />
            </div>

            <div class="space-y-2">
              <label class="text-sm text-gray-600 flex items-center">
                <span>改进措施</span>
                <span class="text-red-500 ml-1">*</span>
              </label>
              <a-textarea v-model:value="editForm.improvement_measures" :rows="4" placeholder="请详细描述后续如何改进"
                class="w-full" />
            </div>
          </div>

          <!-- 第三阶段表单 -->
          <div v-if="editForm.is_bug === '是' && editForm.is_test_missed === '是'"
            class="space-y-6 transition-all duration-300 ease-in-out" :class="{
              'opacity-100 max-h-[2000px]': editForm.is_bug === '是' && editForm.is_test_missed === '是',
              'opacity-0 max-h-0 overflow-hidden': !(editForm.is_bug === '是' && editForm.is_test_missed === '是'),
            }">
            <div class="sticky top-0 bg-white z-10 pb-4 flex justify-between items-center border-b">
              <div class="text-lg font-medium">整改闭环</div>
              <a-button type="primary" @click="handleSaveStage(3)" :disabled="!validateStage(3)">
                保存整改信息
              </a-button>
            </div>

            <div class="grid grid-cols-2 gap-6">
              <div class="space-y-2">
                <label class="text-sm text-gray-600">是否用例已补充</label>
                <a-select v-model:value="editForm.is_case_added" class="w-full" placeholder="请选择是否需要补充用例">
                  <a-select-option value="是">是</a-select-option>
                  <a-select-option value="否">否</a-select-option>
                  <a-select-option value="不涉及">不涉及</a-select-option>
                </a-select>
              </div>

              <div class="space-y-2">
                <label class="text-sm text-gray-600">用例编号</label>
                <a-select v-model:value="editForm.case_number" mode="combobox" placeholder="请选择或输入用例编号" allowClear
                  :loading="caseLoading" class="w-full" show-search :filter-option="false" @popupScroll="handleScroll"
                  @search="handleSearchCaseNumber">
                  <a-select-option v-for="(caseNum, index) in caseOptions" :key="index" :value="caseNum">
                    {{ caseNum }}
                  </a-select-option>
                  <template v-if="loadingMore">
                    <a-spin size="small" class="block py-1 text-gray-500" />
                  </template>
                </a-select>
              </div>
            </div>

            <div class="space-y-2">
              <label class="text-sm text-gray-600">整改责任人</label>
              <a-select v-model:value="editForm.rectification_owner" class="w-full" placeholder="请选择整改责任人" allowClear
                show-search :filter-option="filterUserOption" option-filter-prop="label">
                <a-select-option v-for="user in userList" :key="user.user_name" :value="user.user_name"
                  :label="user.real_name">
                  {{ user.real_name }}
                </a-select-option>
              </a-select>
            </div>
          </div>
        </div>

        <!-- 底部按钮区域 -->
        <div class="flex justify-end gap-4 mt-8 pt-4 border-t">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleSave" :disabled="!validateCurrentForm()"> 保存全部 </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, watch, h, computed, nextTick } from "vue"
import type { Dayjs } from "dayjs"
import dayjs from "dayjs"
import { message } from "ant-design-vue"

// 从正确的路径导入API接口
import {
  getBugList,
  updateStatistics,
  getUserList,
  getBugCount,
  type BugItem,
  getProductCases,
  exportBugList,
  fetchFeatureTree,
  FeatureTreeNode,
  assignFeatures,
  getFeatureResponsible,
  lockBugItems, // 添加锁定API导入
} from "@/api/statistics"

// 在组件顶部添加
import { LockOutlined, UnlockOutlined, QuestionCircleOutlined, UserOutlined, BulbOutlined, FilterOutlined, SyncOutlined } from "@ant-design/icons-vue"

// 添加锁定相关状态
const lockLoading = ref(false) // 锁定操作的加载状态

const showAdvancedPanel = ref(false)
const userList = ref<{ real_name: string; user_name: string }[]>([])

const showAdvanced = ref(false)
const advancedFilters = ref({
  is_analyzed: undefined as string | undefined,
  analyst: undefined as string | undefined,
  bug_owner: undefined as string[] | undefined, // 新增多选字段
  feature: undefined as string | undefined,
  isBug: undefined as string | undefined,
  analysisStatus: undefined as string | undefined,
  rectificationStatus: undefined as string | undefined,
})
const loadingUsers = ref(false)

const selectedRowKeys = ref<string[]>([])
const assignVisible = ref(false)

const selectedAssignee = ref<string>("")

const caseOptions = ref<string[]>([])
const searchKeyword = ref("")
const currentPage = ref(1)
const totalPages = ref(1)
const loadingMore = ref(false)

const caseLoading = ref(false)
const hasCachedUsers = ref(false)

const tagValue = ref(0) // 默认状态设为0
const tagColor = computed(() => {
  switch (tagValue.value) {
    case -1:
      return "orange"
    case 0:
      return "gray"
    case 1:
      return "blue"
    default:
      return "gray"
  }
})
const tagLabel = computed(() => {
  switch (tagValue.value) {
    case -1:
      return "所属目录不变"
    case 0:
      return "默认状态"
    case 1:
      return "分配人员不变"
    default:
      return "未知状态"
  }
})
const toggleAdvancedPanel = () => {
  showAdvancedPanel.value = !showAdvancedPanel.value
}
const filterUserOption = (inputValue: string, option: any) => {
  return (
    option.label.toLowerCase().includes(inputValue.toLowerCase()) ||
    option.value.toLowerCase().includes(inputValue.toLowerCase())
  )
}

// 特性搜索过滤
const filterFeatureOption = (input, option) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 处理高级搜索
const handleAdvancedSearch = () => {
  showAdvanced.value = false
  // 同步高级筛选参数到主筛选条件
  filters.value = {
    ...filters.value,
    analyst: advancedFilters.value.analyst || "", // 新增分析师同步
    bug_owner: (advancedFilters.value.bug_owner || []).join(","),
    feature: advancedFilters.value.feature,
    is_analyzed: advancedFilters.value.is_analyzed,
    is_bug: advancedFilters.value.isBug,
    analysis_status: advancedFilters.value.analysisStatus,
    rectification_status: advancedFilters.value.rectificationStatus,
  }

  // 同时刷新表格和未复核数
  Promise.all([fetchTableData(), fetchBugCount()])
}

const getDefaultDateRange = () => {
  const end = dayjs()
  const start = end.subtract(1, "month")
  return [start, end]
}

const showAssignModal = () => {
  // 获取所有选中行的产品信息
  const selectedProducts = tableData.value
    .filter((item) => selectedRowKeys.value.includes(item.id))
    .map((item) => item.product) // 根据实际字段名调整

  // 空选校验
  if (selectedProducts.length === 0) {
    message.warning("请先选择要分配的数据")
    return
  }

  // 产品一致性校验
  const uniqueProducts = [...new Set(selectedProducts)]
  if (uniqueProducts.length > 1) {
    message.error("所选数据必须属于同一产品")
    return
  }

  // 设置当前产品并加载目录树
  const currentProduct = uniqueProducts[0] as string
  assignVisible.value = true
  console.log("currentProduct", currentProduct)
  const mapProductId = (id: number) => {
    if ([10, 11, 12].includes(id)) return 1
    if ([13, 14, 15].includes(id)) return 4
    if ([16, 18].includes(id)) return 2
    if (id === 41) return 3
    throw new Error("未知的产品ID")
  }
  if (!currentProduct) {
    message.error("无法获取产品信息")
    return
  }
  const selectedProduct = mapProductId(Number(currentProduct))
  loadFeatureTree(selectedProduct.toString())
}

const filters = ref<Filters>({
  analyst: "",
  product: [] as string[], // 改为 string[]
  severity: [],
  bug_status: "",
  dateRange: getDefaultDateRange() as [Dayjs, Dayjs],
  // 初始化高级筛选字段
  bug_owner: "",
  feature: "",
  is_bug: "",
  analysis_status: "",
  rectification_status: "",
  is_analyzed: "",
})

const pagination = ref<Pagination>({
  current: 1,
  pageSize: 10,
  total: 0,
})

const loading = ref(false)
const tableData = ref<BugItem[]>([])
const total = ref(0)

interface Filters {
  analyst: string
  product: string[] // 新增产品字段
  severity: string[]
  dateRange: [Dayjs, Dayjs]
  bug_status: string

  feature?: string
  bug_owner?: string
  is_bug?: string
  analysis_status?: string
  rectification_status?: string
  is_analyzed?: string
}
interface Pagination {
  current: number
  pageSize: number
  total: number
}
const assignedPerson = ref("")
interface EditForm {
  id: string // 新增ID字段
  // 第一阶段
  feature: string

  device_form: string
  is_analyzed: string
  is_bug: string // 修改为字符串类型
  root_cause: string
  // 第二阶段
  is_dev_introduced: string
  history_version: string
  bug_owner: string[] // 修改为字符串数组类型
  is_test_missed: string
  test_missed_phase: string
  test_missed_analysis: string
  improvement_measures: string
  // 第三阶段
  is_case_added: string
  case_number: string
  rectification_owner: string
  analysis_owner: string // 添加这行
}
const editVisible = ref(false)
const currentStep = ref(0)
const editForm = ref<EditForm>({
  id: "", // 初始化ID字段
  feature: "",
  device_form: "",
  is_analyzed: "",
  is_bug: "",
  bug_owner: [], // 修改为数组类型
  root_cause: "",
  is_dev_introduced: "",
  history_version: "",
  is_test_missed: "",
  test_missed_phase: "",
  test_missed_analysis: "",
  improvement_measures: "",
  is_case_added: "",
  case_number: "",
  rectification_owner: "",
  analysis_owner: "",
})

const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  },
}

const columns = [
  {
    title: "BUG编号",
    dataIndex: "id",
    key: "id",
    width: 80,
    resizable: true, // 添加可调整标志
  },
  {
    title: "BUG标题",
    dataIndex: "title",
    key: "title",
    width: 700,
    customRender: ({ text, record }) => ({
      children: h(
        "a",
        {
          href: `https://rdms-cd.das-security.cn/index.php?m=bug&f=view&bugID=${record.id}`,
          target: "_blank",
          class: "text-blue-600 hover:text-blue-800",
        },
        text
      ),
    }),
    resizable: true, // 添加可调整标志
  },
  {
    title: "严重程度",
    dataIndex: "severity",
    key: "severity",
    width: 80,
    resizable: true, // 添加可调整标志
    customRender: ({ text }) => {
      const map = {
        "1": "紧急",
        "2": "严重",
        "3": "一般",
        "4": "建议",
      }
      return map[text] || text
    },
  },
  {
    title: "BUG状态",
    dataIndex: "status",
    key: "status",
    width: 100,
    resizable: true, // 添加可调整标志
    customRender: ({ text }) => {
      const statusMap = {
        resolved: "已解决",
        active: "激活",
        closed: "已关闭",
      }
      return statusMap[text] || text
    },
  },
  {
    title: "所属BUG库",
    dataIndex: "product_name",
    key: "product_name",
    resizable: true, // 添加可调整标志
    width: 100,
    ellipsis: true, // 开启省略
    tooltip: true, // 开启悬浮提示
  },
  {
    title: "创建日期",
    dataIndex: "openeddate",
    key: "openeddate",
    resizable: true, // 添加可调整标志
    width: 100,
  },
  {
    title: "指派给谁",
    dataIndex: "assignedto",
    key: "assignedto",
    resizable: true, // 添加可调整标志
    width: 100,
  },
  {
    title: "问题关闭类",
    dataIndex: "closeType",
    resizable: true, // 添加可调整标志
    key: "closeType",
    width: 100,
  },
  {
    title: "BUG来源",
    dataIndex: "origin",
    resizable: true, // 添加可调整标志
    key: "souoriginrce",
    width: 100,
  },
  {
    title: "子状态",
    dataIndex: "subStatus",
    resizable: true, // 添加可调整标志
    key: "subStatus",
    width: 120,
  },
  {
    title: "所属特性",
    dataIndex: "feature",
    resizable: true, // 添加可调整标志
    key: "feature",
    fixed: "right",
    width: 80,
  },

  {
    title: "分析责任人",
    dataIndex: "analysis_owner",
    key: "analysis_owner",
    fixed: "right",
    width: 90,
  },
  {
    title: "BUG责任人",
    dataIndex: "bug_owner",
    key: "bug_owner",
    fixed: "right",
    width: 90,
  },
  {
    title: "分析状态",
    dataIndex: "is_analyzed",
    key: "is_analyzed",
    fixed: "right",
    width: 75,
  },
  {
    title: "是否现网BUG",
    dataIndex: "is_bug",
    key: "is_bug",
    fixed: "right",
    width: 80,
  },
  {
    title: "缺陷分析",
    dataIndex: "analysis_status",
    key: "analysis_status",
    fixed: "right",
    width: 75,
  },
  {
    title: "整改闭环",
    dataIndex: "rectification_status",
    key: "rectification_status",
    fixed: "right",
    width: 75,
  },
  // {
  //   title: "锁定状态",
  //   dataIndex: "is_locked",
  //   key: "is_locked",
  //   fixed: "right",
  //   width: 80,
  //   customRender: ({ text }) => {
  //     return text ? h('span', { style: { color: 'red' } }, [
  //       h(LockOutlined),
  //       ' 已锁定'
  //     ]) : '未锁定'
  //   }
  // },
  {
    title: "操作",
    key: "action",
    fixed: "right",
    width: 60,
    customRender: ({ record }) => {
      return record.is_locked ?
        h('span', { style: { color: 'gray' } }, '已锁定') :
        h('a-button', {
          type: 'link',
          onClick: () => handleEdit(record)
        }, '编辑')
    }
  },
]

// 修改自定义指令实现
const vColumnResizable = {
  mounted(el) {
    // 等待 DOM 更新完成
    setTimeout(() => {
      const table = el.querySelector(".ant-table-thead")
      const cells = table.querySelectorAll("th")
      const ths = Array.from(cells)

      ths.forEach((th, index) => {
        if (columns[index]?.resizable) {
          const handle = document.createElement("div")
          handle.className = "column-resize-handle"
          th.appendChild(handle)

          let startX = 0
          let startWidth = 0

          const handleMouseDown = (e) => {
            startX = e.pageX
            startWidth = th.offsetWidth

            document.addEventListener("mousemove", handleMouseMove)
            document.addEventListener("mouseup", handleMouseUp)
          }

          const handleMouseMove = (e) => {
            const width = startWidth + (e.pageX - startX)
            if (width >= 50) {
              // 最小宽度限制
              th.style.width = `${width}px`
              columns[index].width = width
            }
          }

          const handleMouseUp = () => {
            document.removeEventListener("mousemove", handleMouseMove)
            document.removeEventListener("mouseup", handleMouseUp)
          }

          handle.addEventListener("mousedown", handleMouseDown)
        }
      })
    }, 0)
  },
}

const fetchCaseNumbers = async (product: string, reset = false) => {
  try {
    if (reset) {
      currentPage.value = 1
      caseOptions.value = []
    }

    const response = await getProductCases(product, {
      page: currentPage.value,
      pageSize: 50,
      search: searchKeyword.value,
    })

    // 根据新接口结构调整解构方式
    caseOptions.value = [
      ...caseOptions.value,
      ...response.data.data.map((item: { id_title: string }) => item.id_title), // 提取id_title字段
    ]

    // 使用接口返回的分页参数
    totalPages.value = response.data.total_pages

    // 更新当前页码（根据接口返回的current_page）
    if (currentPage.value < response.data.total_pages) {
      currentPage.value = response.data.current_page + 1 // 或保持原逻辑 currentPage.value++
    }
  } catch (error) {
    message.error("获取用例编号失败")
  } finally {
    loadingMore.value = false
  }
}
// 添加滚动事件处理
const handleScroll = (e: Event) => {
  const select = e.target as HTMLDivElement
  const { scrollTop, scrollHeight, clientHeight } = select
  const threshold = 50 // 距离底部50px触发加载

  if (
    !loadingMore.value &&
    scrollHeight - (scrollTop + clientHeight) < threshold &&
    currentPage.value < totalPages.value
  ) {
    loadingMore.value = true
    fetchCaseNumbers(editForm.value.case_number)
  }
}

// 添加防抖函数实现
const debounce = (fn: Function, delay: number) => {
  let timeoutId: number
  return (...args: any[]) => {
    clearTimeout(timeoutId)
    timeoutId = window.setTimeout(() => fn.apply(this, args), delay)
  }
}

// 搜索处理（带防抖）
const handleSearchCaseNumber = debounce((value: string) => {
  searchKeyword.value = value
  fetchCaseNumbers(editForm.value.case_number, true)
}, 500)

// 新增editVisible监听器
watch([assignVisible, editVisible], async (visible) => {
  if (visible && !hasCachedUsers.value) {
    try {
      loadingUsers.value = true
      const response = await getUserList()
      const resData = response.data
      if (resData?.results) {
        userList.value = resData.results.map((user) => ({
          real_name: user.real_name,
          user_name: user.user_name,
        }))
        hasCachedUsers.value = true
      }
    } catch (error) {
      console.error("获取人员列表失败:", error)
      message.error("获取人员列表失败: " + error.message)
    } finally {
      loadingUsers.value = false
    }
  }
})

// 添加导出方法
const handleExport = async () => {
  try {
    const params = {
      analyst: filters.value.analyst || undefined,
      severity: filters.value.severity || undefined,
      product: filters.value.product || undefined,
      startDate: filters.value.dateRange?.[0]?.format("YYYY-MM-DD") || undefined,
      endDate: filters.value.dateRange?.[1]?.format("YYYY-MM-DD") || undefined,
    }

    const response = await exportBugList(params)
    console.log("data", response.data)

    // 创建Blob对象并下载
    const blob = new Blob([response.data], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.href = url
    link.download = `缺陷列表_${dayjs().format("YYYYMMDDHHmmss")}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error("导出失败:", error)
    message.error("导出失败，请重试")
  }
}

const categoryOptions = ref<FeatureTreeNode[]>([])

const convertFeatureNode = (node: FeatureTreeNode) => ({
  ...node,
  value: node.id,
  label: node.module_name,
  // 当没有子节点时设为undefined，允许选择父节点
  children: node.children && node.children.length > 0 ? node.children.map(convertFeatureNode) : undefined,
})

// 修改加载目录树的方法
const loadFeatureTree = async (productId?: string) => {
  try {
    // 添加空参数处理
    const params = productId ? { product_id: productId } : {}
    const res = await fetchFeatureTree(params)

    categoryOptions.value = Array.isArray(res.data.data) ? res.data.data.map(convertFeatureNode) : []
  } catch (error) {
    console.error("加载目录树失败:", error)
    message.error("目录加载失败")
    categoryOptions.value = []
  }
}

// 获取表格数据
const fetchTableData = async () => {
  loading.value = true
  try {
    const params = {
      analyst: Array.isArray(advancedFilters.value.analyst) ? advancedFilters.value.analyst.join(',') : advancedFilters.value.analyst,
      severity: filters.value.severity.length ? filters.value.severity.join(',') : undefined,
      product: filters.value.product && filters.value.product.length > 0 ? filters.value.product.join(',') : undefined, // 多选用逗号分隔
     // product: filters.value.product && filters.value.product.length > 0 ? filters.value.product : undefined, // 直接传数组
      bug_status: filters.value.bug_status || undefined,
      //高级筛选
      is_bug: filters.value.is_bug || undefined,
      analysis_status: filters.value.analysis_status || undefined,
      is_analyzed: Array.isArray(advancedFilters.value.is_analyzed) ? advancedFilters.value.is_analyzed.join(',') : advancedFilters.value.analysisStatus,
      rectification_status: filters.value.rectification_status || undefined,
      feature: Array.isArray(filters.value.feature)
        ? filters.value.feature.slice(-1)[0]
        : filters.value.feature || undefined,
      bug_owner: filters.value.bug_owner || undefined,
      startDate: filters.value.dateRange ? filters.value.dateRange[0].format("YYYY-MM-DD") : undefined,
      endDate: filters.value.dateRange ? filters.value.dateRange[1].format("YYYY-MM-DD") : undefined,
      pageSize: pagination.value.pageSize,
      current: pagination.value.current,
    }

    console.log("params", params)

    const res = await getBugList(params)
    console.log(res)
    if (res.data.status === 200) {
      tableData.value = res.data.data
      pagination.value.total = res.data.total
    } else {
      message.error(res.message || "获取数据失败")
    }
  } catch (error) {
    console.error("获取数据失败:", error)
    message.error("获取数据失败")
  } finally {
    loading.value = false
  }
}

const bugStatusArr = computed({
  get: () => filters.value.bug_status ? filters.value.bug_status.split(',') : [],
  set: (value) => {
    filters.value.bug_status = value.join(',')
  }
})

// 修改搜索方法
const handleSearch = async () => {
  pagination.value.current = 1 // 重置页码
  try {
    await Promise.all([
      fetchTableData(),
      fetchBugCount(), // 新增未复核数请求
    ])
  } catch (error) {
    console.error("查询请求失败:", error)
    message.error("数据查询失败")
  }
}

// 修改清空方法
const handleClear = () => {
  filters.value = {
    analyst: "",
    severity: [], // 保持 string[]
    product: [], // 清空为 []
    dateRange: getDefaultDateRange() as [Dayjs, Dayjs],
    // 重置高级筛选字段
    bug_owner: "",
    feature: "",
    is_bug: "",
    analysis_status: "",
    rectification_status: "",
  }
  // 同时清空高级筛选弹窗内的临时值
  advancedFilters.value = {
    is_analyzed: undefined,
    analyst: undefined,
    bug_owner: undefined,
    feature: undefined,
    isBug: undefined,
    analysisStatus: undefined,
    rectificationStatus: undefined,
  }
  pagination.value.current = 1
  Promise.all([fetchTableData(), fetchBugCount()])
}
// 刷新数据
const refreshData = async () => {
  try {
    // 同时刷新表格数据和未复核条数
    await Promise.all([fetchTableData(), fetchBugCount()])
  } catch (error) {
    console.error("刷新失败:", error)
    message.error("数据刷新失败")
  }
}
onMounted(async () => {
  await loadFeatureTree()
})

onMounted(() => {
  // refreshData()
  const initUserList = async () => {
    try {
      loadingUsers.value = true
      const response = await getUserList()
      const resData = response.data
      if (resData?.results) {
        userList.value = resData.results.map((user) => ({
          real_name: user.real_name,
          user_name: user.user_name,
        }))
        hasCachedUsers.value = true
      }
    } catch (error) {
      console.error("初始化用户列表失败:", error)
    } finally {
      loadingUsers.value = false
    }
  }
  Promise.all([
    refreshData(),
    initUserList(), // 新增用户列表初始化
  ])
})

const handleFeatureChange = async (value: string[]) => {
  try {
    const featureId = value[value.length - 1]
    // 从选中行获取产品ID
    const rawProductId = tableData.value.find((item) => selectedRowKeys.value.includes(item.id))?.product

    const mapProductId = (id: number) => {
      if ([10, 11, 12].includes(id)) return 1
      if ([13, 14, 15].includes(id)) return 4
      if ([16, 18].includes(id)) return 2
      if (id === 41) return 3
      throw new Error("未知的产品ID")
    }

    if (!rawProductId) {
      message.error("无法获取产品信息")
      return
    }

    const selectedProduct = mapProductId(Number(rawProductId))

    const response = await getFeatureResponsible(selectedProduct, featureId)

    console.log("response", response.data.data.person_responsible)

    // 修改为直接处理字符串
    const responsible = response?.data?.data?.person_responsible || ""
    if (responsible) {
      assignedPerson.value = responsible
      selectedAssignee.value = responsible // 根据实际情况可能需要调整字段映射
    } else {
      assignedPerson.value = "暂无责任人"
      selectedAssignee.value = ""
    }
  } catch (error) {
    console.error("获取责任人失败:", error)
    message.error("责任人信息获取失败")
  }
}

// 监听分页变化
const handleTableChange = (page: number, pageSize: number) => {
  pagination.value.current = page
  pagination.value.pageSize = pageSize
  fetchTableData()
}
const fetchBugCount = async () => {
  try {
    const params = {
      analyst: filters.value.analyst || undefined,
      product: filters.value.product || undefined,
      severity: filters.value.severity || undefined,
      bug_status: filters.value.bug_status || undefined,  // 确保添加这行
      // 添加高级筛选参数
      is_bug: filters.value.is_bug,
      analysis_status: filters.value.analysis_status,
      rectification_status: filters.value.rectification_status,
      // 日期参数保持不变
      startDate: filters.value.dateRange?.[0]?.format("YYYY-MM-DD") || undefined,
      endDate: filters.value.dateRange?.[1]?.format("YYYY-MM-DD") || undefined,
      _t: Date.now(),
    }

    const res = await getBugCount(params)
    if (res.status === 200) {
      total.value = res.data.data.unreviewed_bugs || 0
    }
  } catch (error) {
    console.error("获取未复核条数失败:", error)
  }
}

const getSeverityColor = (severity: number) => {
  const colorMap: Record<number, string> = {
    1: "orange", // 紧急改为橙色
    2: "red", // 严重保持红色
    3: "volcano", // 一般保持火山色
    4: "blue", // 建议改为蓝色
  }
  return colorMap[severity] || "geekblue"
}
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    resolved: "green", // 已解决
    active: "orange", // 激活
    closed: "gray", // 已关闭
  }
  return colorMap[status] || "blue"
}
const handleEdit = (item: any) => {
  // 检查记录是否被锁定
  if (item.is_locked) {
    message.warning('该记录已被锁定，无法编辑')
    return
  }

  console.log("item", item)
  editVisible.value = true
  currentStep.value = 0
  editForm.value.id = item.id
  fetchCaseNumbers(item.product_name)
  editForm.value = {
    id: editForm.value.id,
    feature: item.feature || item.feature || "", // 兼容不同字段名
    device_form: item.device_form || item.device_form || "",
    is_analyzed: item.is_analyzed || "", // 直接使用字符串值
    is_bug: item.is_bug || "",
    root_cause: item.root_cause || "",
    analysis_owner: item.analysis_owner || "",

    is_dev_introduced: item.is_dev_introduced || "", // 确保字段名与接口一致
    history_version: item.history_version || "",
    is_test_missed: item.is_test_missed || "",
    bug_owner: item.bug_owner ? item.bug_owner.split(",") : [], // 字符串转数组
    test_missed_phase: item.test_missed_phase || "",
    test_missed_analysis: item.test_missed_analysis || "",
    improvement_measures: item.improvement_measures || "",

    is_case_added: item.is_case_added || "",
    case_number: item.case_number || "",
    rectification_owner: item.rectification_owner || "",
  }
}
const handleAdvanced = () => {
  showAdvanced.value = true
  loadFeatureTree()
}
// 替换原有的validateStep方法
const validateCurrentForm = () => {
  return validateStage(1) && validateStage(2) && validateStage(3)
}

// 新增递归查找节点方法
const findTreeNode = (nodes: FeatureTreeNode[], targetId: string): FeatureTreeNode | null => {
  for (const node of nodes) {
    if (node.id === targetId) return node
    if (node.children) {
      const found = findTreeNode(node.children, targetId)
      if (found) return found
    }
  }
  return null
}

// 在现有方法中添加分配处理
const handleAssign = async () => {
  try {
    // 构造请求参数
    const params = selectedRowKeys.value.map((id) => {
      const row = tableData.value.find((item) => item.id === id)
      if (!row) throw new Error("找不到对应数据")

      // 获取最后一级特征值
      const lastFeatureId = editForm.value.feature?.slice(-1)[0] || ""
      const lastFeatureNode = findTreeNode(categoryOptions.value, lastFeatureId)
      return {
        bug_id: id,
        feature: lastFeatureNode?.module_name || "", // 仅取最后一级名称
        analysis_owner: assignedPerson.value,
        tag: tagValue.value,
        is_admin: true,
      }
    })

    // 验证必须字段
    // if (!editForm.value.feature?.length) {
    //   message.error("请先选择所属目录")
    //   return
    // }
    // if (!assignedPerson.value) {
    //   message.error("请选择分析责任人")
    //   return
    // }

    console.log("params", params)

    // 调用API方法
    const response = await assignFeatures(params)
    console.log("完整响应:", response.data)
    if (response.data.status === "200") {
      message.success(`成功分配 ${params.length} 项`)
      await fetchTableData()
      selectedRowKeys.value = []
      assignVisible.value = false
    }
  } finally {
    loading.value = false
    editForm.value.feature = ""
    assignedPerson.value = "" // 清空分配人员
    categoryOptions.value = [] // 清空目录缓存
  }
}

const handleAssignClose = () => {
  editForm.value.feature = ""
  assignedPerson.value = "" // 清空分配人员
  categoryOptions.value = [] // 清空目录缓存
}

const validateStage = (stage: number) => {
  switch (stage) {
    case 1:
      return !!editForm.value.is_analyzed && !!editForm.value.is_bug && !!editForm.value.root_cause?.trim()
    case 2:
      if (editForm.value.is_bug !== "是") return true
      return (
        !!editForm.value.is_dev_introduced &&
        !!editForm.value.is_test_missed &&
        !!editForm.value.test_missed_phase &&
        !!editForm.value.test_missed_analysis?.trim() &&
        !!editForm.value.improvement_measures?.trim()
      )
    case 3:
      if (editForm.value.is_bug !== "是" || editForm.value.is_test_missed !== "是") return true
      return (
        !!editForm.value.is_case_added &&
        (editForm.value.is_case_added === "是" ? !!editForm.value.case_number?.trim() : true) &&
        !!editForm.value.rectification_owner?.trim()
      )
    default:
      return false
  }
}
const handleAdvancedCancel = () => {
  showAdvanced.value = false
  // 清空所有高级筛选值
  advancedFilters.value = {
    analyst: undefined,
    bug_owner: undefined,
    feature: undefined,
    isBug: undefined,
    analysisStatus: undefined,
    rectificationStatus: undefined,
  }
  categoryOptions.value = [] // 清空目录缓存
}

const handleSaveStage = async (stage: number) => {
  try {
    if (!validateStage(stage)) {
      message.warning("请填写必填项")
      return
    }

    loading.value = true
    const { id, bug_owner, ...restForm } = editForm.value
    const params = {
      id,
      ...restForm,
      bug_owner: (Array.isArray(bug_owner) ? bug_owner.join(",") : bug_owner) || restForm.analysis_owner, // 当bug_owner为空时使用analysis_owner
    }

    // 根据阶段设置状态
    if (stage >= 1 && params.is_bug === "是") {
      params["analysis_status"] = "已分析"
    }
    if (stage >= 2 && params.is_test_missed === "是") {
      params["rectification_status"] = "已分析"
    }

    const { status } = await updateStatistics(params)
    if (status === 200) {
      message.success(`第${stage}阶段保存成功`)
      if (stage === 3) {
        editVisible.value = false
      }
      await Promise.all([fetchTableData(), fetchBugCount()])
    }
  } catch (error) {
    console.error("保存失败:", error)
    message.error("保存失败请重试")
  } finally {
    loading.value = false
  }
}

// 编辑框保存方法
const handleSave = () => handleSaveStage(3)

const handlePrev = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const handleCancel = () => {
  editVisible.value = false
}

// 添加计算属性，判断是否显示锁定/解锁按钮
const showLockButton = computed(() => {
  if (selectedRowKeys.value.length === 0) return false

  // 获取选中行的数据
  const selectedRows = tableData.value.filter(item => selectedRowKeys.value.includes(item.id))

  // 检查是否所有选中项都是锁定状态或都是未锁定状态
  const lockedCount = selectedRows.filter(item => item.is_locked).length

  // 全部锁定或全部未锁定时才显示按钮
  return lockedCount === 0 || lockedCount === selectedRows.length
})

// 添加计算属性，判断是执行锁定还是解锁操作
const isLockAction = computed(() => {
  if (selectedRowKeys.value.length === 0) return true

  // 获取选中行的数据
  const selectedRows = tableData.value.filter(item => selectedRowKeys.value.includes(item.id))

  // 检查是否有锁定的项
  const lockedCount = selectedRows.filter(item => item.is_locked).length

  // 如果有锁定的项，且数量等于选中项总数，则执行解锁操作
  return lockedCount === 0
})

// 修改锁定/解锁选中项的方法
const handleLockItems = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要操作的数据')
    return
  }

  try {
    lockLoading.value = true

    // 调用锁定/解锁API
    const response = await lockBugItems({
      bug_ids: selectedRowKeys.value,
      locked: isLockAction.value // true表示锁定，false表示解锁
    })

    // 修改这里的响应处理逻辑，兼容不同的响应格式
    if (response?.code === 200 || response?.data?.code === 200 || response?.status === 200) {
      message.success(`成功${isLockAction.value ? '锁定' : '解锁'} ${selectedRowKeys.value.length} 项`)
      // 刷新表格数据
      await fetchTableData()
      // 清空选中项
      selectedRowKeys.value = []
    } else {
      // 获取错误信息
      const errorMsg = response?.message || response?.data?.message || '操作失败'
      message.error(`${isLockAction.value ? '锁定' : '解锁'}操作失败: ${errorMsg}`)
    }
  } catch (error) {
    console.error(`${isLockAction.value ? '锁定' : '解锁'}操作失败:`, error)

    // 处理403权限错误，显示后端返回的具体错误信息
    if (error.response && error.response.status === 403) {
      message.error(error.response.data.message || '权限不足，无法执行此操作')
    } else {
      message.error(`${isLockAction.value ? '锁定' : '解锁'}操作失败: ` + (error.message || '未知错误'))
    }
  } finally {
    lockLoading.value = false
  }
}

const productIdMap: Record<string, string> = {
  "16": "TGFW",
  "17": "WAF",
  "18": "USM V9",
  // 可补充其他映射
}

onMounted(async () => {
  let localProduct = localStorage.getItem("product")
  let firstProduct = ""
  if (localProduct) {
    try {
      let arr: string[] = []
      if (localProduct.startsWith("[") || localProduct.startsWith("{")) {
        arr = JSON.parse(localProduct)
      } else if (localProduct.includes(",")) {
        arr = localProduct.split(",")
      } else {
        arr = [localProduct]
      }
      firstProduct = arr[0]?.trim() || ""
    } catch (e) {
      firstProduct = ""
    }
  }
  const validProducts = ["USM V8", "USM V9", "TGFW", "USM 云安全", "WAF"]
  if (firstProduct && validProducts.includes(firstProduct)) {
    filters.value.product = [firstProduct]
    await nextTick()
    handleSearch()
  } else {
    filters.value.product = ["TGFW"]
    handleSearch()
  }
})

const hasAutoSearched = ref(false)
watch(
  () => filters.value.product,
  (val) => {
    if (!hasAutoSearched.value && val && val.length > 0) {
      handleSearch()
      hasAutoSearched.value = true
    }
  },
  { immediate: true }
)

</script>
<style scoped>
/* 添加容器样式，确保内容不会与左侧菜单重叠 */
.itr-report-container {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

/* 表格容器样式 */
.table-container {
  max-width: 100%;
  overflow-x: auto;
  /* 确保滚动条不会与左侧菜单重叠 */
  margin-right: 0;
  padding-right: 0;
}

/* 表格包装器样式 */
.table-wrapper {
  min-width: 100%;
  max-width: 100%;
  overflow-x: auto;
  /* 确保表格内容不会超出容器边界 */
  position: relative;
}

/* 添加或确认以下样式存在 */
.custom-table :deep(.ant-table-thead th) {
  position: relative;
}

.custom-table :deep(.column-resize-handle) {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 5px;
  background-color: transparent;
  cursor: col-resize;
  z-index: 1;
}

.custom-table :deep(.column-resize-handle:hover),
.custom-table :deep(.column-resize-handle.active) {
  background-color: #1890ff;
}

.custom-table :deep(.ant-table-thead th) {
  position: relative;
  padding: 16px 8px;
  background: #fafafa;
  transition: background 0.3s ease;
  user-select: none;
}

/* 确保表格容器可以水平滚动 */
.custom-table :deep(.ant-table-container) {
  overflow-x: auto;
  max-width: 100%;
}

/* 防止文本溢出 */
.custom-table :deep(.ant-table-cell) {
  overflow: hidden;
  text-overflow: ellipsis;
}

.container {
  max-width: 100%;
  margin: 0 auto;
  min-width: 1rem;
}

.custom-table :deep(.resizable-handle) {
  position: absolute;
  right: -5px;
  top: 0;
  bottom: 0;
  width: 10px;
  cursor: col-resize;
  z-index: 1;
  background: transparent;
}

.custom-table :deep(.resizable-handle:hover) {
  background: #1890ff;
}

.custom-table :deep(.bug-title-column) {
  min-width: 120px !important;
  max-width: 120px !important;
  width: 120px !important;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.ant-modal-body {
  padding: 20px 24px !important;
}

.ant-table-wrapper {
  margin: 0;
}

.ant-pagination-options {
  margin-left: 16px;
}

.custom-table :deep(.ant-table) {
  min-width: auto;
}

.custom-table :deep(.ant-table-cell) {
  padding: 12px 8px !important;
}

.custom-table :deep(.ant-table-container) {
  @media (min-width: 1536px) {
    width: 100% !important;
  }
}

.custom-table :deep(.ant-table-body) {
  overflow-x: auto !important;
}

.custom-table :deep(.ant-table-fixed-left) {
  box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.15);
}

.custom-table :deep(.bug-title-column) {
  min-width: 20vw;
  /* 视口宽度的20% */
  max-width: 30vw;
  /* 视口宽度的40% */
  width: auto !important;
  /* 允许自动扩展 */
}

/* 自定义滚动条样式 */
.overflow-x-auto::-webkit-scrollbar {
  height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.custom-table :deep(.ant-table-thead th) {
  position: relative;
  user-select: none;
}

.custom-table :deep(.resizable-handle:hover) {
  background-color: #1890ff;
}

.custom-table :deep(.resizable-handle::after) {
  content: "";
  position: absolute;
  right: 3px;
  top: 50%;
  height: 16px;
  width: 2px;
  background: #ddd;
  transform: translateY(-50%);
}

.custom-table :deep(.ant-table-container) {
  position: relative;
}

.custom-table :deep(.resizable-handle) {
  position: absolute;
  right: -5px;
  top: 0;
  bottom: 0;
  width: 10px;
  cursor: col-resize;
  z-index: 1;
  background: #1890ff;
  opacity: 0.3;
  transition: opacity 0.3s;
}

.custom-table :deep(.resizable-handle:hover) {
  opacity: 1;
}

.modal-content {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
  max-height: 70vh;
  overflow-y: auto;
}

.modal-content::-webkit-scrollbar {
  width: 6px;
}

.modal-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 确保sticky标题始终可见 */
.sticky {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 响应式处理，确保在不同屏幕尺寸下正确处理滚动 */
@media (max-width: 1024px) {
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .itr-report-container {
    overflow-x: auto;
  }
}

/* 确保表格在父容器内正确显示 */
.custom-table :deep(.ant-table-wrapper) {
  width: 100%;
  overflow: hidden;
}

.custom-table :deep(.ant-table) {
  width: 100%;
  min-width: 100%;
}

/* 防止表格内容溢出父容器 */
.custom-table :deep(.ant-table-content) {
  overflow-x: auto;
  max-width: 100%;
}
</style>
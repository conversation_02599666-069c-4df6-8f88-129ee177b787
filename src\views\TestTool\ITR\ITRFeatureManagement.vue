<!-- The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work. -->
<template>
  <div>
    <div class="min-h-screen bg-gray-50 w-full">
        <!-- 搜索区域 -->
        <div class="bg-white rounded-lg shadow-sm p-2 sm:p-4 mb-2 sm:mb-4">
          <div class="flex flex-col sm:flex-row flex-wrap items-start sm:items-center gap-2 sm:gap-4">
            <div class="w-full sm:w-auto flex items-center gap-1 flex-shrink-0">
              <span class="text-gray-600 whitespace-nowrap min-w-[1.5rem]">产品：</span>
              <a-select v-model:value="searchForm.product" class="min-w-[100px]" placeholder="请输入产品..." allowClear>
                <a-select-option value="TGFW">TGFW</a-select-option>
                <a-select-option value="USM V8">USM V8</a-select-option>
                <a-select-option value="USM V9">USM V9</a-select-option>
                <a-select-option value="USM 云安全">USM 云安全</a-select-option>
                <a-select-option value="WAF">WAF</a-select-option>
              </a-select>
            </div>
            <div class="w-full sm:w-auto flex items-center gap-1 flex-shrink-0">
              <span class="text-gray-600 whitespace-nowrap min-w-[1.5rem]">特性名称：</span>
              <a-input v-model:value="searchForm.module_name" placeholder="特性名称" class="min-w-[100px] !rounded-button" />
            </div>
            <div class="w-full sm:w-auto flex items-center gap-1 flex-shrink-0">
              <span class="text-gray-600 whitespace-nowrap min-w-[1.5rem]">层级：</span>
              <a-input v-model:value="searchForm.level" placeholder="层级" class="min-w-[100px] !rounded-button" />
            </div>
            <div class="w-full sm:w-auto flex items-center gap-1 flex-shrink-0">
              <span class="text-gray-600 whitespace-nowrap min-w-[1.5rem]">责任组：</span>
              <a-input v-model:value="searchForm.group_responsible" placeholder="责任组" class="min-w-[100px] !rounded-button" />
            </div>
            <div class="w-full sm:w-auto flex items-center gap-1 flex-shrink-0">
              <span class="text-gray-600 whitespace-nowrap min-w-[1.5rem]">责任人：</span>
              <a-input v-model:value="searchForm.person_responsible" placeholder="责任人" class="min-w-[100px] !rounded-button" />
            </div>
            <div class="w-full sm:w-auto flex gap-1 mt-2 sm:mt-0 flex-shrink-0 ml-auto">
              <a-button type="primary" class="flex-1 sm:flex-none !rounded-button" @click="handleSearch">查询</a-button>
              <a-button class="flex-1 sm:flex-none !rounded-button" @click="handleReset">重置</a-button>
            </div>
          </div>
        </div>
        <!-- 功能按钮区 -->
        <div class="flex justify-between items-center mb-6">
          <a-button type="primary" class="!rounded-button" @click="handleAdd">
            <plus-outlined />
            添加
          </a-button>
        </div>
        <!-- 数据表格 -->
        <div class="bg-white rounded-lg shadow-sm">
          <a-table :columns="columns" :data-source="dataSource" :pagination="pagination" @change="handleTableChange">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'productType'">
                <a-tag color="success">{{ record.productType }}</a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <div class="flex gap-2">
                  <a-button type="primary" size="small" class="!rounded-button" @click="handleEdit(record)"
                    >编辑</a-button
                  >
                  <a-button type="primary" danger size="small" class="!rounded-button" @click="handleDelete(record)"
                    >删除</a-button
                  >
                </div>
                <!-- 编辑对话框 -->
              </template>
            </template>
          </a-table>
        </div>
    </div>
    <!-- 新增对话框 -->
    <a-modal v-model:visible="addModalVisible" title="新增特性" @ok="handleAddOk" @cancel="handleAddCancel">
      <div class="space-y-4">
        <div class="flex items-center">
          <span class="w-24 text-right mr-2"><span class="text-red-500">*</span>产品类型</span>
          <a-select v-model:value="addForm.product_id" class="flex-1" @change="handleProductChange">
            <a-select-option value="1">TGFW</a-select-option>
            <a-select-option value="2">USM</a-select-option>
            <a-select-option value="3">云盾</a-select-option>
            <a-select-option value="4">WAF</a-select-option>
          </a-select>
        </div>
        <div class="flex items-center">
          <span class="w-24 text-right mr-2"><span class="text-red-500">*</span>特性名称</span>
          <a-input v-model:value="addForm.module_name" class="flex-1" />
        </div>
        <div class="flex items-center">
          <span class="w-24 text-right mr-2">所属目录</span>
          <a-cascader
            v-model:value="addForm.parent_id"
            :options="categoryOptions"
            :disabled="!addForm.product_id"
            placeholder="请选择所属目录"
            class="flex-1"
            :field-names="{ label: 'module_name', value: 'id' }"
            change-on-select
            :show-search="{ matchInputWidth: false }"
          />
        </div>
        <div class="flex items-center">
          <span class="w-24 text-right mr-2">责任组</span>
          <a-input v-model:value="addForm.group_responsible" class="flex-1" />
        </div>
        <div class="flex items-center">
          <span class="w-24 text-right mr-2">责任人</span>
          <a-select v-model:value="addForm.person_responsible" class="flex-1">
            <a-select-option v-for="user in userOptions" :key="user.id" :value="user.real_name">
              {{ user.real_name }}
            </a-select-option>
          </a-select>
        </div>
      </div>
    </a-modal>
    <!-- 编辑对话框 -->
    <a-modal
      v-model:visible="editModalVisible"
      title="编辑"
      @ok="handleEditOk"
      @cancel="handleEditCancel"
      :maskClosable="false"
      :mask="false"
    >
      <div class="space-y-4">
        <div class="flex items-center">
          <span class="w-24 text-right mr-2"
            ><span class="text-red-500" @change="handleEditProductChange">*</span>产品类型</span
          >
          <a-select v-model:value="editForm.product" class="flex-1">
            <a-select-option value="TGFW">TGFW</a-select-option>
            <a-select-option value="USM">USM</a-select-option>
            <!-- 原USM V8/V9合并 -->
            <a-select-option value="云盾">云盾</a-select-option>
            <a-select-option value="WAF">WAF</a-select-option>
          </a-select>
        </div>
        <div class="flex items-center">
          <span class="w-24 text-right mr-2"><span class="text-red-500">*</span>特性名称</span>
          <a-input v-model:value="editForm.module_name" class="flex-1" />
        </div>
        <div class="flex items-center">
          <span class="w-24 text-right mr-2">所属目录</span>
          <a-cascader
            v-model:value="editForm.parent_id"
            :options="categoryOptions"
            :disabled="!editForm.product"
            placeholder="请选择所属目录"
            class="flex-1"
            :field-names="{ label: 'module_name', value: 'id' }"
            change-on-select
            :show-search="{ matchInputWidth: false }"
          />
            
        </div>
        <div class="flex items-center">
          <span class="w-24 text-right mr-2">层级</span>
          <a-input v-model:value="editForm.level" class="flex-1" />
        </div>
        <div class="flex items-center">
          <span class="w-24 text-right mr-2">责任组</span>
          <a-input v-model:value="editForm.group_responsible" class="flex-1" />
        </div>
        <div class="flex items-center">
          <span class="w-24 text-right mr-2">责任人</span>
          <a-input v-model:value="editForm.person_responsible" class="flex-1" />
        </div>
      </div>
    </a-modal>
  </div>
</template>
  <script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from "vue"
import { message, Modal } from "ant-design-vue"
import { PlusOutlined } from "@ant-design/icons-vue"
import {
  getFeatureList,
  deleteFeature,
  fetchFeatureTree,
  FeatureTreeNode,
  getUserList,
  createFeature,
  UserItem,
  updateFeature
} from "@/api/statistics"

interface EditFormState {
  id: number;
  product: string
  module_name: string
  parent_id: string[] | undefined // 修改为数组类型
  parent_name: string
  level: string
  group_responsible: string
  person_responsible: string
}
const searchForm = ref({
  product: undefined,
  module_name: "",
  level: "",
  group_responsible: "",
  person_responsible: "",
})
const columns = [
  {
    title: "序号",
    dataIndex: "id",
    width: 80,
  },
  {
    title: "产品类型",
    dataIndex: "product",
    key: "product",
    width: 120,
  },
  {
    title: "特性名称",
    dataIndex: "module_name",
    width: 200,
  },
  {
    title: "所属目录",
    dataIndex: "parent_name",
    width: 150,
  },
  {
    title: "层级",
    dataIndex: "level",
    width: 100,
  },
  {
    title: "责任组",
    dataIndex: "group_responsible",
    width: 150,
  },
  {
    title: "责任人",
    dataIndex: "person_responsible",
    width: 150,
  },
  {
    title: "操作",
    key: "action",
    width: 200,
  },
]
const dataSource = ref([])
const pagination = ref({
  total: 279,
  current: 1, // 确保使用正确的属性名
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
})
const userOptions = ref<UserItem[]>([])

const addModalVisible = ref(false)

// 修改 addForm 类型定义
const addForm = reactive<{
  product_id?: string
  module_name: string
  person_responsible?: string
  group_responsible: string
  parent_id?: string[] // 明确指定为字符串数组类型
}>({
  product_id: undefined,
  module_name: "",
  person_responsible: undefined,
  group_responsible: "",
  parent_id: undefined,
})
const handleAdd = () => {
  addForm.product_id = undefined
  addForm.module_name = ""
  addForm.person_responsible = undefined
  addForm.group_responsible = ""
  addForm.parent_id = undefined
  addModalVisible.value = true
}

const handleProductChange = async (productId: string) => {
  try {
    const treeData = await fetchFeatureTree({ product_id: productId })
    categoryOptions.value = treeData.data.data.map(convertFeatureNode)
  } catch (e) {
    message.error("目录加载失败")
  }
}

const convertProductNameToId = (name: string) => {
  const map: Record<string, string> = {
    TGFW: "1",
    USM: "2", // 统一所有USM变体到2
    "USM V8": "2", // 保持向后兼容
    "USM V9": "2", // 保持向后兼容
    云盾: "3",
    WAF: "4",
  }
  return map[name] || ""
}

// 编辑产品类型变更处理
const handleEditProductChange = async (productName: string) => {
  try {
    const productId = convertProductNameToId(productName)
    const treeData = await fetchFeatureTree({ product_id: productId })
    categoryOptions.value = treeData.data.data.map(convertFeatureNode)
    editForm.parent_id = undefined // 清空已选目录
  } catch (e) {
    message.error("目录加载失败")
  }
}

const handleAddOk = async () => {
  try {
    console.log("parent_id", addForm.parent_id)
    const res = await createFeature({
      product_id: addForm.product_id,
      module_name: addForm.module_name,
      person_responsible: addForm.person_responsible,
      group_responsible: addForm.group_responsible,
      parent_id: addForm.parent_id?.length
        ? String(addForm.parent_id.slice(-1)[0]) // 显式转换为字符串
        : null,
    })

    console.log("res", res.data)
    message.success(res.data.data.msg || "操作成功") // 使用接口返回的msg
    addModalVisible.value = false
    loadData()
  } catch (e) {
    message.error(e.response?.data?.msg || "操作失败") // 显示服务端错误信息
  }
}

const handleAddCancel = () => {
  addModalVisible.value = false
}

const handleSearch = () => {
  pagination.value.current = 1 // 搜索时重置到第一页
  loadData()
}
// 修改重置方法
const handleReset = () => {
  searchForm.value = {
    product: undefined,
    module_name: "",
    level: "",
    group_responsible: "",
    person_responsible: "",
  }
  loadData() // 重置后重新加载数据
}

const handleRefresh = () => {
  message.success("执行刷新操作")
}
const handleExport = () => {
  message.success("执行导出操作")
}
const handleColumnSetting = () => {
  message.success("执行列设置操作")
}
const editModalVisible = ref(false)
const editForm = reactive<EditFormState>({
  id: 0, // 初始 id 为0
  product: "",
  parent_id: undefined, // 初始化为 undefined 而不是空字符串
  module_name: "",
  parent_name: "",
  level: "",
  group_responsible: "",
  person_responsible: "",
})
const categoryOptions = ref<FeatureTreeNode[]>([])

// 新增数据结构转换方法
const convertFeatureNode = (node: FeatureTreeNode) => ({
  ...node,
  value: node.id,
  label: node.module_name,
  // 当没有子节点时设为undefined，允许选择父节点
  children: node.children && node.children.length > 0 ? node.children.map(convertFeatureNode) : undefined,
})

const productIdMap: Record<string, string> = {
  "16": "TGFW",
  "17": "WAF",
  "18": "USM V9",
}

onMounted(async () => {
  try {
    const treeData = await fetchFeatureTree()
    console.log("treeDate", treeData.data)
    const users = await getUserList()
    userOptions.value = users.data.results
    // 转换数据结构为Cascader需要的格式
    categoryOptions.value = treeData.data.data.map(convertFeatureNode)
  } catch (e) {
    console.error("加载分类树失败:", e)
    message.error("分类目录加载失败")
  }
})
const loadData = async () => {
  try {
    const response = await getFeatureList({
      product: searchForm.value.product,
      module_name: searchForm.value.module_name,
      person_responsible: searchForm.value.person_responsible,
      level: searchForm.value.level,
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
    })

    dataSource.value = response.data.data.map((item) => ({
      id: item.id,
      product: item.product,
      module_name: item.module_name,
      parent_name: item.parent_name,
      level: item.level,
      group_responsible: item.group_responsible,
      person_responsible: item.person_responsible,
    }))
    pagination.value.total = response.data.total
  } catch (e) {
    message.error("数据加载失败")
  }
}

const handleEdit = async (record: any) => {
  // 清空之前的表单数据
  editForm.product = ""
  editForm.parent_id = undefined
  editForm.module_name = ""

  try {
    const productId = convertProductNameToId(record.product)
    const treeData = await fetchFeatureTree({ product_id: productId })
    // 转换目录数据结构（新增这部分）
    const convertedTree = treeData.data.data.map(convertFeatureNode)
    categoryOptions.value = convertedTree

    // 转换 parent_id 为级联选择器需要的数组格式（这里需要根据实际数据结构调整）
    const parentIdPath = record.parent_id
      ? [record.parent_id] // 如果接口返回的是直接父级ID
      : undefined

    // 初始化表单数据（新增延迟设置）
    setTimeout(() => {
      editForm.id = record.id;
      editForm.product = record.product
      editForm.module_name = record.module_name
      editForm.parent_id = parentIdPath // 使用转换后的路径
      editForm.parent_name = record.parent_name
      editForm.level = record.level
      editForm.group_responsible = record.group_responsible
      editForm.person_responsible = record.person_responsible
      editModalVisible.value = true
    }, 100)
  } catch (e) {
    message.error("目录加载失败")
  }
}

const handleEditOk = async () => {
  try {
    // 转换产品名称到ID
    const productId = convertProductNameToId(editForm.product);
    // 获取父级ID（取级联选择器最后一级）
    const parentId = editForm.parent_id?.length ? editForm.parent_id.slice(-1)[0] : undefined;

   const res =  await updateFeature({
      feature_id: editForm.id, // 从当前编辑记录获取ID
      product_id: productId,
      module_name: editForm.module_name,
      person_responsible: editForm.person_responsible,
      group_responsible: editForm.group_responsible,
      parent_id: parentId ? Number(parentId) : undefined
    });
    // console.log(res);
    if(res.status === 200){
      message.success("保存成功");
    }
    editModalVisible.value = false;
    loadData(); // 刷新表格数据
  } catch (e) {
    message.error(e.response?.data?.msg || "保存失败");
  }
}

const handleEditCancel = () => {
  editModalVisible.value = false
  // 重置编辑表单
  editForm.product = ""
  editForm.parent_id = undefined
  editForm.module_name = ""
  editForm.parent_name = ""
  editForm.level = ""
  editForm.group_responsible = ""
  editForm.person_responsible = ""
}
const handleDelete = (record: any) => {
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除 "${record.module_name}" 吗？`,
    okText: "确认",
    cancelText: "取消",
    async onOk() {
      // 添加async
      try {
        await deleteFeature(record.id) // 添加await
        message.success("删除成功")
        loadData() // 删除后刷新数据
      } catch (e) {
        // message.error("删除失败") // 添加错误提示
      }
    },
  })
}
const handleTableChange = (pag: any) => {
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
  loadData() // 分页变化时重新加载数据
}
onMounted(() => {
  let localProduct = localStorage.getItem("product")
  let needAutoFetch = false
  if (localProduct) {
    try {
      let productArr: any[] = []
      if (localProduct.startsWith("[") || localProduct.startsWith("{")) {
        productArr = JSON.parse(localProduct)
      } else if (localProduct.includes(",")) {
        productArr = localProduct.split(",")
      } else {
        productArr = [localProduct]
      }
      if (Array.isArray(productArr) && productArr.length > 0) {
        const first = String(productArr[0]).trim()
        if (productIdMap[first]) {
          searchForm.value.product = productIdMap[first]
          needAutoFetch = true
        } else {
          searchForm.value.product = ""
        }
      }
    } catch (e) {
      searchForm.value.product = ""
    }
  } else {
    searchForm.value.product = ""
  }
  if (needAutoFetch) {
    nextTick(() => {
      handleSearch?.()
    })
  } else {
    handleSearch?.()
  }
})
</script>
  <style scoped>
:deep(.ant-input) {
  border-radius: 6px;
}

:deep(.ant-select-selector) {
  border-radius: 6px !important;
}

:deep(.ant-table-thead > tr > th) {
  background: #f5f7fa;
}

:deep(.ant-pagination-options-quick-jumper input) {
  border-radius: 6px;
}

:deep(.ant-modal-content) {
  border-radius: 8px;
}

:deep(.ant-modal-header) {
  border-radius: 8px 8px 0 0;
}

:deep(.ant-cascader-picker) {
  width: 100%;
}
</style>
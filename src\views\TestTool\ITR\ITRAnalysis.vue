<!-- The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work. -->
<template>
  <div class="min-h-screen bg-gray-50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-6 max-w-[1920px]">
      <!-- 顶部筛选 -->
      <div class="flex justify-end mb-8 space-x-4">
        <div class="flex items-center">
          <span class="mr-2">日期范围</span>
          <a-range-picker
            v-model:value="filters.dateRange"
            :picker="'month'"
            :format="'YYYY年MM月'"
            :disabled-date="disableCrossYear"
            :allow-clear="true"
            class="!rounded-button"
            style="width: 280px"
          />
        </div>
        <div class="flex items-center">
          <span class="mr-2">产品筛选</span>
          <a-select
            v-model:value="filters.product"
            mode="multiple"
            style="width: 200px"
            allowClear
            placeholder="请选择产品"
            :options="[
              { value: 'USM V8', label: 'USM V8' },
              { value: 'USM V9', label: 'USM V9' },
              { value: 'TGFW', label: 'TGFW' },
              { value: 'USM 云安全', label: 'USM 云安全' },
              { value: 'WAF', label: 'WAF' },
            ]"
            class="custom-product-select"
          />
          <a-button
            type="primary"
            @click="fetchData"
            class="ml-4 h-9 px-6 rounded-lg transition-colors duration-200 bg-blue-500 hover:bg-blue-600 border-none"
          >
            查询
          </a-button>
        </div>
      </div>
      <!-- 数据总览卡片 -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
        <div
          class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100"
        >
          <div class="flex items-center justify-between">
            <div class="text-gray-600">总BUG</div>
            <div class="text-blue-500 bg-blue-50 p-2 rounded-full">
              <i class="fas fa-bug text-lg"></i>
            </div>
          </div>
          <div class="text-2xl font-semibold mt-4">{{ computedTotalGroup.totalBugs }}</div>
          <div class="text-green-500 text-sm mt-2">
            <!-- <i class="fas fa-arrow-up mr-1"></i> -->
            <!-- <span>较上月增长 12%</span> -->
          </div>
        </div>
        <div
          class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100"
        >
          <div class="flex items-center justify-between">
            <div class="text-gray-600">新增Bug</div>
            <div class="text-purple-500 bg-purple-50 p-2 rounded-full">
              <i class="fas fa-clipboard-list text-lg"></i>
            </div>
          </div>
          <div class="text-2xl font-semibold mt-4">{{ computedTotalGroup.testerBugs }}</div>
          <div class="text-red-500 text-sm mt-2">
            <!-- <i class="fas fa-arrow-down mr-1"></i> -->
            <!-- <span>较上月下降 5%</span> -->
          </div>
        </div>
        <div
          class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100"
        >
          <div class="flex items-center justify-between">
            <div class="text-gray-600">BUG逃逸率</div>
            <div class="text-orange-500 bg-orange-50 p-2 rounded-full">
              <i class="fas fa-exclamation-circle text-lg"></i>
            </div>
          </div>
          <div class="text-2xl font-semibold mt-4">
            {{
              (
                (computedTotalGroup.confirmedBugs / (computedTotalGroup.testerBugs + computedTotalGroup.itr)) * 100 || 0
              ).toFixed(2)
            }}%
          </div>
          <div class="text-green-500 text-sm mt-2">
            <!-- <i class="fas fa-arrow-up mr-1"></i> -->
            <!-- <span>较上月增长 8%</span> -->
          </div>
        </div>
        <div
          class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100"
        >
          <div class="flex items-center justify-between">
            <div class="text-gray-600">新增ITR</div>
            <div class="text-red-500 bg-red-50 p-2 rounded-full">
              <i class="fas fa-times-circle text-lg"></i>
            </div>
          </div>
          <div class="text-2xl font-semibold mt-4">{{ computedTotalGroup.itr }}</div>
          <div class="text-red-500 text-sm mt-2">
            <!-- <i class="fas fa-arrow-up mr-1"></i> -->
            <!-- <span>较上月增长 2%</span> -->
          </div>
        </div>
      </div>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
        <div
          class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-shadow-md transition-shadow duration-300 border border-gray-100"
        >
          <div class="flex items-center justify-between">
            <div class="text-gray-600">是BUG</div>
            <div class="text-indigo-500 bg-indigo-50 p-2 rounded-full">
              <i class="fas fa-chart-line text-lg"></i>
            </div>
          </div>
          <div class="text-2xl font-semibold mt-4">{{ computedTotalGroup.confirmedBugs }}</div>
          <div class="text-yellow-500 text-sm mt-2">
            <!-- <i class="fas fa-minus mr-1"></i> -->
            <!-- <span>与上月持平</span> -->
          </div>
        </div>
        <div
          class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100"
        >
          <div class="flex items-center justify-between">
            <div class="text-gray-600">ITR逃逸率</div>
            <div class="text-green-500 bg-green-50 p-2 rounded-full">
              <i class="fas fa-chart-pie text-lg"></i>
            </div>
          </div>
          <div class="text-2xl font-semibold mt-4">
            {{
              ((computedTotalGroup.itr / (computedTotalGroup.testerBugs + computedTotalGroup.itr)) * 100 || 0).toFixed(
                2
              )
            }}%
          </div>
          <div class="text-green-500 text-sm mt-2">
            <!-- <i class="fas fa-arrow-down mr-1"></i> -->
            <!-- <span>较上月下降 0.5%</span> -->
          </div>
        </div>
        <div
          class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100"
        >
          <div class="flex items-center justify-between">
            <div class="text-gray-600">漏测</div>
            <div class="text-yellow-500 bg-yellow-50 p-2 rounded-full">
              <i class="fas fa-percentage text-lg"></i>
            </div>
          </div>
          <div class="text-2xl font-semibold mt-4">{{ computedTotalGroup.missed }}</div>
          <div class="text-red-500 text-sm mt-2">
            <!-- <i class="fas fa-arrow-up mr-1"></i> -->
            <!-- <span>较上月增长 0.2%</span> -->
          </div>
        </div>

        <!-- 新增第八个卡片 -->
        <div
          class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100"
        >
          <div class="flex items-center justify-between">
            <div class="text-gray-600">漏测率</div>
            <div class="text-pink-500 bg-pink-50 p-2 rounded-full">
              <i class="fas fa-clock text-lg"></i>
            </div>
          </div>
          <div class="text-2xl font-semibold mt-4">
            {{
              (
                (computedTotalGroup.missed / (computedTotalGroup.testerBugs + computedTotalGroup.itr)) * 100 || 0
              ).toFixed(2)
            }}%
          </div>
          <div class="text-green-500 text-sm mt-2">
            <!-- <i class="fas fa-arrow-down mr-1"></i> -->
            <!-- <span>较上月减少 0.3天</span> -->
          </div>
        </div>
      </div>
      <!-- 数据明细按钮 -->
      <div class="mb-8">
        <a-button type="primary" @click="toggleDetails" class="!rounded-button">
          <template #icon>
            <EyeOutlined />
          </template>
          查看产品数据明细
        </a-button>
      </div>
      <!-- 明细数据展示 -->
      <div v-if="showDetails" class="mb-8">
        <div class="bg-white p-6 rounded-lg shadow-sm">
          <div class="mb-4">
            <h3 class="text-lg font-medium mb-4">产品明细数据</h3>
            <!-- 表头 -->
            <div class="hidden lg:grid lg:grid-cols-10 gap-2 px-4 py-2 bg-gray-50 rounded-t-lg font-medium text-sm">
              <div class="col-span-2">产品</div>
              <div class="col-span-1">总BUG</div>
              <div class="col-span-1">新增Bug</div>
              <div class="col-span-1">新增ITR</div>
              <div class="col-span-1">是Bug</div>
              <div class="col-span-1">BUG逃逸率</div>
              <div class="col-span-1">ITR逃逸率</div>
              <div class="col-span-1">漏测</div>
              <div class="col-span-1">漏测率</div>
            </div>

            <div class="lg:hidden">
              <div class="grid grid-cols-1 gap-4 px-4 py-2 bg-gray-50 rounded-t-lg font-medium text-sm">
                <div>产品数据</div>
                <div>产品</div>
                <div>总BUG</div>
                <div>新增Bug</div>
                <div>新增ITR</div>
                <div>是Bug</div>
                <div>BUG逃逸率</div>
                <div>ITR逃逸率</div>
                <div>漏测</div>
                <div>漏测率</div>
              </div>
            </div>
            <div class="border-x border-b rounded-b-lg">
              <!-- 添加USM产品组汇总数据 -->
              <div class="hidden lg:grid lg:grid-cols-10 gap-2 px-4 py-3 border-t bg-gray-50">
                <div class="col-span-2">USM产品组</div>
                <div class="col-span-1">{{ computedUSMGroup.totalBugs }}</div>
                <div class="col-span-1">{{ computedUSMGroup.testerBugs }}</div>
                <div class="col-span-1">{{ computedUSMGroup.itr }}</div>
                <div class="col-span-1">{{ computedUSMGroup.confirmedBugs }}</div>
                <div class="col-span-1">
                  {{
                    (
                      (computedUSMGroup.confirmedBugs / (computedUSMGroup.testerBugs + computedUSMGroup.itr)) * 100 || 0
                    ).toFixed(2)
                  }}%
                </div>
                <div class="col-span-1">
                  {{
                    ((computedUSMGroup.itr / (computedUSMGroup.testerBugs + computedUSMGroup.itr)) * 100 || 0).toFixed(
                      2
                    )
                  }}%
                </div>
                <div class="col-span-1">{{ computedUSMGroup.missed }}</div>
                <div class="col-span-1">
                  {{
                    (
                      (computedUSMGroup.missed / (computedUSMGroup.testerBugs + computedUSMGroup.itr)) * 100 || 0
                    ).toFixed(2)
                  }}%
                </div>
              </div>

              <div class="px-4 py-3 bg-gray-100 cursor-pointer" @click="toggleUSMGroup">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <i :class="['fas mr-2', usmGroupExpanded ? 'fa-chevron-down' : 'fa-chevron-right']"></i>
                    <span class="font-medium">USM产品组</span>
                  </div>
                </div>
              </div>
              <div v-show="usmGroupExpanded">
                <div
                  v-for="product in usmProducts"
                  :key="product.name"
                  class="hidden lg:grid lg:grid-cols-10 gap-4 px-4 py-3 border-t"
                >
                  <div class="col-span-2">{{ product.name }}</div>
                  <div class="col-span-1">{{ product.metrics.totalBugs }}</div>
                  <div class="col-span-1">{{ product.metrics.testerBugs }}</div>
                  <div class="col-span-1">{{ product.metrics.itr }}</div>
                  <div class="col-span-1">{{ product.metrics.confirmedBugs }}</div>
                  <div class="col-span-1">
                    {{ (product.metrics.bugEscapeRate || 0).toFixed(2) }}%
                    <!-- 添加百分号 -->
                  </div>
                  <div class="col-span-1">
                    {{ (product.metrics.itrEscapeRate || 0).toFixed(2) }}%
                    <!-- 添加百分号 -->
                  </div>
                  <div class="col-span-1">{{ product.metrics.missed }}</div>
                  <div class="col-span-1">
                    {{ (product.metrics.missRate || 0).toFixed(2) }}%
                    <!-- 添加百分号 -->
                  </div>
                </div>
              </div>
            </div>
            <!-- 其他产品 -->
            <div
              v-for="product in otherProducts"
              :key="product.name"
              class="hidden lg:grid lg:grid-cols-10 gap-4 px-4 py-3 border-x border-b"
            >
              <div class="col-span-2">{{ product.name }}</div>
              <div class="col-span-1">{{ product.metrics.totalBugs }}</div>
              <div class="col-span-1">{{ product.metrics.testerBugs }}</div>
              <div class="col-span-1">{{ product.metrics.itr }}</div>
              <div class="col-span-1">{{ product.metrics.confirmedBugs }}</div>
              <div class="col-span-1">{{ (product.metrics.bugEscapeRate || 0).toFixed(2) }}%</div>
              <div class="col-span-1">{{ (product.metrics.itrEscapeRate || 0).toFixed(2) }}%</div>
              <div class="col-span-1">{{ product.metrics.missed }}</div>
              <div class="col-span-1">{{ (product.metrics.missRate || 0).toFixed(2) }}%</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 图表区域 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        <div v-for="(chart, index) in charts" :key="index" class="bg-white p-6 rounded-lg shadow-sm">
          <div class="text-lg font-medium mb-4">{{ chart.title }}</div>
          <div ref="chartRefs" class="h-[300px] w-full"></div>
        </div>
      </div>
      <a-modal
        v-model:visible="dialogVisible"
        title="详细数据"
        width="80%"
        :footer="null"
        @cancel="dialogVisible = false"
        :maskClosable="true"
        :keyboard="false"
      >
        <div class="mb-4">
          <span class="mr-4">月份：{{ selectedParams.month }}</span>
          <span>产品：{{ selectedParams.product }}</span>
        </div>

        <a-table
          :dataSource="detailData"
          :columns="[
            { title: 'BUGID', dataIndex: 'id' },
            {
              title: 'BUG标题',
              dataIndex: 'title',
              customRender: ({ text, record }) => ({
                children: h(
                  'a',
                  {
                    href: `https://rdms-cd.das-security.cn/index.php?m=bug&f=view&bugID=${record.id}`,
                    target: '_blank',
                    class: 'text-blue-600 hover:text-blue-800',
                  },
                  text
                ),
              }),
            },
            {
              title: 'BUG优先级',
              dataIndex: 'pri',
              width: 80,
              customRender: ({ text }) => ({ children: '' }), // 清空原有渲染
            },
            {
              title: 'BUG严重等级',
              dataIndex: 'severity',
              width: 80,
              customRender: ({ text }) => ({ children: '' }), // 清空原有渲染
            },
            { title: '创建人', dataIndex: 'openedby', width: 100 },
            { title: '指派人', dataIndex: 'assignedto', width: 100 },
          ]"
          :pagination="pagination"
          :loading="loading"
          bordered
          @change="handlePageChange"
          class="ant-table-wrapper"
        >
          <template #bodyCell="{ column, text }">
            <!-- 严重等级列 -->
            <template v-if="column.dataIndex === 'severity'">
              <a-tag :color="getSeverityColor(text as string)">
                {{
                  {
                    "1": "紧急",
                    "2": "严重",
                    "3": "一般",
                    "4": "建议",
                    critical: "critical",
                    normal: "normal",
                  }[text as string] || text
                }}
              </a-tag>
            </template>

            <!-- 优先级列 -->
            <template v-else-if="column.dataIndex === 'pri'">
              <a-tag :color="getPriColor(text as string)">
                {{
                  {
                    "0": "无",
                    "1": "紧急",
                    "2": "优先",
                    "3": "正常",
                    "4": "建议",
                  }[text as string] || text
                }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </a-modal>
    </div>
  </div>
</template>
  <script lang="ts" setup>
import { ref, onMounted, onUnmounted, onActivated, computed, h, watch } from "vue"
import { nextTick } from "vue"

import dayjs, { Dayjs } from "dayjs"
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
import { useRoute } from "vue-router"
import * as echarts from "echarts"
import { EyeOutlined } from "@ant-design/icons-vue"
import { message } from "ant-design-vue"
import { getProductStatistics, ChartDataItem, MonthlyTrendItem, getBugList } from "@/api/statistics"

const dialogVisible = ref(false)
// 或使用接口明确定义类型
interface SelectedParams {
  month: string
  product: string
  type: string
}
const selectedParams = ref<SelectedParams>({
  month: "",
  product: "",
  type: "", // 保持与接口一致
})

const chartData = ref<ChartDataItem[]>([])
const chartsData = ref<{
  itrTrend: Record<string, MonthlyTrendItem[]>
  bugTrend: Record<string, MonthlyTrendItem[]>
  missedTrend: Record<string, MonthlyTrendItem[]>
}>({
  itrTrend: {},
  bugTrend: {},
  missedTrend: {},
})

const loading = ref(false)

const requestParams = computed(() => {
  const [start, end] = filters.value.dateRange || [
    dayjs().startOf("month").subtract(11, "month"),
    dayjs().endOf("month"),
  ]

  // 将数组转换为逗号分隔的字符串
  const productsString = filters.value.product.length > 0 ? filters.value.product.join(",") : undefined

  return {
    start_year_month: start?.format("YYYY-MM"), // 修改为年月格式
    end_year_month: end?.format("YYYY-MM"), // 修改为年月格式
    product: productsString,
  }
})

const filters = ref({
  dateRange: [dayjs().startOf("month").subtract(11, "month"), dayjs().endOf("month")] as [Dayjs, Dayjs], // 修改为近12个月
  product: [] as string[],
})

// 移除未使用的cards数据
const showDetails = ref(false)
const usmGroupExpanded = ref(false)
const usmProducts = ref<ChartDataItem[]>([])
const otherProducts = ref<ChartDataItem[]>([])

// 新增计算属性自动分类产品
const productGroups = computed(() => {
  return {
    usm: chartData.value?.filter((item) => item.name.includes("USM")) || [],
    others: chartData.value?.filter((item) => !item.name.includes("USM")) || [],
  }
})

const computedUSMGroup = computed(() => {
  const usmProducts = productGroups.value.usm || []
  return usmProducts.reduce(
    (acc, product) => ({
      totalBugs: acc.totalBugs + (product.metrics.totalBugs || 0),
      testerBugs: acc.testerBugs + (product.metrics.testerBugs || 0),
      itr: acc.itr + (product.metrics.itr || 0),
      confirmedBugs: acc.confirmedBugs + (product.metrics.confirmedBugs || 0),
      missed: acc.missed + (product.metrics.missed || 0),
    }),
    {
      totalBugs: 0,
      testerBugs: 0,
      itr: 0,
      confirmedBugs: 0,
      missed: 0,
    }
  )
})

// 添加日期限制方法 ↓
const disableCrossYear = (current: Dayjs) => {
  const today = dayjs()
  const oneYearAgo = today.subtract(1, "year")

  // 禁用一年前的日期和未来的日期
  return current.isBefore(oneYearAgo) || current.isAfter(today)
}

const getSeverityColor = (severity: string | number) => {
  const severityStr = String(severity)
  const colorMap: Record<string, string> = {
    "1": "red", // 紧急
    "2": "orange", // 严重
    "3": "green", // 一般
    "0": "gray", // 无
    critical: "purple",
    normal: "blue",
  }
  return colorMap[severityStr] || "gray"
}

const getPriColor = (pri: string | number) => {
  const priStr = String(pri)
  const colorMap: Record<string, string> = {
    "1": "red", // 紧急
    "2": "orange", // 优先
    "3": "blue", // 正常
    "0": "gray", // 无
  }
  return colorMap[priStr] || "gray"
}

const computedTotalGroup = computed(() => {
  return chartData.value.reduce(
    (acc, product) => ({
      totalBugs: acc.totalBugs + (product.metrics.totalBugs || 0),
      testerBugs: acc.testerBugs + (product.metrics.testerBugs || 0),
      itr: acc.itr + (product.metrics.itr || 0),
      confirmedBugs: acc.confirmedBugs + (product.metrics.confirmedBugs || 0),
      missed: acc.missed + (product.metrics.missed || 0),
    }),
    {
      totalBugs: 0,
      testerBugs: 0,
      itr: 0,
      confirmedBugs: 0,
      missed: 0,
    }
  )
})

// 修改后的fetchData方法
const fetchData = async () => {
  console.log("开始请求数据...")

  try {
    loading.value = true
    const res = await getProductStatistics(requestParams.value)
    console.log("数据获取成功:", productGroups)
    chartData.value = res.data?.chartData || []
    usmProducts.value = [...(productGroups.value.usm || [])] // 添加数组解构
    otherProducts.value = [...(productGroups.value.others || [])] // 确保响应式更新
    console.log("漏测分布数据:", res.data.data?.missedMonthlyTrend)

    // 更新图表数据（添加空值保护）
    chartsData.value = {
      itrTrend: res.data.data?.itrMonthlyTrend || {},
      bugTrend: res.data.data?.escapeBugMonthlyTrend || {},
      missedTrend: res.data.data?.missedMonthlyTrend || {},
    }

    // 添加年份变化处理 ↓
    // const handleYearChange = (dates: [Dayjs, Dayjs] | null) => {
    //   if (dates && dates[0]?.year() !== dates[1]?.year()) {
    //     message.error("请选择同一年份内的月份范围")
    //     filters.value.dateRange = null
    //   }
    // }

    // 更新产品数据（添加空值保护）
    chartData.value = res.data.data?.chartData || []
    console.log("产品数据:", chartData.value)
    console.log("USM产品数据:", productGroups.value.usm)
    usmProducts.value = productGroups.value.usm ?? [] // 添加空值保护
    otherProducts.value = productGroups.value.others ?? [] // 添加空值保护

    initCharts()
  } catch (e) {
    console.error("数据获取失败:", e)
  } finally {
    loading.value = false
  }
}

// 修改后的createChartOption方法
const createChartOption = (title: string) => {
  type TrendMap = Record<string, MonthlyTrendItem[]>;
  type SeriesMapKey = 'itrTrend' | 'bugTrend' | 'missedTrend';
  const seriesMap: Record<string, SeriesMapKey> = {
    "ITR 统计分布": "itrTrend",
    逃逸分布: "bugTrend",
    漏测分布: "missedTrend",
  };
  const chartDataSet: TrendMap = chartsData.value?.[seriesMap[title]] || {};

  // 获取所有月份数据并排序
  const allMonths = Object.values(chartDataSet)
    .flatMap((data: MonthlyTrendItem[]) => data?.map((item: MonthlyTrendItem) => item.month) || [])
    .filter((value, index, self) => self.indexOf(value) === index)
    .sort((a, b) => {
      const [yearA, monthA] = a.split("年")
      const [yearB, monthB] = b.split("年")
      return yearA === yearB ? parseInt(monthA) - parseInt(monthB) : parseInt(yearA) - parseInt(yearB)
    })

  return {
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "shadow" },
      formatter: (params: any) => {
        return params
          .map(
            (item: any) => `
            ${item.marker} ${item.seriesName}<br/>
            ${item.name}: ${item.value}
          `
          )
          .join("<br/>")
      },
    },
    legend: {
      data: Object.keys(chartDataSet),
      right: 20,
      top: 10,
      itemWidth: 12,
      itemHeight: 12,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "12%",
      top: "20%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: allMonths, // 使用排序后的月份数据
      axisTick: { alignWithLabel: true },
      axisLabel: {
        interval: 0,
        rotate: 45, // 斜着显示文字，防止重叠
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        lineStyle: { type: "dashed" },
      },
    },
    series: Object.entries(chartDataSet).map(([product, data], index) => {
      // 创建与 allMonths 长度相同的数据数组，默认值为 0
      const seriesData = allMonths.map((month) => {
        const matchingData = (data as MonthlyTrendItem[])?.find((item: MonthlyTrendItem) => item.month === month)
        return matchingData?.value || 0
      })

      const colors = ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272"]

      return {
        name: product,
        type: "bar",
        stack: "总量",
        barWidth: 22,
        itemStyle: {
          color: colors[index % colors.length],
          borderRadius: [4, 4, 0, 0],
        },
        label: {
          show: false,
          position: "top",
          formatter: "{c}",
        },
        emphasis: {
          focus: "series",
        },
        data: seriesData,
      }
    }),
  }
}

// 修改日期范围监听器
watch(
  () => filters.value.dateRange,
  (newRange) => {
    if (!newRange || !newRange[0] || !newRange[1]) return

    const startDate = newRange[0]
    const endDate = newRange[1]

    // 计算日期差异（月份）
    const monthsDiff = endDate.diff(startDate, "month")

    if (monthsDiff > 11) {
      message.error("查询时间范围不能超过一年")
      // 重置为最近一年（从当前月份开始往前推11个月）
      filters.value.dateRange = [dayjs().startOf("month").subtract(11, "month"), dayjs().endOf("month")]
    }
  },
  { immediate: false }
) // 修改为 false，避免初始化时触发

const toggleDetails = () => {
  showDetails.value = !showDetails.value
  if (!showDetails.value) {
    usmGroupExpanded.value = false
  }
}

const toggleUSMGroup = () => {
  usmGroupExpanded.value = !usmGroupExpanded.value
}
const chartRefs = ref<HTMLElement[]>([])
const charts = ref([{ title: "ITR 统计分布" }, { title: "逃逸分布" }, { title: "漏测分布" }])

// 添加 typeMap 定义
const typeMap: Record<string, string> = {
  "ITR 统计分布": "itr",
  逃逸分布: "is_bug",
  漏测分布: "is_test_missed",
}

// 产品ID与名称的映射表
const productIdMap: Record<string, string> = {
  "16": "TGFW",
  "17": "WAF",
  "18": "USM V9",
}

// 增加图表实例引用
const chartInstances = ref<echarts.ECharts[]>([])
// 修改图表创建函数，添加响应式支持

const createChart = (el: HTMLElement, index: number) => {
  if (chartInstances.value[index]) {
    chartInstances.value[index].dispose()
  }
  const chart = echarts.init(el)
  chartInstances.value[index] = chart

  // 获取当前图表标题
  const currentChartTitle = charts.value[index].title

  // 获取动态配置
  const chartType = charts.value[index].title
  const option = createChartOption(chartType)

  // 应用配置
  chart.setOption(option)

  // 添加窗口resize监听
  window.addEventListener("resize", () => chart.resize())

  // 添加点击事件监听（修复作用域问题）
  chart.on("click", (params: any) => {
    ;(async () => {
      const monthStr = params.name // 直接使用点击的月份字符串，格式如 "2024年1月"
      const [year, month] = monthStr.match(/(\d+)年(\d+)月/).slice(1)

      selectedParams.value = {
        month: `${year}-${month.padStart(2, "0")}`,
        product: params.seriesName,
        type: typeMap[currentChartTitle],
      }
      pagination.value.current = 1
      try {
        await fetchDetailData()
        nextTick(() => {
          dialogVisible.value = true
        })
      } catch (e) {
        console.error("请求失败:", e)
        dialogVisible.value = false
      }
    })()
  })
}

// 新增筛选变化处理方法 ↓

onUnmounted(() => {
  chartInstances.value.forEach((chart) => chart?.dispose())
  chartInstances.value = []
})

onMounted(() => {
  // 读取本地存储的 product 字段
  let localProduct = localStorage.getItem("product")
  let needAutoFetch = false
  if (localProduct) {
    try {
      let productArr: any[] = []
      // 兼容字符串数组、数字、逗号分隔字符串
      if (localProduct.startsWith("[") || localProduct.startsWith("{")) {
        productArr = JSON.parse(localProduct)
      } else if (localProduct.includes(",")) {
        productArr = localProduct.split(",")
      } else {
        productArr = [localProduct]
      }
      if (Array.isArray(productArr) && productArr.length > 0) {
        const first = String(productArr[0]).trim()
        if (productIdMap[first]) {
          filters.value.product = [productIdMap[first]]
          needAutoFetch = true
        }
      }
    } catch (e) {
      // 解析失败忽略
    }
  }
  if (needAutoFetch) {
    // 等 filters 响应式更新后再 fetch
    nextTick(() => {
      fetchData()
    })
  } else {
    fetchData()
  }
})

// 添加详细数据获取方法
const fetchDetailData = async () => {
  try {
    // 转换月份为当月第一天和最后一天

    if (!selectedParams.value.month || !selectedParams.value.product) {
      throw new Error("缺少必要参数")
    }
    // 使用选中的月份创建日期范围
    const selectedMonth = dayjs(selectedParams.value.month)
    const startDate = selectedMonth.startOf("month").format("YYYY-MM-DD")
    const endDate = selectedMonth.endOf("month").format("YYYY-MM-DD")

    const res = await getBugList({
      startDate: startDate, // 使用筛选器的开始日期
      endDate: endDate, // 使用筛选器的结束日期
      product: selectedParams.value.product,
      type: selectedParams.value.type,
      current: pagination.value.current,
      pageSize: pagination.value.pageSize,
    })

    // 添加空值保护
    if (res?.data?.data) {
      detailData.value = res.data.data
      pagination.value.total = res.data.total || 0
    } else {
      throw new Error("接口返回数据结构异常")
    }
  } catch (e) {
    console.error("获取详细数据失败:", e)
    // 重置数据防止显示错误内容
    detailData.value = []
    pagination.value.total = 0
    message.error("数据加载失败，请稍后重试")
  }
}

const detailData = ref<any[]>([])
const pagination = ref({
  current: 1,
  pageSize: 5,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: [5, 10, 15], // 使用数字类型而非字符串
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
})

// 添加分页变化处理
const handlePageChange = (pag: { current: number; pageSize: number }) => {
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
  fetchDetailData()
}

onActivated(() => {
  fetchData()
})
// 修改后的初始化方法
const initCharts = () => {
  // 清空已有图表实例
  chartInstances.value.forEach((chart) => chart?.dispose())
  chartInstances.value = []

  // 重新创建所有图表
  chartRefs.value.forEach((el, index) => {
    if (el) {
      createChart(el, index)
    }
  })
}
</script>
  <style scoped>
.container {
  min-height: calc(100vh - 2rem);
  max-width: 100%;
  margin: 0 auto;
}

:deep(.ant-select-selector) {
  border-radius: 6px !important;
}

:deep(.ant-drawer-content) {
  border-radius: 16px 0 0 16px;
}
:deep(.custom-product-select .ant-select-selection-item) {
  display: flex;
  align-items: center;
  height: 32px;
  line-height: 32px;
}
:deep(.custom-product-select .ant-select-selection-item-content) {
  display: flex;
  align-items: center;
}
:deep(.custom-product-select .ant-select-selection-item-remove) {
  display: flex;
  align-items: center;
  margin-left: 2px;
}
</style>
  
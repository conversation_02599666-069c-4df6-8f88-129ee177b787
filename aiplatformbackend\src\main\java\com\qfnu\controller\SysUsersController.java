package com.qfnu.controller;

import com.qfnu.common.ResponseVO;
import com.qfnu.common.annotation.RequiredPermission;
import com.qfnu.model.po.SysUsersPO;
import com.qfnu.service.SysUsersService;
import com.qfnu.model.vo.SysUsersVO;
import com.qfnu.model.dto.SysUsersDTO;
import com.qfnu.model.param.SysUsersParam;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

@Api(tags = "SysUsers接口")
@RestController
@RequestMapping("/api/SysUsers")
public class SysUsersController {
    
    @Resource
    private SysUsersService sysUsersService;

    @ApiOperation("根据ID查询")
    @PostMapping("/getById")
    public ResponseVO<SysUsersPO> getById(@RequestBody SysUsersParam param) {
        return ResponseVO.success(sysUsersService.getById(param.getUserId()));
    }

    @RequiredPermission({"super,admin"})
    @ApiOperation("重置密码")
    @PostMapping("/resetPassword")
    public ResponseVO resetPassword(@RequestBody SysUsersParam param) {
        return ResponseVO.success(sysUsersService.resetPassword(param),200);
    }
    

} 
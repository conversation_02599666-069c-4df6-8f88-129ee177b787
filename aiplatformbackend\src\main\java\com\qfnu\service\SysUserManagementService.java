package com.qfnu.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qfnu.common.constant.DefaultConstants;
import com.qfnu.mapper.primary.SysUsersMapper;
import com.qfnu.model.dto.AuthUserDto;
import com.qfnu.model.param.SysUserManagementQueryParam;
import com.qfnu.model.po.SysUserManagementPO;
import com.qfnu.model.po.SysUsersPO;
import com.qfnu.model.vo.SysUserManagementVO;
import com.qfnu.model.param.SysUserManagementParam;
import com.qfnu.mapper.primary.SysUserManagementMapper;
import com.qfnu.converter.BaseConverter;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.mindrot.jbcrypt.BCrypt;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

@Service
public class SysUserManagementService extends ServiceImpl<SysUserManagementMapper, SysUserManagementPO> {

    @Resource
    private SysUserManagementMapper sysUserManagementMapper;

    @Resource
    private SysUsersMapper sysUsersMapper;

    @Value("${app.security.bcrypt.salt}")
    private String salt;

    @Resource
    private BaseConverter converter;

    public SysUserManagementVO getByUserId(String userId) {
        SysUserManagementPO po = sysUserManagementMapper.selectByUserId(userId);
        return converter.convertToSysUserManagementVO(po);
    }

    public SysUserManagementPO getByAccountName(String accountName) {
        return sysUserManagementMapper.selectByAccountName(accountName);
    }

    @Transactional(rollbackFor = Exception.class)
    public int save(SysUserManagementParam param) {
        try {

            // 检查用户名是否已存在于数据库中
            if (sysUsersMapper.getUserByUseranme(param.getUsername())!=null) {
                // 如果用户存在，则返回-1表示用户名已被占用
                return -1;
            }
            SysUserManagementPO sysUserManagementPO = new SysUserManagementPO();
            SysUsersPO sysUsersPO = new SysUsersPO();
            String userId = UUID.randomUUID().toString();

            // 构建 sysUsersPO 对象
            sysUsersPO.setUserId(userId);
            sysUsersPO.setDateJoined(new Date());
            sysUsersPO.setIsActive("1");
            sysUsersPO.setIsStaff("1");
            sysUsersPO.setUsername(param.getUsername());
            sysUsersPO.setPassword(BCrypt.hashpw(DefaultConstants.DEFAULT_PASSWORD, salt));
            String[] parts = param.getAccountName().split("\\.");
            if (parts.length >= 2) {
                sysUsersPO.setLastName(parts[1]);
                sysUsersPO.setFirstName(parts[0]);
            }
            sysUsersPO.setIsSuperuser("0");
            sysUsersMapper.insert(sysUsersPO);

            // 构建 sysUserManagementPO 对象
            sysUserManagementPO.setUserId(userId);
            sysUserManagementPO.setManagementId(UUID.randomUUID().toString());
            sysUserManagementPO.setUsername(param.getUsername());
            sysUserManagementPO.setAccountName(param.getAccountName());
            sysUserManagementPO.setRealName(param.getRealName());
            sysUserManagementPO.setUserType(param.getUserType());
            sysUserManagementPO.setRole("common");
            sysUserManagementPO.setProduct(param.getProduct());
            sysUserManagementMapper.insert(sysUserManagementPO);

            return 1;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public int saveBatch(List<SysUserManagementParam> paramList) {
        List<SysUserManagementPO> poList = converter.convertToSysUserManagementPO(paramList);
        return saveBatch(poList) ? poList.size() : 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public int update(SysUserManagementParam param) {
        // 参数校验
        if (Objects.isNull(param) || Objects.isNull(param.getManagementId())) {
            return 0;
        }
        SysUserManagementPO sysUserManagementPO = new SysUserManagementPO();
        sysUserManagementPO.setRealName(param.getRealName());
        sysUserManagementPO.setUserType(param.getUserType());
        sysUserManagementPO.setProduct(param.getProduct());
        sysUserManagementPO.setManagementId(param.getManagementId());
        sysUserManagementPO.setRole(param.getRole());

        return sysUserManagementMapper.myUpdateById(sysUserManagementPO);
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(SysUserManagementParam param) {
        int result1 = sysUserManagementMapper.myDeleteById(param.getManagementId());
        int result2 = sysUsersMapper.myDeleteById(param.getUserId());
        if (result1 == 1 && result2 == 1) {
            return 1;
        }
        return 0;
    }

    public SysUserManagementVO getById(String id) {
        SysUserManagementPO po = super.getById(id);
        if (po == null) {
            return null;
        }
        return converter.convertToSysUserManagementVO(po);
    }

    public List<SysUserManagementPO> getList(SysUserManagementParam param) {
        // 非分页查询
        LambdaQueryWrapper<SysUserManagementPO> wrapper = new LambdaQueryWrapper<>();
        return list(wrapper);
    }

    public IPage<SysUserManagementPO> getPageList(SysUserManagementQueryParam queryParam) {
        // 创建分页对象
        Page<SysUserManagementPO> page = new Page<>(queryParam.getPageNum(), queryParam.getPageSize());
        // 执行分页查询
        return sysUserManagementMapper.getPageList(page, queryParam);
    }

    /**
     * 批量添加或更新
     * @param paramList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<SysUserManagementVO> batchAddOrUpdate(List<SysUserManagementParam> paramList) {
        List<SysUserManagementPO> poList = converter.convertToSysUserManagementPO(paramList);
        saveOrUpdateBatch(poList);
        return converter.convertToSysUserManagementVO(poList);
    }

    /**
     * 授权
     * @param managementId
     */
    public void authorize(String managementId) {
        SysUserManagementPO po = new SysUserManagementPO();
        po.setUserId(managementId);
        po.setRole("admin");
        sysUserManagementMapper.updateById(po);
    }
}
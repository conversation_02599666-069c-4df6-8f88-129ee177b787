from typing import Dict, List, Any, Optional
from db.mysql.mysql_pool import MySQLPool
from logger import logging
import json
import uuid


class MapDocComponentsModel:
    """组件文档映射模型类 - 专门处理 map_doc_components 表"""

    def __init__(self):
        self.table = "map_doc_components"
        self.primary_key = "id"
        self.logger = logging()
        self.db_pool = MySQLPool()

    async def initialize(self):
        """初始化连接池"""
        await self.db_pool.initialize()

    async def close(self):
        """关闭连接池，释放资源"""
        await self.db_pool.close()

    async def delete_doc_mapping_by_name(self, document_name: str, knowledge_base_uid: str) -> bool:
        """
        根据知识库uid和文档名称删除组件映射记录

        Args:
            document_name: 文档名称
            knowledge_base_uid: 知识库UID

        Returns:
            bool: 删除是否成功
        """
        try:
            sql = f"""
                DELETE FROM {self.table}
                WHERE document_name = %s 
                AND knowledge_base_uid = %s
            """
            affected_rows = await self.db_pool.execute(sql, (document_name, knowledge_base_uid))

            if affected_rows > 0:
                self.logger.info(f"成功删除知识库 {knowledge_base_uid} 下的组件映射记录: {document_name}")
                return True
            self.logger.warning(f"未找到知识库 {knowledge_base_uid} 下的组件映射记录: {document_name}")
            return False

        except Exception as e:
            self.logger.error(f"删除组件映射记录失败: {str(e)}")
            raise

    async def check_doc_name(self, document_name: str, knowledge_base_uid: str) -> bool:
        """
        检查知识库中是否存在同名组件文档

        Args:
            document_name: 文档名称
            knowledge_base_uid: 知识库UID

        Returns:
            bool: 是否存在同名文档
        """
        try:
            sql = f"""
                SELECT COUNT(*) as count 
                FROM {self.table}
                WHERE document_name = %s 
                AND knowledge_base_uid = %s
            """
            result = await self.db_pool.fetch_one(sql, (document_name, knowledge_base_uid))

            if result and result.get("count", 0) > 0:
                self.logger.info(f"知识库 {knowledge_base_uid} 下存在同名组件文档: {document_name}")
                return True
            return False

        except Exception as e:
            self.logger.error(f"检查同名组件文档失败: {str(e)}")
            raise

    async def create_component_record(
            self,
            knowledge_base_uid: str,
            document_name: str,
            entity_name: str,
            schema_type: Optional[str] = None,
            title: Optional[str] = None,
            default_value: Optional[str] = None,
            enum_values: Optional[List] = None,
            properties: Optional[Dict] = None,
            required_properties: Optional[List] = None,
            status: str = "未嵌入"
    ) -> int:
        """
        创建组件记录

        Args:
            knowledge_base_uid: 知识库UID
            document_name: 文档名称
            entity_name: 实体名称
            schema_type: 组件类型
            title: 标题/描述
            default_value: 默认值
            enum_values: 枚举值列表
            properties: 属性列表
            required_properties: 必需属性列表
            status: 实体状态

        Returns:
            int: 插入记录的主键ID
        """
        try:
            # 处理默认值转换
            if default_value is not None and not isinstance(default_value, str):
                default_value = str(default_value)

            sql = f"""
                INSERT INTO {self.table} (
                    knowledge_base_uid, document_name, entity_name,
                    entity_type, schema_type, title,
                    default_value, enum_values, properties,
                    required_properties, status
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                knowledge_base_uid,
                document_name,
                entity_name[:100] if entity_name else "Unnamed Component",  # 限制长度
                "Component",
                schema_type[:50] if schema_type else None,  # 限制长度
                title[:255] if title else None,  # 限制长度
                default_value[:255] if default_value else None,  # 限制长度
                json.dumps(enum_values) if enum_values else None,
                json.dumps(properties) if properties else None,
                json.dumps(required_properties) if required_properties else None,
                status
            )

            return await self.db_pool.execute(sql, params)

        except Exception as e:
            self.logger.error(f"创建组件记录失败: {str(e)}")
            raise

    async def update_component_record(
            self,
            id: int,
            document_name: Optional[str] = None,
            entity_name: Optional[str] = None,
            schema_type: Optional[str] = None,
            title: Optional[str] = None,
            default_value: Optional[str] = None,
            enum_values: Optional[List] = None,
            properties: Optional[Dict] = None,
            required_properties: Optional[List] = None,
            status: Optional[str] = None
    ) -> bool:
        """
        更新组件记录

        Args:
            id: 记录ID
            document_name: 文档名称
            entity_name: 实体名称
            schema_type: 组件类型
            title: 标题/描述
            default_value: 默认值
            enum_values: 枚举值列表
            properties: 属性列表
            required_properties: 必需属性列表
            status: 实体状态

        Returns:
            bool: 是否更新成功
        """
        try:
            update_fields = []
            params = []

            if document_name is not None:
                update_fields.append("document_name = %s")
                params.append(document_name)
            if entity_name is not None:
                update_fields.append("entity_name = %s")
                params.append(entity_name)
            if schema_type is not None:
                update_fields.append("schema_type = %s")
                params.append(schema_type)
            if title is not None:
                update_fields.append("title = %s")
                params.append(title)
            if default_value is not None:
                update_fields.append("default_value = %s")
                params.append(str(default_value)[:255])  # 转换为字符串并限制长度
            if enum_values is not None:
                update_fields.append("enum_values = %s")
                params.append(json.dumps(enum_values))
            if properties is not None:
                update_fields.append("properties = %s")
                params.append(json.dumps(properties))
            if required_properties is not None:
                update_fields.append("required_properties = %s")
                params.append(json.dumps(required_properties))
            if status is not None:
                update_fields.append("status = %s")
                params.append(status)

            if not update_fields:
                return False

            sql = f"""
                UPDATE {self.table}
                SET {", ".join(update_fields)}
                WHERE id = %s
            """
            params.append(id)

            affected_rows = await self.db_pool.execute(sql, params)
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"更新组件记录失败: {str(e)}")
            raise

    async def get_component_record_by_id(self, id: int) -> Dict[str, Any]:
        """
        根据ID获取组件记录

        Args:
            id: 记录ID

        Returns:
            Dict[str, Any]: 组件记录
        """
        try:
            query = f"""
                SELECT * FROM {self.table}
                WHERE id = %s
            """
            result = await self.db_pool.fetch_one(query, (id,))

            if result:
                # 解析JSON字段
                if result.get("enum_values") and isinstance(result["enum_values"], str):
                    result["enum_values"] = json.loads(result["enum_values"])
                if result.get("properties") and isinstance(result["properties"], str):
                    result["properties"] = json.loads(result["properties"])
                if result.get("required_properties") and isinstance(result["required_properties"], str):
                    result["required_properties"] = json.loads(result["required_properties"])
                return result
            return {}

        except Exception as e:
            self.logger.error(f"获取组件记录失败: {str(e)}")
            return {}

    async def get_doc_mappings_by_kb_uid(self, knowledge_base_uid: str) -> list:
        """
        根据知识库uid查询文档，返回去重后的文档名称列表

        Args:
            knowledge_base_uid: 知识库UID

        Returns:
            list: 文档名称列表
        """
        try:
            sql = f"""
                SELECT DISTINCT document_name
                FROM {self.table}
                WHERE knowledge_base_uid = %s
                ORDER BY document_name
            """
            result = await self.db_pool.fetch_all(sql, (knowledge_base_uid,))

            if result:
                doc_list = [row["document_name"] for row in result]
                self.logger.info(f"成功查询知识库 {knowledge_base_uid} 的组件文档列表: {doc_list}")
                return doc_list

            self.logger.warning(f"知识库 {knowledge_base_uid} 下没有找到任何组件文档")
            return []

        except Exception as e:
            self.logger.error(f"查询知识库组件文档列表失败: {str(e)}")
            raise

    async def get_component_mappings_by_entity_name(self, entity_name: str) -> List[Dict[str, Any]]:
        """
        根据实体名称获取组件映射记录列表

        Args:
            entity_name: 实体名称

        Returns:
            List[Dict[str, Any]]: 组件映射记录列表
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE entity_name = %s
            """
            results = await self.db_pool.fetch_all(sql, (entity_name,))

            # 解析所有记录的JSON字段
            for result in results:
                if result.get("enum_values") and isinstance(result["enum_values"], str):
                    result["enum_values"] = json.loads(result["enum_values"])
                if result.get("properties") and isinstance(result["properties"], str):
                    result["properties"] = json.loads(result["properties"])
                if result.get("required_properties") and isinstance(result["required_properties"], str):
                    result["required_properties"] = json.loads(result["required_properties"])

            return results

        except Exception as e:
            self.logger.error(f"获取组件映射记录列表失败: {str(e)}")
            raise

    async def delete_component_mapping(self, id: int) -> bool:
        """
        删除组件映射记录

        Args:
            id: 记录ID

        Returns:
            bool: 是否删除成功
        """
        try:
            sql = f"""
                DELETE FROM {self.table}
                WHERE id = %s
            """
            affected_rows = await self.db_pool.execute(sql, (id,))
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"删除组件映射记录失败: {str(e)}")
            raise

    async def update_status(self, id: int, status: str) -> bool:
        """
        更新组件记录状态

        Args:
            id: 记录ID
            status: 新状态

        Returns:
            bool: 是否更新成功
        """
        try:
            sql = f"""
                UPDATE {self.table}
                SET status = %s
                WHERE id = %s
            """
            affected_rows = await self.db_pool.execute(sql, (status, id))
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"更新组件记录状态失败: {str(e)}")
            raise

    async def get_component_mappings_by_status(self, status: str) -> List[Dict[str, Any]]:
        """
        根据状态获取组件映射记录列表

        Args:
            status: 状态

        Returns:
            List[Dict[str, Any]]: 组件映射记录列表
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE status = %s
            """
            results = await self.db_pool.fetch_all(sql, (status,))

            # 解析所有记录的JSON字段
            for result in results:
                if result.get("enum_values") and isinstance(result["enum_values"], str):
                    result["enum_values"] = json.loads(result["enum_values"])
                if result.get("properties") and isinstance(result["properties"], str):
                    result["properties"] = json.loads(result["properties"])
                if result.get("required_properties") and isinstance(result["required_properties"], str):
                    result["required_properties"] = json.loads(result["required_properties"])

            return results

        except Exception as e:
            self.logger.error(f"获取组件映射记录列表失败: {str(e)}")
            raise

    async def get_component_mappings_by_kb_uid_and_doc_name(
        self,
        knowledge_base_uid: str
    ) -> List[Dict[str, Any]]:
        """
        根据知识库UID查询该知识库下的所有组件记录

        Args:
            knowledge_base_uid: 知识库UID

        Returns:
            List[Dict[str, Any]]: 该知识库下的所有组件记录
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE knowledge_base_uid = %s
                ORDER BY id
            """
            results = await self.db_pool.fetch_all(sql, (knowledge_base_uid,))

            # 解析所有记录的JSON字段
            for result in results:
                if result.get("enum_values") and isinstance(result["enum_values"], str):
                    result["enum_values"] = json.loads(result["enum_values"])
                if result.get("properties") and isinstance(result["properties"], str):
                    result["properties"] = json.loads(result["properties"])
                if result.get("required_properties") and isinstance(result["required_properties"], str):
                    result["required_properties"] = json.loads(result["required_properties"])

            return results

        except Exception as e:
            self.logger.error(f"根据知识库ID查询组件记录失败: {str(e)}")
            raise

    async def get_all_components_by_kb_uid(self, kb_uid: str):
        """
        查询指定知识库下的所有组件记录（不考虑 status）
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE knowledge_base_uid = %s
            """
            results = await self.db_pool.fetch_all(sql, (kb_uid,))

            # 解析JSON字段
            for result in results:
                if result.get("enum_values") and isinstance(result["enum_values"], str):
                    result["enum_values"] = json.loads(result["enum_values"])
                if result.get("properties") and isinstance(result["properties"], str):
                    result["properties"] = json.loads(result["properties"])
                if result.get("required_properties") and isinstance(result["required_properties"], str):
                    result["required_properties"] = json.loads(result["required_properties"])

            return results

        except Exception as e:
            self.logger.error(f"查询组件记录失败: {str(e)}")
            raise

    # async def extract_component_relationships(self, kb_uid: str) -> List[Dict]:
    #     """
    #     提取指定知识库下的所有组件之间的父子关系（基于属性的包含关系）
    #
    #     Args:
    #         kb_uid: 知识库UID
    #
    #     Returns:
    #         List[Dict]: 包含 ID、数据模型名称、父模型、子模型 的关系列表
    #     """
    #     try:
    #         components = await self.get_all_components_by_kb_uid(kb_uid)
    #         if not components:
    #             return []
    #
    #         # 构建组件名到组件对象的映射
    #         component_map = {comp["entity_name"]: comp for comp in components if comp.get("entity_name")}
    #
    #         relationships = []
    #         id_counter = 1  # 手动生成唯一 ID
    #
    #         component_names = list(component_map.keys())
    #
    #         for name_b in component_names:
    #             comp_b = component_map[name_b]
    #             props_b = set(comp_b.get("properties", {}).keys())
    #
    #             parent_model = None
    #             child_model = None
    #
    #             # 查找父模型：即属性包含当前组件所有属性的组件
    #             for name_a in component_names:
    #                 if name_a == name_b:
    #                     continue
    #                 comp_a = component_map[name_a]
    #                 props_a = set(comp_a.get("properties", {}).keys())
    #                 if props_a.issuperset(props_b):  # A ⊇ B → A 是 B 的父模型
    #                     parent_model = name_a
    #                     break  # 每个组件最多只有一个父模型
    #
    #             # 查找子模型：即当前组件是其父模型的组件
    #             for name_c in component_names:
    #                 if name_c == name_b:
    #                     continue
    #                 comp_c = component_map[name_c]
    #                 props_c = set(comp_c.get("properties", {}).keys())
    #                 if props_b.issuperset(props_c):  # B ⊇ C → B 是 C 的父模型 → B 是 C 的父模型，C 是 B 的子模型
    #                     child_model = name_c
    #                     break  # 每个组件最多有一个子模型
    #
    #             relationships.append({
    #                 "ID": id_counter,
    #                 "数据模型名称": name_b,
    #                 "父模型": parent_model,
    #                 "子模型": child_model
    #             })
    #             id_counter += 1
    #
    #         return relationships
    #
    #     except Exception as e:
    #         self.logger.error(f"提取组件关系失败: {str(e)}")
    #         raise

    # async def extract_component_relationships(self, kb_uid: str) -> List[Dict]:
    #     """
    #     提取指定知识库下的所有组件之间的父子关系（基于属性的包含关系）
    #
    #     Args:
    #         kb_uid: 知识库UID
    #
    #     Returns:
    #         List[Dict]: 包含以下字段的组件关系列表：
    #             - id: 唯一标识符
    #             - data_model_name: 数据模型名称
    #             - parent_model: 父模型名称
    #             - child_model: 子模型名称
    #             - properties: 组件的属性字段
    #             - schema_type: 组件类型
    #             - title: 标题/描述
    #             - default_value: 默认值
    #             - enum_values: 枚举值列表
    #             - required_properties: 必需属性列表
    #     """
    #     try:
    #         components = await self.get_all_components_by_kb_uid(kb_uid)
    #         if not components:
    #             return []
    #
    #         # 构建组件名到组件对象的映射
    #         component_map = {comp["entity_name"]: comp for comp in components if comp.get("entity_name")}
    #
    #         relationships = []
    #         id_counter = 1  # 手动生成唯一 ID
    #
    #         component_names = list(component_map.keys())
    #
    #         for name_b in component_names:
    #             comp_b = component_map[name_b]
    #             props_b = set(comp_b.get("properties", {}).keys())
    #
    #             parent_model = None
    #             child_model = None
    #
    #             # 查找父模型：即属性包含当前组件所有属性的组件
    #             for name_a in component_names:
    #                 if name_a == name_b:
    #                     continue
    #                 comp_a = component_map[name_a]
    #                 props_a = set(comp_a.get("properties", {}).keys())
    #                 if props_a.issuperset(props_b):  # A ⊇ B → A 是 B 的父模型
    #                     parent_model = name_a
    #                     break  # 每个组件最多只有一个父模型
    #
    #             # 查找子模型：即当前组件是其父模型的组件
    #             for name_c in component_names:
    #                 if name_c == name_b:
    #                     continue
    #                 comp_c = component_map[name_c]
    #                 props_c = set(comp_c.get("properties", {}).keys())
    #                 if props_b.issuperset(props_c):  # B ⊇ C → B 是 C 的父模型 → C 是 B 的子模型
    #                     child_model = name_c
    #                     break  # 每个组件最多有一个子模型
    #
    #             # 构造完整的关系记录
    #             relationship_entry = {
    #                 "id": id_counter,
    #                 "data_model_name": name_b,
    #                 "parent_model": parent_model,
    #                 "child_model": child_model,
    #                 "properties": comp_b.get("properties"),
    #                 "schema_type": comp_b.get("schema_type"),
    #                 "title": comp_b.get("title"),
    #                 "default_value": comp_b.get("default_value"),
    #                 "enum_values": comp_b.get("enum_values"),
    #                 "required_properties": comp_b.get("required_properties")
    #             }
    #             relationships.append(relationship_entry)
    #             id_counter += 1
    #
    #         return relationships
    #
    #     except Exception as e:
    #         self.logger.error(f"提取组件关系失败: {str(e)}")
    #         raise

    async def extract_component_relationships(self, kb_uid: str) -> List[Dict]:
        """
        提取指定知识库下的所有组件之间的父子关系（基于属性的包含关系）
        """
        try:
            components = await self.get_all_components_by_kb_uid(kb_uid)
            if not components:
                return []

            component_map = {comp["entity_name"]: comp for comp in components if comp.get("entity_name")}

            relationships = []
            id_counter = 1

            component_names = list(component_map.keys())

            for name_b in component_names:
                comp_b = component_map[name_b]

                # 安全获取 properties 字段
                props_b = set((comp_b.get("properties") or {}).keys())

                parent_model = None
                child_model = None

                # 查找父模型
                for name_a in component_names:
                    if name_a == name_b:
                        continue
                    comp_a = component_map[name_a]
                    props_a = set((comp_a.get("properties") or {}).keys())
                    if props_a.issuperset(props_b):
                        parent_model = name_a
                        break

                # 查找子模型
                for name_c in component_names:
                    if name_c == name_b:
                        continue
                    comp_c = component_map[name_c]
                    props_c = set((comp_c.get("properties") or {}).keys())
                    if props_b.issuperset(props_c):
                        child_model = name_c
                        break

                relationship_entry = {
                    "id": id_counter,
                    "data_model_name": name_b,
                    "parent_model": parent_model,
                    "child_model": child_model,
                    "properties": comp_b.get("properties"),
                    "schema_type": comp_b.get("schema_type"),
                    "title": comp_b.get("title"),
                    "default_value": comp_b.get("default_value"),
                    "enum_values": comp_b.get("enum_values"),
                    "required_properties": comp_b.get("required_properties")
                }
                relationships.append(relationship_entry)
                id_counter += 1

            return relationships

        except Exception as e:
            self.logger.error(f"提取组件关系失败: {str(e)}")
            raise

package com.qfnu.common.enums;

import lombok.Getter;

/**
 * 工具发布状态枚举
 */
@Getter
public enum PublishStatusEnum {
    
    UNPUBLISHED(0, "下架"),
    PUBLISHED(1, "上架");

    private final Integer code;
    private final String desc;

    PublishStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PublishStatusEnum getByCode(Integer code) {
        for (PublishStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
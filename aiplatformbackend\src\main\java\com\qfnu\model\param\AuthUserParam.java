package com.qfnu.model.param;


import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.SuperBuilder;
import lombok.EqualsAndHashCode;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode
@ApiModel(value = "AuthUserParam", description = "SysUserManagement参数对象")
public class AuthUserParam {

    private String username;

    private String password;

    private Boolean rememberMe;
}

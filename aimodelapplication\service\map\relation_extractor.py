from template.prompt_template import PromptTemplate
from db.model.model_pool import register_qwen3_model
import re
import json

class ApiComponentRelationExtractor:
    def __init__(self, api_doc: str, component_doc: str, use_deep_thought: bool = False):
        self.api_doc = api_doc
        self.component_doc = component_doc
        self.use_deep_thought = use_deep_thought
        self.client = register_qwen3_model()

    def build_prompt(self) -> str:
        """
        生成用于关系抽取的提示词
        """
        return PromptTemplate.api_component_relationship(self.api_doc, self.component_doc)

    @staticmethod
    def extract_json_from_text(text: str) -> dict:
        """
        从文本中提取第一个合法JSON对象并返回
        """
        match = re.search(r'(\{[\s\S]*\})', text)
        if match:
            try:
                return json.loads(match.group(1))
            except Exception:
                return {}
        return {}

    def extract_relationships(self) -> dict:
        """
        调用大模型进行关系抽取，只返回JSON内容
        """
        prompt = self.build_prompt()
        messages = [
            {"role": "system", "content": "你是一个接口文档关系抽取专家。"},
            {"role": "user", "content": prompt}
        ]
        if not self.client:
            raise RuntimeError("未能获取大模型client，请检查模型服务配置。")
        result = self.client.chat(messages)
        # 取第一个choice的content
        if isinstance(result, dict) and 'choices' in result:
            content = result['choices'][0]['message']['content']
            return self.extract_json_from_text(content)
        return {}
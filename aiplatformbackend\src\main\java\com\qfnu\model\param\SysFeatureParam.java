package com.qfnu.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import java.io.Serializable;
import java.util.Date;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SysFeatureParam", description = "SysFeature参数对象")
public class SysFeatureParam extends BasePage implements Serializable {
    
    private static final long serialVersionUID = 1L;


    private String featureId;
    
    /**
     * 
     */
    @ApiModelProperty("")
    private String description;

    /**
     * 
     */
    @ApiModelProperty("")
    private String creator;

    /**
     * 
     */
    @ApiModelProperty("")
    private String modifier;

    /**
     * 
     */
    @ApiModelProperty("")
    private String deptBelongId;

    /**
     * 
     */
    @ApiModelProperty("")
    private Date updateDatetime;

    /**
     * 
     */
    @ApiModelProperty("")
    private Date createDatetime;

    /**
     * 
     */
    @ApiModelProperty("")
    private String moduleName;

    /**
     * 
     */
    @ApiModelProperty("")
    private String personResponsible;

    /**
     * 
     */
    @ApiModelProperty("")
    private String groupResponsible;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long level;

    /**
     * 
     */
    @ApiModelProperty("")
    private String isDeleted;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long parentId;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long productId;


} 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实API列表的匹配
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from aimodelapplication.service.rerank_service import PrerequisiteMatchingService, RerankService


async def test_real_api_list():
    """测试真实API列表的匹配"""
    
    print("=== 测试真实API列表匹配 ===")
    
    api_key = "sk-fdfd70db7f9b48b4b8cad01c56c3fe99"
    
    # 从日志中提取的真实API列表
    real_api_summaries = [
        {"id": "a3b201d8-7eb2-48e8-a126-fb925bc4ad01", "summary": "审批规则列表", "method": "GET"},
        {"id": "dac401c9-0842-4fbe-b122-3514ab689bba", "summary": "审批规则获取", "method": "GET"},
        {"id": "12f0bbeb-c202-4385-ac40-d1f3caadc8b8", "summary": "审批规则创建", "method": "POST"},
        {"id": "3c2ef112-ff3b-4772-b7f4-5a7cd96bbe90", "summary": "资产账号列表", "method": "GET"},
        {"id": "ecf7c383-f3c8-41a4-911a-165e1debc17a", "summary": "资产账号获取", "method": "GET"},
        # 假设还有其他API
        {"id": "other_1", "summary": "资产账号批量操作", "method": "POST"},
        {"id": "other_2", "summary": "查询应用服务器列表", "method": "GET"},
        {"id": "other_3", "summary": "资产目录列表", "method": "GET"},
        {"id": "other_4", "summary": "资产创建", "method": "POST"},
        {"id": "other_5", "summary": "资产列表", "method": "GET"},
        {"id": "other_6", "summary": "资产账号创建", "method": "POST"},
        {"id": "other_7", "summary": "用户组创建", "method": "POST"},
        {"id": "other_8", "summary": "动态用户组创建", "method": "POST"}
    ]
    
    prerequisite = "创建项目"
    
    print(f"前置依赖: {prerequisite}")
    print(f"真实API数量: {len(real_api_summaries)}")
    print("真实API列表:")
    for i, api in enumerate(real_api_summaries, 1):
        print(f"  {i}. [{api['method']}] {api['summary']}")
    
    # 1. 先直接测试rerank
    print(f"\n=== 1. 直接测试Rerank ===")
    
    try:
        rerank_service = RerankService(api_key)
        
        summaries = [api['summary'] for api in real_api_summaries]
        results = await rerank_service.rerank_summaries(prerequisite, summaries, top_k=10)
        
        print(f"Rerank结果 (前10个):")
        for i, result in enumerate(results, 1):
            print(f"  {i}. {result['summary']} (分数: {result['score']:.4f})")
        
        if results:
            max_score = results[0]['score']
            print(f"\n最高分: {max_score:.4f}")
            
            # 分析是否有合理的匹配
            reasonable_matches = [r for r in results if r['score'] > 0.1]
            print(f"分数>0.1的匹配: {len(reasonable_matches)} 个")
            
            if reasonable_matches:
                print("可能的匹配:")
                for match in reasonable_matches[:3]:
                    print(f"  - {match['summary']} (分数: {match['score']:.4f})")
    
    except Exception as e:
        print(f"Rerank测试失败: {str(e)}")
    
    # 2. 测试完整的匹配服务
    print(f"\n=== 2. 测试完整匹配服务 ===")
    
    try:
        matching_service = PrerequisiteMatchingService(api_key)
        
        print(f"当前阈值: 语义={matching_service.semantic_threshold}, 动词={matching_service.verb_threshold}")
        
        result = await matching_service.find_best_matching_api(prerequisite, real_api_summaries)
        
        if result:
            print(f"✓ 找到匹配:")
            print(f"  API: {result.get('summary')}")
            print(f"  语义相似度: {result.get('semantic_score', 0):.4f}")
            print(f"  动词相似度: {result.get('verb_score', 0):.4f}")
            print(f"  综合相似度: {result.get('final_score', 0):.4f}")
            print(f"  风险级别: {result.get('risk_level')}")
        else:
            print("✗ 未找到匹配")
            
            # 尝试更低的阈值
            print(f"\n尝试更低的阈值...")
            matching_service.semantic_threshold = 0.05
            matching_service.verb_threshold = 0.1
            
            result = await matching_service.find_best_matching_api(prerequisite, real_api_summaries)
            
            if result:
                print(f"✓ 降低阈值后找到匹配:")
                print(f"  API: {result.get('summary')}")
                print(f"  语义相似度: {result.get('semantic_score', 0):.4f}")
                print(f"  动词相似度: {result.get('verb_score', 0):.4f}")
                print(f"  综合相似度: {result.get('final_score', 0):.4f}")
            else:
                print("✗ 即使降低阈值也未找到匹配")
    
    except Exception as e:
        print(f"匹配服务测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_different_prerequisites():
    """测试不同的前置依赖"""
    
    print(f"\n=== 3. 测试不同前置依赖 ===")
    
    api_key = "sk-fdfd70db7f9b48b4b8cad01c56c3fe99"
    
    # 真实API列表
    real_api_summaries = [
        {"id": "1", "summary": "审批规则列表", "method": "GET"},
        {"id": "2", "summary": "审批规则获取", "method": "GET"},
        {"id": "3", "summary": "审批规则创建", "method": "POST"},
        {"id": "4", "summary": "资产账号列表", "method": "GET"},
        {"id": "5", "summary": "资产账号获取", "method": "GET"},
        {"id": "6", "summary": "资产账号批量操作", "method": "POST"},
        {"id": "7", "summary": "资产创建", "method": "POST"},
        {"id": "8", "summary": "资产列表", "method": "GET"},
        {"id": "9", "summary": "资产账号创建", "method": "POST"},
        {"id": "10", "summary": "用户组创建", "method": "POST"}
    ]
    
    # 测试不同的前置依赖
    test_prerequisites = [
        "创建项目",      # 原始的，可能没有匹配
        "创建审批规则",  # 应该能匹配到"审批规则创建"
        "创建资产",      # 应该能匹配到"资产创建"
        "创建账号",      # 应该能匹配到"资产账号创建"
        "创建用户组"     # 应该能匹配到"用户组创建"
    ]
    
    try:
        matching_service = PrerequisiteMatchingService(api_key)
        matching_service.semantic_threshold = 0.1  # 使用较低的阈值
        matching_service.verb_threshold = 0.2
        
        for prerequisite in test_prerequisites:
            print(f"\n--- 测试前置依赖: {prerequisite} ---")
            
            result = await matching_service.find_best_matching_api(prerequisite, real_api_summaries)
            
            if result:
                print(f"✓ 匹配成功: {result.get('summary')} (综合分数: {result.get('final_score', 0):.4f})")
            else:
                print(f"✗ 未找到匹配")
    
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_real_api_list())
    asyncio.run(test_different_prerequisites())

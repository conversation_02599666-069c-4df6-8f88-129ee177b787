import { createFromIconfontCN } from "@ant-design/icons-vue";
import iconEnum from "../../shared/iconEnum";
import { defineComponent, h } from "vue";

const IconFont = createFromIconfontCN({});

export default defineComponent({
  props: {
    type: {
      type: String,
      required: true,
    },
    color: {
      type: String,
    },
    size: {
      type: String,
    },
    className: {
      type: String,
      default: "",
    },
  },
  emits: ["click"],
  setup(props, { emit }) {
    const handleClick = () => emit("click");
    return () => {
      const isIconFont = props.type.startsWith("icon-");
      const Tag = isIconFont ? IconFont : (iconEnum as any)[props.type];
      return h(Tag, {
        type: props.type,
        style: {
          color: props.color,
          fontSize: props.size ? props.size + "px" : undefined,
        },
        class: props.className,
        onClick: handleClick,
      });
    };
  },
}); 
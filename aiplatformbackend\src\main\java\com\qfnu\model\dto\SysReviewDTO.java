package com.qfnu.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import java.io.Serializable;
import java.util.Date;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@ApiModel(value = "SysReviewDTO", description = "SysReview传输对象")
public class SysReviewDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 审核ID（主键）
     */
    @ApiModelProperty("审核ID（主键）")
    private String reviewId;

    /**
     * 关联工具ID
     */
    @ApiModelProperty("关联工具ID")
    private String relatedId;

    /**
     * 提交时间
     */
    @ApiModelProperty("提交时间")
    private Date submissionTime;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    private Date approvalTime;

    /**
     * 审批状态（0=未通过, 1=通过, 2=待审核）
     */
    @ApiModelProperty("审批状态（0=未通过, 1=通过, 2=待审核）")
    private String approvalStatus;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    private String reviewer;

    /**
     * 关联工具信息
     */
    @ApiModelProperty("关联工具信息")
    private SysToolDTO tool;
}
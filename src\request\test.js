export const testApi = {
  common: {
    projectConfig: "/config/project_config",
    syncAll: "/config/global_config/init_self/",
    config: "/config/global_config",
    time: "/config/global_config/get_init_time/",
    //获取产品
    product: "/product/get_product/",
    myProduct: "/product/get_my_product/",
    //获取项目
    project: "/product/get_project/",
    //获取产品及其项目
    productProject: "/product/get_product_project/",
    //获取节假日信息
    holiday: "/metrics/holiday/",
  },
  dev: {
    info: "/init_update/update_config/",
    update: "/init_update/manual/",
    updateAll: "/init_update/manual_all/",
    schedule: "/init_update/dingshi/",
  },
  useCase: {
    productList: "/product/project/",
    platformList: "/product/branch/",
    version: "/product/build/",
    //用例树
    tree: "/2_case/progress/get_case_module/",
    version_test: "/product/build2/",
    //用例结果
    result: "/2_case/progress/case_results/",
    //人员列表
    runner: "/2_case/progress/case_results_user/",
    //用例详情
    caseDetail: "/2_case/progress/case_results_single_row/",
    //执行情况详情
    executionDetail: "/2_case/progress/case_results_single_cell/",
    report: "/case/case_report/",
    detail: "/case/single_case/",
    summaryDetail: "/case/single_case2/",
    testTaskVersion: "/2_case/progress/testtask/",
  },
  progress: {
    tree: "/2_case/progress/case_module/",
    //跳转链接
    zentao: "/2_case/progress/results_single_cell/",

    //特性维度
    statistics: "/2_case/progress/feature_results/",
    detail: "/2_case/progress/feature_results_details/",

    //人员维度
    user: "/2_case/progress/assign_list/",
    userStatistics: "/2_case/progress/assign_results/",
    userDetail: "/2_case/progress/assign_results_details/",

    //版本对比
    contrast: "/2_case/progress/multi_compare/",
    contrastDetail: "/2_case/progress/multi_compare_details/",
  },
  //bug看板
  bug: {
    version: "/2_bug/bug_progress/bug_build/",
    tree: "/2_bug/bug_progress/bug_module/",
    //特性
    statistics: "/2_bug/bug_progress/bug_feature_results/",
    //详情
    detail: "/2_bug/bug_progress/bug_feature_results_details/",
    zentao: "/2_bug/bug_progress/results_single_cell/",

    //对比
    contrast: "/2_bug/bug_progress/multi_compare/",
    contrastDetail: "/2_bug/bug_progress/multi_compare_details/",

    //人员
    user: "/2_bug/bug_progress/assign_list/",
    userStatistics: "/2_bug/bug_progress/assign_results/",
    userDetail: "/2_bug/bug_progress/assign_results_details/",
  },
  //看板
  board: {
    project: "/bug/project/",
    execution: "/bug/execution/",
    kanban: "/bug/kanban/",
    kanbanNum: "/bug/kanban_num/",
    cell: "/bug/cell/",
    info: "/bug/kanban_cell/",
    update: "/bug/update_zt_bug/",
    user: "/bug/kanban_user/",
    chartInfo: "/bug/bugchart/details_234/",
    retest: "/bug/test_task_details/",
    retest_chandao: "/bug/test_task_details_single/",
  },
  // 项目看板
  product: {
    projectSummary: "/product_board/staging/project_summary/",
    projectBoard: "/product_board/staging/project_board/",
    projectDetail: "/product_board/staging/project_detail/",
    project: "/product_board/project_progress/project_table/",
    detailBuild: "/product_board/project_progress/detail/build",
    detailBug: "/product_board/project_progress/detail/bug/",
    detailStory: "/product_board/project_progress/detail/story/",
    detailCase: "/product_board/project_progress/detail/case/",
    product_chandao: "/product_board/project_progress/results_single_cell/",
    // story_info: "/product_board/project_progress/story_table/",
    // case_info: "/product_board/project_progress/case_table/",
    bug_info: "/product_board/project_progress/active_bug_table/",
    remain_info: "/product_board/project_progress/remain_bug_table/",
  },
  //报告
  report: {
    allReport: "/case/all_report",
    info: "/case/report_get",
    delete: "/case/report_delete",
    export: "/case/report_output/",
    import: "/case/report_input/",
    join: "/case/input_join/",
    all: "/case/all_report/",
    save: "/case/report_input_save/",
    create: "/case/report/",
    saveTemp: "/case/report_save/",
  },
  chart: {
    status: "/bug/bugchart/substatus/",
    user: "/bug/bugchart/assigned/",
    bug: "/bug/bugchart/newandresolve/",
    bugStack: "/bug/bugchart/bug_module_chart/",
    retest: "/bug/test_task_results/",
    catchBug: "/bug/bug_rank_results/",
  },
  //度量—配置(公共数据查询)
  metricsConfig: {
    publicData: "/metrics/metrics_progress/public_date/",
  },
  //度量—测试
  metricsTest: {
    //版本维度
    multiCompare: "/metrics/metrics_progress/multi_compare/",
    //个人维度
    personCompare: "/metrics/metrics_progress/person_compare/",
    timeStatement: "/metrics/metrics_progress/time_statement/",
    personPerformance: "/metrics/metrics_progress/get_person_performance/",
  },
  //度量—开发
  metricsDevelop: {},
  //度量—月度报表
  metricsStatement: {},
  //团队绩效考核
  productPerformance: {
    getTeamData: "/metrics/metrics_progress/get_team_data/",
  },
  //度量—工时展示
  metricsWorkHour: {
    //工时查询and录入
    worktime: "/metrics/metrics_progress/work_time/",
    //用户查询
    teamUser: "/metrics/metrics_progress/get_teamuser/",
    //工时统计
    workTimeCount: "/metrics/metrics_progress/get_worktime/",
    worktimeUser: "/metrics/metrics_progress/work_time/user",
    worktimeProject: "/metrics/metrics_progress/work_time/project",
    //获取产品组列表
  },
  login: {
    signin: "/user/signin_token/",
    signout: "/user/signout/",
  },
  user: {
    userMsg: "/user/user_msg/",
    usersMsg: "/user/users_msg/",
    //用户根据类型分类
    usersMsgByType: "/user/users_msg/type",
    delete: "/user/delete",
    create: "/user/create/",
    passwordChange: "/user/change_password/",
    passwordReset: "/user/reset_password/",
    setAdmin: "/user/role_set/",
  },
  config: {
    bug_module: "/config/self_config/",
    //获取测试单
    testTask: "/config/self_config/case_testtask/",
    //获取测试单-编辑
    testTaskEdit: "/config/self_config/change_case_testtask/",

    //获取bug目录-新建
    bugCatalog: "/config/self_config/bug_module/",
    //获取bug目录-编辑
    bugCatalogEdit: "/config/self_config/change_bug_module/",

    //获取bug版本-新建
    version: "/config/self_config/create_build/",
    //获取bug版本-编辑
    versionEdit: "/config/self_config/change_build/",
    //获取产品组
    productTeamList: "/metrics/metrics_progress/get_product_team/",
    // 团队考核配置 获取产品列表
    productList: "/product/get_product_project/",
    //获取产品组绩效考核配置信息
    productTeamConfig: "/metrics/metrics_progress/performance_data/",
    //获取人员信息
    userList: "/metrics/metrics_progress/get_person_data/",
  },
}

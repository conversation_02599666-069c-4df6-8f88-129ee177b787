package com.qfnu.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qfnu.common.enums.DeleteStatusEnum;
import com.qfnu.common.enums.PublishStatusEnum;
import com.qfnu.common.enums.ReviewStatusEnum;
import com.qfnu.converter.SysToolConverter;
import com.qfnu.mapper.primary.SysReviewMapper;
import com.qfnu.mapper.primary.SysUserManagementMapper;
import com.qfnu.model.dto.SysReviewDTO;
import com.qfnu.model.dto.SysToolDTO;
import com.qfnu.model.param.SysReviewParam;
import com.qfnu.model.param.SysToolQueryParam;
import com.qfnu.model.po.SysReviewPO;
import com.qfnu.model.po.SysToolPO;
import com.qfnu.model.po.SysUserManagementPO;
import com.qfnu.model.vo.SysReviewVO;
import com.qfnu.model.vo.SysToolVO;
import com.qfnu.model.param.SysToolParam;
import com.qfnu.mapper.primary.SysToolMapper;
import com.qfnu.converter.BaseConverter;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

@Service
public class SysToolService extends ServiceImpl<SysToolMapper, SysToolPO> {

    @Resource
    private SysToolMapper sysToolMapper;

    @Autowired
    private SysReviewMapper sysReviewMapper;

    @Autowired
    private SysToolConverter sysToolConverter;

    @Autowired
    private SysUserManagementMapper sysUserManagementMapper;



    @Transactional(rollbackFor = Exception.class)
    public int save(SysToolParam param,String userId) {
        // 生成工具ID
        String toolId = UUID.randomUUID().toString();
        param.setToolId(toolId);

        SysUserManagementPO po1 = sysUserManagementMapper.getUserById(userId);

        param.setCreators(po1.getRealName());

        param.setUserIds(po1.getUserId());

        // 创建审核记录
        SysReviewPO sysReviewPO = new SysReviewPO();
        sysReviewPO.setRelatedId(toolId);
        sysReviewPO.setSubmissionTime(new Date());
        sysReviewPO.setReviewId(UUID.randomUUID().toString());
        sysReviewPO.setApprovalStatus(String.valueOf(ReviewStatusEnum.PENDING.getCode()));

        // 保存审核记录
        sysReviewMapper.insert(sysReviewPO);

        // 保存工具信息，设置初始状态为待审核
        SysToolPO po = sysToolConverter.toSysToolPO(param);
        po.setReviewStatus(String.valueOf(ReviewStatusEnum.PENDING.getCode()));
        return save(po) ? 1 : 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public int saveBatch(List<SysToolParam> paramList) {
        List<SysToolPO> poList = sysToolConverter.toSysToolPOList(paramList);
        return saveBatch(poList) ? poList.size() : 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public int update(SysToolParam param) {
        SysToolPO po = sysToolConverter.toSysToolPO(param);
        return updateById(po) ? 1 : 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(String id) {
        SysToolParam param = new SysToolParam();
        param.setToolId(id);
        param.setIsDeleted(String.valueOf(DeleteStatusEnum.DELETED.getCode()));
        return sysToolMapper.myDelete(param) ? 1 : 0;
    }

    public SysToolVO getById(String id) {
        SysToolPO po = super.getById(id);
        if (po == null) {
            return null;
        }
        return sysToolConverter.toSysToolVO(po);
    }

    public List<SysToolVO> getList(SysToolParam param) {
        SysToolPO po = sysToolConverter.toSysToolPO(param);
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<SysToolPO> wrapper = new LambdaQueryWrapper<>();
        // TODO: 根据实际需求添加查询条件
        List<SysToolPO> poList = list(wrapper);
        return sysToolConverter.toSysToolVOList(poList);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<SysToolVO> batchAddOrUpdate(List<SysToolParam> paramList) {
        List<SysToolPO> poList = sysToolConverter.toSysToolPOList(paramList);
        saveOrUpdateBatch(poList);
        return sysToolConverter.toSysToolVOList(poList);
    }


    public IPage<SysToolVO> getToolsList(SysToolQueryParam sysToolQueryParam) {
        // 创建分页对象
        Page<SysToolDTO> page = new Page<>(sysToolQueryParam.getPageNum() != null ? sysToolQueryParam.getPageNum() : 1,
                sysToolQueryParam.getPageSize() != null ? sysToolQueryParam.getPageSize() : 10);

        Long offset = (long) (sysToolQueryParam.getPageSize() * (sysToolQueryParam.getPageNum() - 1));

        sysToolQueryParam.setOffset(offset);

        IPage<SysToolDTO> dtoPage = sysToolMapper.getToolsList(page, sysToolQueryParam);

        Integer count = sysToolMapper.countTotal(page,sysToolQueryParam);

        // 转换为VO对象
        Page<SysToolVO> voPage = new Page<>();
        voPage.setTotal(count);
        voPage.setCurrent(dtoPage.getCurrent());
        voPage.setSize(dtoPage.getSize());
        voPage.setRecords(sysToolConverter.toSysToolVOListFromDTO(dtoPage.getRecords()));
        return voPage;
    }

    @Transactional(rollbackFor = Exception.class)
    public int publishTool(String toolId) {
        SysToolPO po = super.getById(toolId);
        if (po == null) {
            return 0;
        }
        // 检查工具审核状态是否为已通过
        if (!String.valueOf(ReviewStatusEnum.APPROVED.getCode()).equals(po.getReviewStatus())) {
            return 0;
        }
        po.setIsPublished(String.valueOf(PublishStatusEnum.PUBLISHED.getCode()));
        return sysToolMapper.myUpdateById(po);
    }

    @Transactional(rollbackFor = Exception.class)
    public int unpublishTool(String toolId) {
        SysToolPO po = sysToolMapper.getToolById(toolId);
        if (po == null) {
            return 0;
        }
        po.setIsPublished(String.valueOf(PublishStatusEnum.UNPUBLISHED.getCode()));
        return sysToolMapper.myUpdateById(po);
    }



    public IPage<SysToolVO> getMyToolList(SysToolQueryParam sysToolQueryParam) {
        // 创建分页对象
        Page<SysToolVO> page = new Page<>(sysToolQueryParam.getPageNum() != null ? sysToolQueryParam.getPageNum() : 1,
                sysToolQueryParam.getPageSize() != null ? sysToolQueryParam.getPageSize() : 10);

        // 调用Mapper层执行查询
        IPage<SysToolVO> voPage = sysToolMapper.getMyToolList(page, sysToolQueryParam);
        return voPage;
    }


}

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import Antd from 'ant-design-vue'
import "ant-design-vue/dist/reset.css"
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import Loading from './utils/loading'

// main.js
import './tailwind.css'
// Font Awesome
import '@fortawesome/fontawesome-free/css/all.css'

const app = createApp(App)

app.use(router)
app.use(Antd)
app.use(ElementPlus)
app.use(Loading)

app.config.globalProperties.$loading = Loading

app.mount('#app')



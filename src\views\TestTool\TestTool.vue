<template>
  <div class="min-h-screen bg-gray-50">
    <div class="flex min-h-screen">
      <SubNavigation v-model:isCollapsed="isCollapsed" />
      <!-- 主内容区域 -->
      <main :class="['flex-1 p-6', isCollapsed ? 'ml-16' : 'ml-48', 'transition-all duration-300', 'overflow-hidden']">
        <Breadcrumb />
        <!-- 页面标题和搜索栏 -->
  
        <!-- 子路由视图 -->
        <div class="w-full overflow-hidden">
          <router-view></router-view>
        </div>
    
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import SubNavigation from '@/components/navigation/SubNavigation.vue';
import Breadcrumb from '@/components/navigation/Breadcrumb.vue';

const isCollapsed = ref(false);


</script>

<style scoped>
.rounded-button {
  border-radius: 9999px;
}

/* 确保主内容区域正确处理overflow */
main {
  min-width: 0;
  max-width: 100%;
}

/* 确保子路由容器正确处理内容 */
main > div {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}
</style>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qfnu.mapper.primary.SysUsersMapper">

    <resultMap id="BaseResultMap" type="com.qfnu.model.po.SysUsersPO">
        <id column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="email" property="email"/>
        <result column="job_no" property="jobNo"/>
        <result column="team_names" property="teamNames"/>
        <result column="first_name" property="firstName"/>
        <result column="last_name" property="lastName"/>
        <result column="is_superuser" property="isSuperuser"/>
        <result column="is_staff" property="isStaff"/>
        <result column="is_active" property="isActive"/>
        <result column="date_joined" property="dateJoined"/>
        <result column="last_login" property="lastLogin"/>
    </resultMap>

    <sql id="Base_Column_List">
        user_id, username, password, email, job_no, team_names, first_name, last_name, is_superuser, is_staff, is_active, date_joined, last_login
    </sql>

    <select id="getUserByUseranme" parameterType="string" resultType="com.qfnu.model.dto.AuthUserDto">
        SELECT su.*, sumt.role, sumt.real_name as realName, sumt.user_type as userType, sumt.product
        FROM sys_users su
                 LEFT JOIN sys_user_management sumt ON su.user_id = sumt.user_id
        WHERE su.username = #{username};
    </select>

    <select id="getUserByJobNo" parameterType="string" resultType="com.qfnu.model.dto.AuthUserDto">
        SELECT su.*, sumt.role, sumt.real_name as realName, sumt.user_type as userType, sumt.product
        FROM sys_users su
                 LEFT JOIN sys_user_management sumt ON su.user_id = sumt.user_id
        WHERE su.job_no = #{jobNo};
    </select>

    <select id="getUserById" parameterType="string" resultType="com.qfnu.model.dto.AuthUserDto">
        SELECT su.*, sumt.role, sumt.real_name as realName, sumt.user_type as userType, sumt.product
        FROM sys_users su
                 LEFT JOIN sys_user_management sumt ON su.user_id = sumt.user_id
        WHERE su.user_id = #{userId};
    </select>

    <update id="MyupdateById">
        update sys_users
        <set>
            <if test="lastLogin != null">
                last_login = #{lastLogin},
            </if>
            <if test="email != null">
                email = #{email},
            </if>
            <if test="jobNo != null">
                job_no = #{jobNo},
            </if>
            <if test="teamNames != null">
                team_names = #{teamNames},
            </if>
            <if test="firstName != null">
                first_name = #{firstName},
            </if>
            <if test="lastName != null">
                last_name = #{lastName},
            </if>
            <if test="password != null">
                password = #{password},
            </if>
        </set>
        WHERE user_id = #{userId}
    </update>

    <delete id="myDeleteById" parameterType="string">
        delete from sys_users where user_id = #{userId}
    </delete>

    <select id="count" resultType="int">
        select count(*) from sys_users
    </select>



</mapper>
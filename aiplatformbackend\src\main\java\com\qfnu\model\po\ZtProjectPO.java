package com.qfnu.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("zt_project")
@ApiModel(value = "ZtProjectPO", description = "ZtProject实体类")
public class ZtProjectPO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 
     */
    @TableField(value = "type")
    @ApiModelProperty(value = "")
    private String type;

    /**
     * 
     */
    @TableField(value = "path")
    @ApiModelProperty(value = "")
    private String path;

    /**
     * 
     */
    @TableField(value = "grade")
    @ApiModelProperty(value = "")
    private Long grade;

    /**
     * 
     */
    @TableField(value = "project")
    @ApiModelProperty(value = "")
    private Long project;

    /**
     * 
     */
    @TableField(value = "name")
    @ApiModelProperty(value = "")
    private String name;

    /**
     * 
     */
    @TableField(value = "parent")
    @ApiModelProperty(value = "")
    private Long parent;

    /**
     * 
     */
    @TableField(value = "status")
    @ApiModelProperty(value = "")
    private String status;

    /**
     * 
     */
    @TableField(value = "substatus")
    @ApiModelProperty(value = "")
    private String substatus;

    /**
     * 
     */
    @TableField(value = "pri")
    @ApiModelProperty(value = "")
    private String pri;

    /**
     * 
     */
    @TableField(value = "`desc`")
    @ApiModelProperty(value = "")
    private String desc;

    /**
     * 
     */
    @TableField(value = "`order`")
    @ApiModelProperty(value = "")
    private String order;

    /**
     * 
     */
    @TableField(value = "deleted")
    @ApiModelProperty(value = "")
    private String deleted;

    /**
     * 
     */
    @TableField(value = "`begin`")
    @ApiModelProperty(value = "")
    private Date begin;

    /**
     * 
     */
    @TableField(value = "`end`")
    @ApiModelProperty(value = "")
    private Date end;

    /**
     * 
     */
    @TableField(value = "PM")
    @ApiModelProperty(value = "")
    private String PM;

    /**
     * 
     */
    @TableField(value = "model")
    @ApiModelProperty(value = "")
    private String model;


} 
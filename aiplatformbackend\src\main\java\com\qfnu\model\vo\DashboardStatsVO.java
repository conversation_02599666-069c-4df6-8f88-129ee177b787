package com.qfnu.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("首页统计数据VO")
public class DashboardStatsVO {

    @ApiModelProperty("工具总数")
    private Integer totalTools;

    @ApiModelProperty("累计使用次数")
    private Integer totalUsage;

    @ApiModelProperty("活跃用户数")
    private Integer activeUsers;

    @ApiModelProperty("平均评分")
    private Double averageRating;

}
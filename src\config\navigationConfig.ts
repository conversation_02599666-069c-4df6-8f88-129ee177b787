import { RouteRecordRaw } from 'vue-router';

interface NavigationItem {
  path: string;
  name: string;
  icon: string;
  children?: NavigationItem[];
}

export const navigationConfig: NavigationItem[] = [
  {
    path: '/ai-efficiency',
    name: 'AI提效',
    icon: 'fas fa-robot',
    children: [
      {
        path: '/ai-efficiency/apps',
        name: 'AI应用',
        icon: 'fas fa-cube'
      },
      {
        path: '/ai-efficiency/models',
        name: 'AI模型',
        icon: 'fas fa-brain'
      },
      {
        path: '/ai-efficiency/knowledge',
        name: '知识库',
        icon: 'fas fa-book'
      },
      {
        path: '/ai-efficiency/mcp',
        name: 'MCP',
        icon: 'fas fa-microchip'
      }
    ]
  },
  {
    path: '/test-tools',
    name: '测试工具',
    icon: 'fas fa-tools',
    children: [
      {
        path: '/test-tools/platform',
        name: '测试平台',
        icon: 'fas fa-desktop'
      },
      {
        path: '/test-tools/statistics',
        name: '度量看板(待上线)',
        icon: 'fas fa-chart-bar',
        children: [
          {
            path: '/test-tools/statistics/gantt',
            name: '任务看板',
            icon: 'fas fa-project-diagram'
          }
        ]
      },
      {
        path: '/test-tools/itr',
        name: 'ITR',
        icon: 'fas fa-table',
        children: [
          {
            path: '/test-tools/itr/analysis',
            name: 'ITR分析表',
            icon: 'fas fa-chart-line'
          },
          {
            path: '/test-tools/itr/report',
            name: '数据填报',
            icon: 'fas fa-pen'
          },
          {
            path: '/test-tools/itr/feature-management',
            name: '特性管理',
            icon: 'fas fa-flag'
          }
        ]
      }
    ]
  },
  {
    path: '/system',
    name: '系统配置',
    icon: 'fas fa-cog',
    children: [
      {
        path: '/system/toolManagement',
        name: '工具管理',
        icon: 'fas fa-wrench'
      },
      {
        path: '/system/basicSettings',
        name: '基本配置',
        icon: 'fas fa-sliders-h',
        children: [
          {
            path: '/system/basicSettings/user-management',
            name: '用户管理',
            icon: 'fas fa-users'
          },
          {
            path: '/system/basicSettings/feature-management',
            name: '特性管理',
            icon: 'fas fa-flag'
          }
        ]
      }
    ]
  }
];
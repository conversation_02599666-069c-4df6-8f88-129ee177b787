<template>
  <a-layout class="layout">
    <Header @publish-tool="showPublishTool" />
    <PublishTool v-model:visible="publishToolVisible" />
    <a-layout-content class="content">
      <router-view></router-view>
    </a-layout-content>
  </a-layout>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import Header from './Header.vue';
import PublishTool from '../PublishTool.vue';

const publishToolVisible = ref(false);

const showPublishTool = () => {
  publishToolVisible.value = true;
};
</script>

<style scoped>
.layout {
  min-height: 100vh;
  background: #f0f2f5;
}

.content {
  margin-top: 30px;
  padding: 12px 2px;
  min-height: calc(100vh - 48px);
}
</style>
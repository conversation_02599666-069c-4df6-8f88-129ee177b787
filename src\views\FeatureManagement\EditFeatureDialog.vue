<template>
  <a-modal
    v-model:visible="visible"
    title="编辑特性"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirmLoading="loading"
  >
    <a-form :model="formState" :rules="rules" ref="formRef">
      <a-form-item label="产品类型" name="productId">
        <a-select 
          v-model:value="formState.productId" 
          placeholder="请选择产品类型"
          @change="handleProductChange"
        >
          <a-select-option v-for="(label, value) in productTypeMapping" :key="value" :value="Number(value)">
            {{ label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="特性名称" name="moduleName">
        <a-input v-model:value="formState.moduleName" placeholder="请输入特性名称" />
      </a-form-item>
      <a-form-item label="所属目录" name="parentId">
        <a-cascader
          v-model:value="formState.parentId"
          :options="treeData"
          placeholder="请选择所属目录"
          :disabled="!formState.productId"
          :fieldNames="{
            label: 'moduleName',
            value: 'featureId',
            children: 'children'
          }"
          :changeOnSelect="true"
        />
      </a-form-item>
      <a-form-item label="责任组" name="groupResponsible">
        <a-input v-model:value="formState.groupResponsible" placeholder="请输入责任组" />
      </a-form-item>
      <a-form-item label="责任人" name="personResponsible">
        <a-input v-model:value="formState.personResponsible" placeholder="请输入责任人" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { productTypeMapping } from '@/utils/productTypeMapping';
import { updateFeature, getFeatureTree } from '@/utils/api';
import type { FormInstance } from 'ant-design-vue';
import $loading from '@/utils/loading';

const visible = ref(false);
const loading = ref(false);
const formRef = ref<FormInstance>();

const formState = reactive({
  featureId: '',
  productId: undefined,
  moduleName: '',
  parentId: undefined,
  groupResponsible: '',
  personResponsible: ''
});

// 目录树数据
const treeData = ref([]);

// 产品类型变更处理
const handleProductChange = async (value: number) => {
  if (value) {
    try {
      const data = await getFeatureTree({
        productId: Number(value)
      });
      treeData.value = data;
    } catch (error) {
      message.error('获取特性树失败');
      console.error(error);
    }
  } else {
    treeData.value = [];
    formState.parentId = undefined;
  }
};

const rules = {
  productId: [{ required: true, message: '请选择产品类型' }],
  moduleName: [{ required: true, message: '请输入特性名称' }],
  parentId: [{ required: true, message: '请选择所属目录' }],
  groupResponsible: [],
  personResponsible: [{ required: true, message: '请输入责任人' }]
};

const emit = defineEmits(['refresh']);

const showModal = (record: any) => {
  Object.assign(formState, {
    ...record,
    parentId: record.parentId ? [record.parentId] : undefined
  });
  if (record.productId) {
    handleProductChange(record.productId);
  }
  visible.value = true;
};

const handleOk = () => {
  formRef.value?.validate().then(async () => {
    const loadingInstance = $loading.show();
    loading.value = true;
    try {
      await updateFeature(formState);
      message.success('更新特性成功');
      visible.value = false;
      emit('refresh');
    } catch (error) {
      message.error('更新特性失败');
    } finally {
      loading.value = false;
      loadingInstance.close();
    }
  });
};

const handleCancel = () => {
  visible.value = false;
};

defineExpose({
  showModal
});
</script>
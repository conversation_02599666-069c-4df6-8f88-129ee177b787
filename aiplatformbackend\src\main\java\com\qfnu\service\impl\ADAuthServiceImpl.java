package com.qfnu.service.impl;

import com.qfnu.model.dto.ADTokenResponseDTO;
import com.qfnu.model.dto.ADUserInfoDTO;
import com.qfnu.model.dto.AuthUserDto;
import com.qfnu.model.po.SysUsersPO;
import com.qfnu.model.po.SysUserManagementPO;
import com.qfnu.service.ADAuthService;
import com.qfnu.service.SysUserManagementService;
import com.qfnu.service.SysUsersService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientException;
import reactor.core.publisher.Mono;

import java.util.Date;
import java.util.Map;
import java.util.UUID;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

@Slf4j
@Service
public class ADAuthServiceImpl implements ADAuthService {

    @Autowired
    private SysUsersService sysUsersService;

    @Autowired
    private SysUserManagementService sysUserManagementService;

    @Value("${oauth.auth.token-url:https://tam.your-company.com/oauth2/token}")
    private String oauthTokenUrl;

    @Value("${oauth.auth.userinfo-url:https://tam.your-company.com/oauth2/userinfo}")
    private String oauthUserInfoUrl;

    @Value("${ad.auth.timeout:10000}")
    private int timeout;

    private final WebClient webClient;

    public ADAuthServiceImpl() {
        // 使用简单的WebClient配置，避免复杂的SSL配置
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024))
                .build();
    }

    @Override
    public ADTokenResponseDTO exchangeTokenWithCode(String code, String clientId, String clientSecret, String redirectUri) {
        log.info("开始使用授权码换取OAuth用户信息，code: {}", code);

        System.out.println("=== ADAuthService.exchangeTokenWithCode 开始 ===");
        System.out.println("输入参数:");
        System.out.println("  - code: " + (code != null ? code.substring(0, Math.min(code.length(), 20)) + "..." : "null"));
        System.out.println("  - clientId: " + clientId);
        System.out.println("  - clientSecret: " + (clientSecret != null ? clientSecret.substring(0, 3) + "***" : "null"));
        System.out.println("  - redirectUri: " + redirectUri);
        System.out.println("配置的URL:");
        System.out.println("  - oauthTokenUrl: " + oauthTokenUrl);
        System.out.println("  - oauthUserInfoUrl: " + oauthUserInfoUrl);

        try {
            // 第一步：使用授权码换取访问令牌
            System.out.println("步骤1: 开始换取访问令牌...");
            String accessToken = exchangeCodeForAccessToken(code, clientId, clientSecret, redirectUri);
            System.out.println("步骤1: 访问令牌获取成功: " + (accessToken != null ? accessToken.substring(0, Math.min(accessToken.length(), 20)) + "..." : "null"));

            // 第二步：使用访问令牌获取用户信息
            System.out.println("步骤2: 开始获取用户信息...");
            ADUserInfoDTO userInfo = getUserInfoWithAccessToken(accessToken);
            System.out.println("步骤2: 用户信息获取成功");

            // 构建响应
            ADTokenResponseDTO response = ADTokenResponseDTO.builder()
                    .code(0)
                    .msg("成功")
                    .content(userInfo)
                    .build();

            log.info("OAuth用户信息获取成功，用户: {}", userInfo.getAccount());
            System.out.println("=== ADAuthService.exchangeTokenWithCode 成功结束 ===");
            return response;

        } catch (Exception e) {
            log.error("调用OAuth认证服务失败", e);
            System.out.println("=== ADAuthService.exchangeTokenWithCode 异常结束 ===");
            System.out.println("异常信息: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("OAuth认证服务调用失败: " + e.getMessage());
        }
    }

    /**
     * 测试网络连接
     */
    private void testNetworkConnection(String url) {
        try {
            System.out.println("测试网络连接到: " + url);
            // 简化网络测试，只记录尝试
            System.out.println("网络连接测试：准备连接到TAM服务器");
        } catch (Exception e) {
            System.out.println("网络连接测试异常");
        }
    }

    /**
     * 使用授权码换取访问令牌
     */
    private String exchangeCodeForAccessToken(String code, String clientId, String clientSecret, String redirectUri) {
        try {
            // 构建请求参数 - 根据OAuth3.0文档，不包含redirect_uri
            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            formData.add("grant_type", "authorization_code");
            formData.add("code", code);
            formData.add("client_id", clientId);
            formData.add("client_secret", clientSecret);
            // OAuth3.0文档中没有要求redirect_uri参数，所以不添加

            System.out.println("=== OAuth3.0 Token请求参数 ===");
            System.out.println("grant_type: authorization_code");
            System.out.println("client_id: " + clientId);
            System.out.println("client_secret: ***");
            System.out.println("code: " + (code != null ? code.substring(0, Math.min(code.length(), 10)) + "..." : "null"));
            System.out.println("请求URL: " + oauthTokenUrl);

            // 使用 WebClient 发送请求
            return webClient.post()
                    .uri(oauthTokenUrl)
                    .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                    .body(BodyInserters.fromFormData(formData))
                    .retrieve()
                    .bodyToMono(Map.class)
                    .map(response -> (String) response.get("access_token"))
                    .block();
        } catch (WebClientException e) {
            log.error("WebClient请求异常: {}", e.getMessage(), e);
            // 尝试使用备用HTTP客户端
            return exchangeCodeForAccessTokenWithHttpClient(code, clientId, clientSecret, redirectUri);
        } catch (RuntimeException e) {
            log.error("运行时异常: {}", e.getMessage(), e);
            // 尝试使用备用HTTP客户端
            return exchangeCodeForAccessTokenWithHttpClient(code, clientId, clientSecret, redirectUri);
        }
    }

    /**
     * 使用RestTemplate作为备用方案换取访问令牌
     */
    private String exchangeCodeForAccessTokenWithHttpClient(String code, String clientId, String clientSecret, String redirectUri) {
        try {
            System.out.println("--- 使用备用RestTemplate换取访问令牌 ---");

            // 构建请求体
            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            formData.add("grant_type", "authorization_code");
            formData.add("code", code);
            formData.add("client_id", clientId);
            formData.add("client_secret", clientSecret);
            if (redirectUri != null && !redirectUri.isEmpty()) {
                formData.add("redirect_uri", redirectUri);
            }

            System.out.println("RestTemplate请求参数:");
            formData.forEach((key, value) -> {
                if ("client_secret".equals(key)) {
                    System.out.println("  " + key + ": ***");
                } else {
                    System.out.println("  " + key + ": " + value);
                }
            });

            // 创建RestTemplate
            RestTemplate restTemplate = new RestTemplate();

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            // 创建请求实体
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(formData, headers);

            System.out.println("发送RestTemplate请求到: " + oauthTokenUrl);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    oauthTokenUrl,
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            System.out.println("RestTemplate响应状态码: " + response.getStatusCode());
            System.out.println("RestTemplate响应体: " + response.getBody());

            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new RuntimeException("HTTP错误: " + response.getStatusCode() + ", 响应: " + response.getBody());
            }

            // 解析响应
            Map<String, Object> responseBody = response.getBody();
            if (responseBody != null && responseBody.containsKey("access_token")) {
                String accessToken = (String) responseBody.get("access_token");
                if (accessToken != null && !accessToken.isEmpty()) {
                    System.out.println("RestTemplate成功获取访问令牌: " + accessToken.substring(0, Math.min(accessToken.length(), 20)) + "...");
                    return accessToken;
                }
            }

            throw new RuntimeException("响应中未找到access_token: " + responseBody);

        } catch (Exception e) {
            System.out.println("RestTemplate请求失败: " + e.getClass().getSimpleName() + " - " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("RestTemplate请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用访问令牌获取用户信息
     */
    private ADUserInfoDTO getUserInfoWithAccessToken(String accessToken) {
        try {
            System.out.println("--- getUserInfoWithAccessToken 开始 ---");
            System.out.println("请求URL: " + oauthUserInfoUrl);
            System.out.println("访问令牌: " + (accessToken != null ? accessToken.substring(0, Math.min(accessToken.length(), 20)) + "..." : "null"));

            // 调用OAuth userinfo端点
            Mono<Map> responseMono = webClient.get()
                    .uri(oauthUserInfoUrl)
                    .header("Authorization", "Bearer " + accessToken)
                    .retrieve()
                    .bodyToMono(Map.class);

            Map<String, Object> userInfoResponse = responseMono.block();

            System.out.println("UserInfo响应:");
            if (userInfoResponse != null) {
                userInfoResponse.forEach((key, value) -> {
                    System.out.println("  " + key + ": " + value);
                });
            } else {
                System.out.println("  响应为空");
            }

            if (userInfoResponse == null) {
                throw new RuntimeException("OAuth userinfo服务无响应");
            }

            // 构建用户信息DTO
            ADUserInfoDTO userInfo = ADUserInfoDTO.builder()
                    .sub((String) userInfoResponse.get("sub"))
                    .preferredUsername((String) userInfoResponse.get("preferred_username"))
                    .account((String) userInfoResponse.get("account"))  // 使用account字段
                    .name((String) userInfoResponse.get("name"))
                    .fullName((String) userInfoResponse.get("name"))
                    .email((String) userInfoResponse.get("email"))
                    .mobile((String) userInfoResponse.get("mobile"))
                    .jobNo((String) userInfoResponse.get("jobNo"))
                    .teamNames((String) userInfoResponse.get("teamNames"))
                    .status(1) // 默认状态为正常
                    .build();

            System.out.println("构建的用户信息:");
            System.out.println("  account: " + userInfo.getAccount());
            System.out.println("  name: " + userInfo.getName());
            System.out.println("  email: " + userInfo.getEmail());
            System.out.println("  mobile: " + userInfo.getMobile());
            System.out.println("  jobNo: " + userInfo.getJobNo());
            System.out.println("--- getUserInfoWithAccessToken 成功结束 ---");

            return userInfo;

        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            System.out.println("--- getUserInfoWithAccessToken 异常结束 ---");
            System.out.println("异常: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("获取用户信息失败: " + e.getMessage());
        }
    }

    @Override
    public AuthUserDto matchOrCreateUser(ADUserInfoDTO adUserInfo) {
        log.info("开始匹配或创建用户，AD用户: {}", adUserInfo.getAccount());

        AuthUserDto user = null;

        // 1. 尝试通过邮箱匹配
        if (adUserInfo.getEmail() != null && !adUserInfo.getEmail().isEmpty()) {
            user = findUserByEmail(adUserInfo.getEmail());
        }

        // 2. 尝试通过工号匹配
        if (user == null && adUserInfo.getJobNo() != null && !adUserInfo.getJobNo().isEmpty()) {
            user = findUserByJobNo(adUserInfo.getJobNo());
        }

        // 3. 尝试通过账号匹配
        if (user == null && adUserInfo.getAccount() != null && !adUserInfo.getAccount().isEmpty()) {
            user = sysUsersService.selectOne(adUserInfo.getAccount());
        }

        if (user != null) {
            // 更新现有用户信息
            updateUserLoginInfo(user.getUserId(), adUserInfo);
            log.info("匹配到现有用户: {}", user.getUsername());
        } else {
            // 创建新用户
            user = createADUser(adUserInfo);
            log.info("创建新用户: {}", user.getUsername());
        }

        return user;
    }

    @Override
    public AuthUserDto findUserByEmail(String email) {
        if (email == null || email.isEmpty()) {
            return null;
        }
        return sysUsersService.selectByEmail(email);
    }

    @Override
    public AuthUserDto findUserByJobNo(String jobNo) {
        if (jobNo == null || jobNo.isEmpty()) {
            return null;
        }
        return sysUsersService.selectByJobNo(jobNo);
    }

    @Override
    public AuthUserDto createADUser(ADUserInfoDTO adUserInfo) {
        try {
            // 生成用户ID
            String userId = UUID.randomUUID().toString();

            // 创建 SysUsers 记录
            SysUsersPO sysUsersPO = new SysUsersPO();
            sysUsersPO.setUserId(userId);
            sysUsersPO.setUsername(adUserInfo.getAccount());
            sysUsersPO.setEmail(adUserInfo.getEmail());
            sysUsersPO.setJobNo(adUserInfo.getJobNo());
            sysUsersPO.setTeamNames(adUserInfo.getTeamNames());
            sysUsersPO.setFirstName(adUserInfo.getName());
            sysUsersPO.setLastName("");
            sysUsersPO.setIsActive("1");
            sysUsersPO.setIsStaff("1");
            sysUsersPO.setIsSuperuser("0");
            sysUsersPO.setDateJoined(new Date());
            sysUsersPO.setLastLogin(new Date());
            sysUsersPO.setPassword(""); // OAuth用户不需要密码

            // 保存用户基本信息
            sysUsersService.save(sysUsersPO);

            // 创建用户管理信息
            String userRole = determineUserRole(adUserInfo);
            String userProduct = determineUserProduct(adUserInfo);
            createUserManagementInfo(userId, adUserInfo, userRole, userProduct);

            // 构建返回的用户信息
            AuthUserDto authUserDto = AuthUserDto.builder()
                    .userId(userId)
                    .username(adUserInfo.getAccount())
                    .email(adUserInfo.getEmail())
                    .firstName(adUserInfo.getName())
                    .lastName("")
                    .isActive("1")
                    .isStaff("1")
                    .isSuperuser("0")
                    .dateJoined(new Date())
                    .lastLogin(new Date())
                    .role(userRole)
                    .realName(adUserInfo.getName())
                    .userType("oauth_user")
                    .product(userProduct)
                    .build();

            return authUserDto;

        } catch (Exception e) {
            log.error("创建OAuth用户失败", e);
            throw new RuntimeException("创建用户失败: " + e.getMessage());
        }
    }

    @Override
    public void updateUserLoginInfo(String userId, ADUserInfoDTO adUserInfo) {
        try {
            SysUsersPO updateUser = new SysUsersPO();
            updateUser.setUserId(userId);
            updateUser.setLastLogin(new Date());
            updateUser.setEmail(adUserInfo.getEmail());
            updateUser.setJobNo(adUserInfo.getJobNo());
            updateUser.setTeamNames(adUserInfo.getTeamNames());
            // 不更新 firstName, lastName, password 字段

            sysUsersService.updateLastLoginTime(updateUser);
            log.info("更新用户登录信息成功，用户ID: {}", userId);

        } catch (Exception e) {
            log.error("更新用户登录信息失败", e);
        }
    }

    /**
     * 创建用户管理信息
     */
    private void createUserManagementInfo(String userId, ADUserInfoDTO adUserInfo, String role, String product) {
        try {
            // 生成管理记录ID
            String managementId = UUID.randomUUID().toString();

            // 创建用户管理记录
            SysUserManagementPO managementPO = new SysUserManagementPO();
            managementPO.setManagementId(managementId);
            managementPO.setUserId(userId);
            managementPO.setUsername(adUserInfo.getAccount());
            managementPO.setAccountName(adUserInfo.getAccount());
            managementPO.setRealName(adUserInfo.getName());
            managementPO.setUserType("oauth_user");
            managementPO.setRole(role);
            managementPO.setProduct(product);

            // 保存用户管理信息
            // 使用 MyBatis-Plus 的 save 方法
            sysUserManagementService.save(managementPO);

            log.info("用户管理信息创建成功，用户ID: {}, 角色: {}", userId, role);

        } catch (Exception e) {
            log.error("创建用户管理信息失败", e);
            throw new RuntimeException("创建用户管理信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据AD用户信息确定用户角色
     */
    private String determineUserRole(ADUserInfoDTO adUserInfo) {
        // 根据部门信息或其他规则确定用户角色
        // 这里可以根据 teamIds 或其他字段来判断
        return "user"; // 默认角色
    }

    /**
     * 根据AD用户信息确定用户产品权限
     */
    private String determineUserProduct(ADUserInfoDTO adUserInfo) {
        // 根据部门信息或其他规则确定产品权限
        return "default"; // 默认产品
    }
}

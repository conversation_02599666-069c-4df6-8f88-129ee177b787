import http from './http';

// 导出 http 实例供其他组件使用
export { http };

// 首页相关接口
export const getIndexStats = () => {
  return http.get('/index/stats');
};

// AI模型相关接口
export const getAIModelResponse = (params: {
  messages: Array<{role: string; content: string}>;
  model: string;
  max_tokens?: number;
  temperature?: number;
}) => {
  return http.post('/ai/model/generate/nostream', params);
};

export const login = (username: string, password: string, rememberMe: boolean) => {
  return http.post('/AuthUser/login', {
    username,
    password,
    rememberMe
  });
};



// OAuth完整登录接口
export const oauthCompleteLogin = (params: {
  code: string;
  state: string;
}) => {
  return http.post('/AuthUser/oauth-complete-login', params);
};



export const getUserList = (params: {
  pageNum: number;
  pageSize: number;
  permissionList?: string[];
  productList?: string[];
  typeList?: string[];
  usernameLike?: string;
}) => {
  return http.post('/SysUserManagement/list', params);
};

export const getProjectList = () => {
    return http.get('/ZtProject/getListByParentAndType');
};

export const saveUser = (params: {
  accountName: string;
  managementId?: string;
  product: string;
  realName: string;
  role?: string;
  userId?: number;
  userType: string;
  username: string;
}) => {
  return http.post('/SysUserManagement/save', params);
};

export const updateUser = (params: {
  managementId: string;
  realName?: string;
  userType?: string;
  product?: string;
  role?: string;
}) => {
  return http.post('/SysUserManagement/update', params);
};

export const resetPassword = (userId: string) => {
  return http.post('/SysUsers/resetPassword', { userId });
};

export const deleteUser = (params:{
  managementId: string;
  userId: string;
}) => {
  return http.post('/SysUserManagement/delete', params);
};

export const getFeatureList = (params: {
  pageNum: number;
  pageSize: number;
  moduleName?: string;
  personResponsible?: string;
  level?: string;
  groupResponsible?: string;
}) => {
  return http.post('/SysFeature/list', params);
};

export const getFeatureTree = (params: {
  productId?: number;
}) => {
  const config = {
    headers: {
      'Content-Type': 'application/json'
    }
  };
  return http.post(`/SysFeature/feature_tree`, params, config);
};

export const saveFeature = (params: {
  productId: number;
  moduleName: string;
  parentId: number;
  groupResponsible: string;
  personResponsible: string;
  level: number;
}) => {
  return http.post('/SysFeature/save', params);
};

export const updateFeature = (params: {
 featureId: String;
 moduleName: string;
 groupResponsible: string;
 personResponsible: string; 
 parentId: string;
 level: string;
 productId: string;
}) => {
  return http.post('/SysFeature/update', params);
};

export const deleteFeature = (params: {
  featureId: string;
  Isdelete: number; 
}) => {
  return http.post('/SysFeature/delete', params); 
}

// 获取首页排行榜数据
export const getIndexRankings = () => {
  return http.get('/index/rankings');
};

// 获取工具类别分布数据
export const getIndexDistribution = () => {
  return http.get('/index/distribution');
};

// 获取工具列表数据
export const getToolsList = (params: {
  pageNum: number;
  pageSize: number;
  category?: string;
}) => {
  return http.post('/SysTool/toolsList', params);
};

// 获取工具审核列表
export const getReviewPage = (params: {
  pageNum: number;
  pageSize: number;
  approvalStatus?: string;
  approvalStatusList?: string[];
  reviewer?: string;
  
}) => {
  return http.post('/tool/review/page', params);
};

// 审核工具
export const reviewTool = (params: {
  relatedId: string;
  approvalStatus: string;
}) => {
  return http.post('/tool/review/review', params);
};

// 上架工具
export const publishTool = (toolId: string) => {
  return http.post(`/SysTool/publish/${toolId}`);
};

// 下架工具
export const unpublishTool = (toolId: string) => {
  return http.post(`/SysTool/unpublish/${toolId}`);
};

// 删除工具
export const deleteTool = (params: {
  toolId: string;
}) => {
  return http.post('/SysTool/delete',params);
};

// 提交工具
export const submitTool = (params: {
  toolName: string;
  linkAddress: string;
  functionDescription: string;
  category: string;
  tags: string;
}) => {
  return http.post('/SysTool/save', params);
};

// 获取当前用户的工具提交记录
export const getMyToolList = (params: {
  pageNum: number;
  pageSize: number;
}) => {
  return http.post('/SysTool/myToolList', params);
};


// 更新工具状态
export const updateToolStatus = (params: {
  toolId: string;
  status: string;
}) => {
  return http.post('/SysTool/update', params);
};


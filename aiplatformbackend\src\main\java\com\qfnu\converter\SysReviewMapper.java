package com.qfnu.converter;

import com.qfnu.model.po.SysReviewPO;
import com.qfnu.model.dto.SysReviewDTO;
import com.qfnu.model.vo.SysReviewVO;
import com.qfnu.model.param.SysReviewParam;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SysReviewMapper {

    @Mappings({
        @Mapping(target = "reviewId", source = "reviewId"),
        @Mapping(target = "relatedId", source = "relatedId"),
        @Mapping(target = "submissionTime", source = "submissionTime"),
        @Mapping(target = "approvalTime", source = "approvalTime"),
        @Mapping(target = "approvalStatus", source = "approvalStatus"),
        @Mapping(target = "reviewer", source = "reviewer")
    })
    SysReviewPO toSysReviewPO(SysReviewParam param);
    
    List<SysReviewPO> toSysReviewPOList(List<SysReviewParam> paramList);
    
    @Mappings({
        @Mapping(target = "reviewId", source = "reviewId"),
        @Mapping(target = "relatedId", source = "relatedId"),
        @Mapping(target = "submissionTime", source = "submissionTime"),
        @Mapping(target = "approvalTime", source = "approvalTime"),
        @Mapping(target = "approvalStatus", source = "approvalStatus"),
        @Mapping(target = "reviewer", source = "reviewer")
    })
    SysReviewVO toSysReviewVO(SysReviewPO po);
    
    List<SysReviewVO> toSysReviewVOList(List<SysReviewPO> poList);
    
    @Mappings({
        @Mapping(target = "reviewId", source = "reviewId"),
        @Mapping(target = "relatedId", source = "relatedId"),
        @Mapping(target = "submissionTime", source = "submissionTime"),
        @Mapping(target = "approvalTime", source = "approvalTime"),
        @Mapping(target = "approvalStatus", source = "approvalStatus"),
        @Mapping(target = "reviewer", source = "reviewer")
    })
    SysReviewDTO toSysReviewDTO(SysReviewPO po);
    
    List<SysReviewDTO> toSysReviewDTOList(List<SysReviewPO> poList);
    
    @Mappings({
        @Mapping(target = "reviewId", source = "reviewId"),
        @Mapping(target = "relatedId", source = "relatedId"),
        @Mapping(target = "submissionTime", source = "submissionTime"),
        @Mapping(target = "approvalTime", source = "approvalTime"),
        @Mapping(target = "approvalStatus", source = "approvalStatus"),
        @Mapping(target = "reviewer", source = "reviewer"),
        @Mapping(target = "tool", source = "tool")
    })
    SysReviewVO toVO(SysReviewDTO dto);
    
    List<SysReviewVO> toSysReviewVOListFromDTO(List<SysReviewDTO> dtoList);
    
    @Mappings({
        @Mapping(target = "reviewId", source = "reviewId"),
        @Mapping(target = "relatedId", source = "relatedId"),
        @Mapping(target = "submissionTime", source = "submissionTime"),
        @Mapping(target = "approvalTime", source = "approvalTime"),
        @Mapping(target = "approvalStatus", source = "approvalStatus"),
        @Mapping(target = "reviewer", source = "reviewer")
    })
    SysReviewPO toSysReviewPOFromDTO(SysReviewDTO dto);
    
    List<SysReviewPO> toSysReviewPOListFromDTO(List<SysReviewDTO> dtoList);
}
package com.qfnu.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qfnu.common.ResponseVO;
import com.qfnu.model.dto.AuthUserDto;
import com.qfnu.model.param.AuthUserParam;
import com.qfnu.model.po.SysUsersPO;
import com.qfnu.model.po.SysUserManagementPO;
import com.qfnu.model.vo.SysUserManagementVO;
import com.qfnu.model.param.SysUserManagementParam;
import com.qfnu.service.SysUserManagementService;
import com.qfnu.service.SysUsersService;
import com.qfnu.service.ADAuthService;
import com.qfnu.utils.JwtUtil;
import io.swagger.annotations.Api;
import org.mindrot.jbcrypt.BCrypt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.List;

@Api(tags = "登录")
@RestController
@RequestMapping("/api/AuthUser")
public class AuthUserController {

    private static final long JWT_EXPIRATION = 7 * 24 * 60 * 60 * 1000; // 7天

    @Autowired
    private SysUsersService sysUsersService;
    @Autowired
    private SysUserManagementService sysUserManagementService;

    @Autowired
    private ADAuthService adAuthService;

    @Value("${app.security.bcrypt.salt}")
    private String salt;

    @Value("${oauth.auth.client-id}")
    private String oauthClientId;

    @Value("${oauth.auth.client-secret}")
    private String oauthClientSecret;

    @Value("${oauth.auth.redirect-uri}")
    private String oauthRedirectUri;

    @Value("${oauth.auth.token-url}")
    private String oauthTokenUrl;

    @Value("${oauth.auth.userinfo-url}")
    private String oauthUserInfoUrl;


    @PostMapping("/login")
    public ResponseVO login(@RequestBody AuthUserParam authUserParam, HttpServletResponse response) {
        String username = authUserParam.getUsername();
        String password = authUserParam.getPassword();
        boolean rememberMe = authUserParam.getRememberMe();
        // 查询用户
        AuthUserDto user = sysUsersService.selectOne(username);

        if (user != null && user.getPassword().equals(BCrypt.hashpw(password, salt))) {
            // 查询对应用户的sys_user_management表的相关信息
            SysUserManagementVO managementVO = sysUserManagementService.getByUserId(user.getUserId());
            if(managementVO != null){
                user.setRealName(managementVO.getRealName());
                user.setUserType(managementVO.getUserType());
                user.setProduct(managementVO.getProduct());
            }

            // 生成JWT令牌
            String token = JwtUtil.generateAccessToken(user.getUserId(),user.getUsername(),user.getRole(),rememberMe);

            // 设置JWT令牌到Cookie
            Cookie jwtCookie = new Cookie("jwt_token", token);
            jwtCookie.setHttpOnly(true); // 防止XSS攻击
            jwtCookie.setSecure(true); // 只在HTTPS下传输
            jwtCookie.setPath("/");

            if (rememberMe) {
                jwtCookie.setMaxAge((int) (JWT_EXPIRATION / 1000)); // 转换为秒
            } else {
                jwtCookie.setMaxAge(-1); // 会话Cookie
            }

            response.addCookie(jwtCookie);

            Map<String, Object> data = new HashMap<>();
            user.setPassword(null);
            data.put("token", token);
            data.put("user", user);

            SysUsersPO sysUsersPO = new SysUsersPO();
            sysUsersPO.setUserId(user.getUserId());
            sysUsersPO.setLastLogin(new Date());
            sysUsersService.updateLastLoginTime(sysUsersPO);

            return new ResponseVO(200, "登录成功", data);
        } else {
            return new ResponseVO(401, "用户名或密码错误", null);
        }
    }



    /**
     * 完整的OAuth登录接口（GET方式） - 一站式处理：授权码 -> Token -> 用户信息 -> 用户匹配 -> 登录
     */
    @GetMapping("/oauth-complete-login")
    public ResponseVO oauthCompleteLoginGet(@RequestParam String code, @RequestParam String state, HttpServletResponse response) {
        return processOAuthLogin(code, state, response);
    }

    /**
     * 完整的OAuth登录接口（POST方式） - 一站式处理：授权码 -> Token -> 用户信息 -> 用户匹配 -> 登录
     */
    @PostMapping("/oauth-complete-login")
    public ResponseVO oauthCompleteLogin(@RequestBody Map<String, String> params, HttpServletResponse response) {
        try {
            String code = params.get("code");
            String state = params.get("state");
            return processOAuthLogin(code, state, response);
        } catch (Exception e) {
            System.out.println("OAuth完整登录异常: " + e.getMessage());
            e.printStackTrace();
            return new ResponseVO(500, "OAuth登录失败: " + e.getMessage(), null);
        }
    }

    /**
     * 处理OAuth登录的核心逻辑
     */
    private ResponseVO processOAuthLogin(String code, String state, HttpServletResponse response) {
        try {

            System.out.println("=== 完整OAuth登录流程开始 ===");
            System.out.println("授权码: " + (code != null ? code.substring(0, Math.min(code.length(), 20)) + "..." : "null"));
            System.out.println("状态: " + state);

            if (code == null || code.isEmpty()) {
                return new ResponseVO(400, "授权码不能为空", null);
            }

            // 第一步：使用授权码获取access_token
            System.out.println("步骤1: 获取Access Token...");
            String accessToken = getAccessTokenFromCode(code);
            if (accessToken == null) {
                return new ResponseVO(500, "获取Access Token失败", null);
            }
            System.out.println("Access Token获取成功: " + accessToken.substring(0, Math.min(accessToken.length(), 20)) + "...");

            // 第二步：使用access_token获取用户信息
            System.out.println("步骤2: 获取用户信息...");
            Map<String, Object> userInfo = getUserInfoFromToken(accessToken);
            if (userInfo == null) {
                return new ResponseVO(500, "获取用户信息失败", null);
            }
            System.out.println("用户信息获取成功: " + userInfo);

            // 第三步：用户匹配和创建
            System.out.println("步骤3: 用户匹配和创建...");
            AuthUserDto localUser = matchOrCreateUser(userInfo);
            if (localUser == null) {
                System.out.println("❌ 用户匹配或创建失败");
                return new ResponseVO(500, "用户匹配或创建失败", null);
            }
            System.out.println("✅ 用户匹配成功: " + localUser.getUsername() + ", userId: " + localUser.getUserId());

            // 第四步：生成JWT Token并设置Cookie
            System.out.println("步骤4: 生成JWT Token...");
            String jwtToken = generateJwtToken(localUser);
            setJwtCookie(response, jwtToken);

            // 更新最后登录时间
            SysUsersPO updateUser = new SysUsersPO();
            updateUser.setUserId(localUser.getUserId());
            updateUser.setLastLogin(new Date());
            sysUsersService.updateLastLoginTime(updateUser);

            System.out.println("=== OAuth登录流程完成 ===");

            // 返回登录成功信息
            Map<String, Object> result = new HashMap<>();
            result.put("user", localUser);
            result.put("token", jwtToken);
            result.put("message", "OAuth登录成功");

            return new ResponseVO(200, "OAuth登录成功", result);

        } catch (Exception e) {
            System.out.println("OAuth完整登录异常: " + e.getMessage());
            e.printStackTrace();
            return new ResponseVO(500, "OAuth登录失败: " + e.getMessage(), null);
        }
    }
    
    /**
     * 创建OAuth用户
     */
    private AuthUserDto createOAuthUser(String account, String email, String jobNo, Object teamNamesObj, String name) {
        try {
            // 生成用户ID
            String userId = UUID.randomUUID().toString();

            // 创建 SysUsers 记录
            SysUsersPO sysUsersPO = new SysUsersPO();
            sysUsersPO.setUserId(userId);
            sysUsersPO.setUsername(account);
            sysUsersPO.setEmail(email);
            sysUsersPO.setJobNo(jobNo);

            // 处理teamNames（转换为JSON字符串）
            if (teamNamesObj != null) {
                String teamNamesJson = convertTeamNamesToJson(teamNamesObj);
                sysUsersPO.setTeamNames(teamNamesJson);
            }

            // 处理姓名
            if (name != null && !name.isEmpty()) {
                sysUsersPO.setFirstName(name);
                sysUsersPO.setLastName("");
            }

            sysUsersPO.setIsActive("1");
            sysUsersPO.setIsStaff("1");
            sysUsersPO.setIsSuperuser("0");
            sysUsersPO.setDateJoined(new Date());
            sysUsersPO.setLastLogin(new Date());
            sysUsersPO.setPassword(""); // OAuth用户不需要密码

            // 保存用户基本信息
            boolean saveResult = sysUsersService.save(sysUsersPO);

            if (saveResult) {
                // 创建 sys_user_management 记录
                createUserManagementRecord(userId, account, name);

                // 查询并返回创建的用户信息
                return sysUsersService.selectOne(account);
            } else {
                return null;
            }

        } catch (Exception e) {
            System.out.println("创建OAuth用户异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 创建用户管理记录
     */
    private void createUserManagementRecord(String userId, String account, String name) {
        try {
            SysUserManagementPO userManagementPO = new SysUserManagementPO();
            userManagementPO.setManagementId(UUID.randomUUID().toString());  // 生成管理记录ID
            userManagementPO.setUserId(userId);
            userManagementPO.setUsername(account);
            userManagementPO.setAccountName(account);
            userManagementPO.setRealName(name != null ? name : account);
            userManagementPO.setUserType("other");  // 按要求设置为 other
            userManagementPO.setRole("common");     // 按要求设置为 common
            userManagementPO.setProduct("");        // 按要求设置为空

            sysUserManagementService.save(userManagementPO);
            System.out.println("创建用户管理记录成功: " + account);
        } catch (Exception e) {
            System.out.println("创建用户管理记录异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建OAuth用户（当sys_user_management记录已存在时）
     */
    private AuthUserDto createOAuthUserWithExistingManagement(SysUserManagementPO userManagement, String account, String email, String jobNo, Object teamNamesObj, String name) {
        try {
            // 使用已存在的user_id
            String userId = userManagement.getUserId();

            // 创建 SysUsers 记录
            SysUsersPO sysUsersPO = new SysUsersPO();
            sysUsersPO.setUserId(userId);
            sysUsersPO.setUsername(account);
            sysUsersPO.setEmail(email);
            sysUsersPO.setJobNo(jobNo);

            // 处理teamNames（转换为JSON字符串）
            if (teamNamesObj != null) {
                String teamNamesJson = convertTeamNamesToJson(teamNamesObj);
                sysUsersPO.setTeamNames(teamNamesJson);
            }

            // 处理姓名
            if (name != null && !name.isEmpty()) {
                sysUsersPO.setFirstName(name);
                sysUsersPO.setLastName("");
            }

            sysUsersPO.setIsActive("1");
            sysUsersPO.setIsStaff("1");
            sysUsersPO.setIsSuperuser("0");
            sysUsersPO.setDateJoined(new Date());
            sysUsersPO.setLastLogin(new Date());
            sysUsersPO.setPassword(""); // OAuth用户不需要密码

            // 保存用户基本信息
            boolean saveResult = sysUsersService.save(sysUsersPO);

            if (saveResult) {
                // 查询并返回创建的用户信息
                return sysUsersService.getUserDtoById(userId);
            } else {
                return null;
            }

        } catch (Exception e) {
            System.out.println("创建OAuth用户异常（使用已存在的管理记录）: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 从授权码获取Access Token
     */
    private String getAccessTokenFromCode(String code) {
        try {
            RestTemplate restTemplate = new RestTemplate();

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            // 设置请求参数
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("grant_type", "authorization_code");
            params.add("client_id", oauthClientId);
            params.add("client_secret", oauthClientSecret);
            params.add("code", code);

            // 创建请求实体
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    oauthTokenUrl,
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                return (String) responseBody.get("content");
            }

            return null;
        } catch (Exception e) {
            System.out.println("获取Access Token异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 从Access Token获取用户信息
     */
    private Map<String, Object> getUserInfoFromToken(String accessToken) {
        try {
            RestTemplate restTemplate = new RestTemplate();

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + accessToken);

            // 创建请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(headers);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    oauthUserInfoUrl,
                    HttpMethod.GET,
                    requestEntity,
                    Map.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return response.getBody();
            }

            return null;
        } catch (Exception e) {
            System.out.println("获取用户信息异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 用户匹配或创建
     */
    private AuthUserDto matchOrCreateUser(Map<String, Object> userInfo) {
        try {
            String account = (String) userInfo.get("account");
            String email = (String) userInfo.get("email");
            String jobNo = (String) userInfo.get("jobNo");
            Object teamNamesObj = userInfo.get("teamNames");
            String name = (String) userInfo.get("name");

            if (account == null || account.isEmpty()) {
                return null;
            }

            System.out.println("开始用户匹配，account: " + account);

            // 第一步：通过account字段匹配sys_user_management表的account_name字段
            SysUserManagementPO userManagement = sysUserManagementService.getByAccountName(account);

            if (userManagement != null) {
                System.out.println("在sys_user_management表中找到匹配用户，user_id: " + userManagement.getUserId());

                // 第二步：用sys_user_management表的user_id字段去检查sys_users表是否有用户
                AuthUserDto existingUser = sysUsersService.getUserDtoById(userManagement.getUserId());

                if (existingUser != null) {
                    System.out.println("在sys_users表中找到对应用户，登录成功");

                    // 用户已存在，更新用户信息
                    SysUsersPO updateUser = new SysUsersPO();
                    updateUser.setUserId(existingUser.getUserId());
                    updateUser.setEmail(email);
                    updateUser.setJobNo(jobNo);

                    if (teamNamesObj != null) {
                        String teamNamesJson = convertTeamNamesToJson(teamNamesObj);
                        updateUser.setTeamNames(teamNamesJson);
                    }

                    updateUser.setLastLogin(new Date());
                    sysUsersService.updateLastLoginTime(updateUser);

                    // 重新查询更新后的用户信息
                    return sysUsersService.getUserDtoById(userManagement.getUserId());
                } else {
                    System.out.println("在sys_users表中未找到对应用户，创建新用户");

                    // 第三步：如果sys_users表中没有用户，创建新用户
                    return createOAuthUserWithExistingManagement(userManagement, account, email, jobNo, teamNamesObj, name);
                }
            } else {
                System.out.println("在sys_user_management表中未找到匹配用户，检查sys_users表");

                // 检查sys_users表中是否已经存在该用户名的用户
                AuthUserDto existingUser = sysUsersService.selectOne(account);

                if (existingUser != null) {
                    System.out.println("在sys_users表中找到现有用户，直接登录");

                    // 用户已存在，更新用户信息
                    SysUsersPO updateUser = new SysUsersPO();
                    updateUser.setUserId(existingUser.getUserId());
                    updateUser.setEmail(email);
                    updateUser.setJobNo(jobNo);

                    if (teamNamesObj != null) {
                        String teamNamesJson = convertTeamNamesToJson(teamNamesObj);
                        updateUser.setTeamNames(teamNamesJson);
                    }

                    updateUser.setLastLogin(new Date());
                    sysUsersService.updateLastLoginTime(updateUser);

                    // 重新查询更新后的用户信息
                    return sysUsersService.selectOne(account);
                } else {
                    System.out.println("在sys_users表中也未找到用户，创建全新用户");

                    // 如果两个表中都没有匹配的用户，创建全新用户
                    return createOAuthUser(account, email, jobNo, teamNamesObj, name);
                }
            }
        } catch (Exception e) {
            System.out.println("用户匹配或创建异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 生成JWT Token - 使用JwtUtil统一生成
     */
    private String generateJwtToken(AuthUserDto user) {
        try {
            // 使用JwtUtil统一生成Token，确保密钥一致
            return JwtUtil.generateAccessToken(user.getUserId(), user.getUsername(), user.getRole(), true);
        } catch (Exception e) {
            System.out.println("生成JWT Token异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 设置JWT Cookie
     */
    private void setJwtCookie(HttpServletResponse response, String jwtToken) {
        try {
            Cookie jwtCookie = new Cookie("jwt_token", jwtToken);
            jwtCookie.setHttpOnly(true);
            jwtCookie.setSecure(false); // 开发环境设为false，生产环境应设为true
            jwtCookie.setPath("/");
            jwtCookie.setMaxAge(7 * 24 * 60 * 60); // 7天
            response.addCookie(jwtCookie);
        } catch (Exception e) {
            System.out.println("设置JWT Cookie异常: " + e.getMessage());
        }
    }

    /**
     * 将teamNames转换为JSON字符串
     */
    private String convertTeamNamesToJson(Object teamNamesObj) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();

            if (teamNamesObj instanceof List) {
                // 如果是List，直接转换为JSON
                return objectMapper.writeValueAsString(teamNamesObj);
            } else if (teamNamesObj instanceof String) {
                // 如果是字符串，检查是否已经是JSON格式
                String teamNamesStr = (String) teamNamesObj;
                if (teamNamesStr.startsWith("[") && teamNamesStr.endsWith("]")) {
                    return teamNamesStr; // 已经是JSON格式
                } else {
                    // 单个团队名称，转换为JSON数组
                    return "[\"" + teamNamesStr + "\"]";
                }
            } else {
                // 其他类型，尝试转换为JSON
                return objectMapper.writeValueAsString(teamNamesObj);
            }
        } catch (Exception e) {
            System.out.println("转换teamNames为JSON异常: " + e.getMessage());
            // 如果转换失败，返回空数组
            return "[]";
        }
    }

}

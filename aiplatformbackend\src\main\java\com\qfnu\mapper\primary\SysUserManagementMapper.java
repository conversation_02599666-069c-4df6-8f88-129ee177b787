package com.qfnu.mapper.primary;

import com.qfnu.model.po.SysUserManagementPO;
import com.qfnu.model.param.SysUserManagementQueryParam;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface SysUserManagementMapper extends BaseMapper<SysUserManagementPO> {
    /**
     * 分页查询用户管理列表
     *
     * @param page  分页参数
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<SysUserManagementPO> getPageList(IPage<SysUserManagementPO> page, @Param("param") SysUserManagementQueryParam param);

    int myUpdateById(SysUserManagementPO sysUserManagementPO);

    int myDeleteById(String managementId);

    SysUserManagementPO getUserById(String userId);

    @Select("SELECT * FROM sys_user_management WHERE user_id = #{userId}")
    SysUserManagementPO selectByUserId(String userId);

    @Select("SELECT * FROM sys_user_management WHERE account_name = #{accountName}")
    SysUserManagementPO selectByAccountName(String accountName);

}
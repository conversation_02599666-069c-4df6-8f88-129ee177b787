<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qfnu.mapper.primary.SysReviewMapper">

    <resultMap id="ReviewWithToolMap" type="com.qfnu.model.dto.SysReviewDTO">
        <id column="review_id" property="reviewId"/>
        <result column="related_id" property="relatedId"/>
        <result column="submission_time" property="submissionTime"/>
        <result column="approval_time" property="approvalTime"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="reviewer" property="reviewer"/>
        <association property="tool" javaType="com.qfnu.model.dto.SysToolDTO">
            <result column="tool_name" property="toolName"/>
            <result column="link_address" property="linkAddress"/>
            <result column="creators" property="creators"/>
            <result column="function_description" property="functionDescription"/>
            <result column="category" property="category"/>
            <result column="tags" property="tags"/>
            <result column="rating" property="rating"/>
        </association>
    </resultMap>

    <sql id="Base_Column_List">
        r.review_id, r.related_id, r.submission_time, r.approval_time, r.approval_status, r.reviewer,
        t.tool_name, t.link_address, t.function_description, t.category, t.tags, t.rating,t.creators
    </sql>

    <select id="getPageList" resultMap="ReviewWithToolMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_review r
        LEFT JOIN sys_tool t ON r.related_id = t.tool_id
        <where>
             1=1
            <choose>
                <when test="param.approvalStatusList != null and param.approvalStatusList.size() > 0">
                    AND r.approval_status IN
                    <foreach collection="param.approvalStatusList" item="status" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </when>
                <when test="param.approvalStatus != null">
                    AND r.approval_status = #{param.approvalStatus}
                </when>
            </choose>
            <if test="param.relatedId != null">
                AND r.related_id = #{param.relatedId}
            </if>
            <if test="param.reviewer != null and param.reviewer != ''">
                AND r.reviewer = #{param.reviewer}
            </if>
            AND t.is_deleted = 0
        </where>
        ORDER BY r.submission_time DESC
    </select>


    <!-- 统计总记录数 -->
    <select id="countTotal" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM sys_review r
        LEFT JOIN sys_tool t ON r.related_id = t.tool_id
        <where>
            <choose>
                <when test="param.approvalStatusList != null and param.approvalStatusList.size() > 0">
                    AND r.approval_status IN
                    <foreach collection="param.approvalStatusList" item="status" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </when>
                <when test="param.approvalStatus != null">
                    AND r.approval_status = #{param.approvalStatus}
                </when>
            </choose>
            <if test="param.relatedId != null">
                AND r.related_id = #{param.relatedId}
            </if>
            <if test="param.reviewer != null and param.reviewer != ''">
                AND r.reviewer = #{param.reviewer}
            </if>
            AND t.is_deleted = 0
        </where>
    </select>
</mapper>
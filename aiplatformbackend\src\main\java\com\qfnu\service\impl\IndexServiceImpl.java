package com.qfnu.service.impl;

import com.qfnu.mapper.primary.SysToolMapper;
import com.qfnu.mapper.primary.SysUsersMapper;
import com.qfnu.service.IndexService;
import com.qfnu.model.vo.DashboardStatsVO;
import com.qfnu.model.vo.TopRankingsVO;
import com.qfnu.model.vo.ToolDistributionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class IndexServiceImpl implements IndexService {


    @Autowired
    private SysUsersMapper sysUsersMapper;
    @Autowired
    private SysToolMapper sysToolMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DashboardStatsVO getDashboardStats() {
        DashboardStatsVO stats = new DashboardStatsVO();

        stats.setTotalTools(sysToolMapper.toolCount());

        stats.setTotalUsage(sysToolMapper.totalUsage());

        stats.setActiveUsers(sysUsersMapper.count());

        stats.setAverageRating(sysToolMapper.avgRating());
        
        return stats;
    }

    @Override
    @Transactional(readOnly = true)
    public TopRankingsVO getTopRankings() {
        TopRankingsVO rankings = new TopRankingsVO();
        
        // 获取热门工具TOP5
        rankings.setHotTools(sysToolMapper.getHotTools());
        
        // 获取AI应用贡献达人TOP5
        rankings.setTopContributors(sysToolMapper.getTopContributors());
        
        // 获取好评工具TOP5
        rankings.setTopRatedTools(sysToolMapper.getTopRatedTools());
        
        return rankings;
    }

    @Override
    @Transactional(readOnly = true)
    public ToolDistributionVO getToolDistribution() {
        ToolDistributionVO distribution = new ToolDistributionVO();
        
        // 获取工具类别占比数据
        distribution.setCategoryDistribution(sysToolMapper.getCategoryDistribution());
        
        // 获取月度工具上线趋势数据
        distribution.setMonthlyTrend(sysToolMapper.getMonthlyTrend());
        
        return distribution;
    }
}
package com.qfnu.controller;

import com.qfnu.common.ResponseVO;
import com.qfnu.common.annotation.RequiredPermission;
import com.qfnu.model.vo.SysUserManagementVO;
import com.qfnu.service.SysReviewService;
import com.qfnu.model.vo.SysReviewVO;
import com.qfnu.model.param.SysReviewParam;

import com.qfnu.service.SysUserManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

@Api(tags = "SysReview接口,审核记录")
@RestController
@RequestMapping("/api/SysReview")
public class SysReviewController {

    @Resource
    private SysReviewService sysReviewService;

//    @ApiOperation("批量新增")
//    @PostMapping("/saveBatch")
//    public ResponseVO<Integer> saveBatch(@RequestBody List<SysReviewParam> paramList) {
//        return ResponseVO.success(sysReviewService.saveBatch(paramList));
//    }

    @RequiredPermission({"super,admin"})
    @ApiOperation("根据Id删除记录")
    @PostMapping("/delete")
    public ResponseVO<Integer> delete(@RequestBody SysReviewParam param) {
        return ResponseVO.success(sysReviewService.delete(param.getReviewId()));
    }

    @ApiOperation("根据ID查询")
    @PostMapping("/getById")
    public ResponseVO<SysReviewVO> getById(@RequestBody SysReviewParam param) {
        return ResponseVO.success(sysReviewService.getById(param.getReviewId()));
    }


    @ApiOperation("分页查询审核记录")
    @PostMapping("/page")
    public ResponseVO<IPage<SysReviewVO>> getPage(@RequestBody SysReviewParam param) {
        return ResponseVO.success(sysReviewService.getPage(param), "查询成功", 200);
    }

}
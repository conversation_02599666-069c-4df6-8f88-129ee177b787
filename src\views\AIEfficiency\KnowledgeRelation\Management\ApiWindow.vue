<template>
  <a-modal
    :visible="visible"
    @update:visible="(val) => emit('update:visible', val)"
    :title="title"
    width="90vw"
    :footer="null"
    @cancel="handleClose"
    :body-style="{ maxHeight: '70vh', overflowY: 'auto', padding: '24px' }"
  >
    <div style="margin-bottom: 16px; display: flex; gap: 12px; align-items: center; justify-content: space-between;">
      <div style="display: flex; gap: 12px; align-items: center;">
        <a-select
          v-model:value="statusFilter"
          mode="multiple"
          allow-clear
          placeholder="请选择状态"
          style="width: 180px"
          @change="onStatusFilterChange"
        >
          <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
        <a-select
          v-model:value="methodFilter"
          mode="multiple"
          allow-clear
          placeholder="请选择请求方法"
          style="width: 180px"
          @change="onMethodFilterChange"
        >
          <a-select-option v-for="item in methodOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
        <a-select
          v-model:value="tagFilter"
          mode="multiple"
          allow-clear
          placeholder="请选择标签"
          style="width: 180px"
          @change="onTagFilterChange"
        >
          <a-select-option v-for="tag in tagOptions" :key="tag" :value="tag">
            {{ tag }}
          </a-select-option>
        </a-select>
        <a-input-search
          v-model:value="apiNameSearch"
          placeholder="搜索API名称"
          style="width: 200px"
          @search="onApiSearch"
          @input="(e) => debouncedSearch(e.target.value)"
        />
      </div>
      <div style="display: flex; gap: 8px;">
        <a-button 
          :type="selectedRowKeys.length ? 'danger' : 'primary'" 
          :disabled="!selectedRowKeys.length"
          @click="handleBatchDeleteApi"
          class="batch-delete-btn"
        >
          <template #icon><DeleteOutlined /></template>
          批量删除
        </a-button>
        <a-button 
          type="primary" 
          :disabled="!selectedRowKeys.length"
          @click="handleEmbedSelected"
          class="embed-btn"
        >
          <template #icon><ApiOutlined /></template>
          接口嵌入
        </a-button>
        <a-button type="primary" @click="showColumnSettings" class="column-settings-btn">
          <template #icon><SettingOutlined /></template>
          自定义列
        </a-button>
      </div>
    </div>
    <a-table
      :columns="visibleColumns"
      :data-source="pagedList"
      :pagination="{
        current: page,
        pageSize: pageSize,
        pageSizeOptions: ['10', '20', '50'],
        showSizeChanger: true,
        showQuickJumper: true,
        onShowSizeChange: onPageSizeChange,
        total: total,
        onChange: onPageChange
      }"
      rowKey="id"
      bordered
      size="middle"
      :scroll="{ x: 'max-content', y: 'calc(70vh - 200px)' }"
      :row-selection="rowSelection"
      :loading="apiLoading"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'method'">
          <a-tooltip :title="record.method">
            <span class="method-tag" :style="getMethodTagStyle(record.method)">
              {{ record.method }}
            </span>
          </a-tooltip>
        </template>
        <template v-else-if="column.key === 'status'">
          <a-tooltip :title="record.status">
            <a-tag :color="getStatusColor(record.status)">
              {{ record.status }}
            </a-tag>
          </a-tooltip>
        </template>
        <template v-else-if="column.key === 'tags'">
          <a-tooltip :title="record.tags && record.tags.length > 0 ? record.tags.join(', ') : '-'">
            <div class="tags-container">
              <a-tag v-for="tag in record.tags" :key="tag" :color="getTagColor(tag)">{{ tag }}</a-tag>
              <span v-if="!record.tags || record.tags.length === 0" class="no-tags">-</span>
            </div>
          </a-tooltip>
        </template>
        <template v-else-if="['parameters_list', 'request_body_list', 'responses_list'].includes(column.key)">
          <div class="content-preview">
            <a-tooltip :title="formatJsonForTooltip(record[column.key])">
              <span class="preview-text">{{ getPreviewText(record[column.key]) }}</span>
            </a-tooltip>
            <a-button 
              type="link" 
              class="view-btn"
              @click="showDetailModal(column.key, record)"
            >
              <EyeOutlined />
            </a-button>
          </div>
        </template>
        <template v-else>
          <a-tooltip :title="record[column.key]">
            <span>{{ record[column.key] }}</span>
          </a-tooltip>
        </template>
      </template>
    </a-table>

    <!-- 列设置弹窗 -->
    <a-modal
      v-model:visible="columnSettingsVisible"
      title="自定义列"
      width="400px"
      :footer="null"
      @cancel="columnSettingsVisible = false"
      class="column-settings-modal"
    >
      <div class="column-settings-content">
        <div class="column-settings-header">
          <a-checkbox
            :indeterminate="isIndeterminate"
            :checked="isAllSelected"
            @change="onCheckAllChange"
          >
            全选
          </a-checkbox>
        </div>
        <div class="column-settings-body">
          <a-checkbox-group v-model:value="selectedColumns" style="width: 100%">
            <a-row>
              <a-col :span="8" v-for="col in allColumns" :key="col.key">
                <a-checkbox :value="col.key">{{ col.title }}</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </div>
      </div>
    </a-modal>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      :title="detailModalTitle"
      width="800px"
      :footer="null"
      @cancel="detailModalVisible = false"
    >
      <div class="detail-content">
        <!-- 参数详情 -->
        <template v-if="detailType === 'parameters_list'">
          <a-table
            :dataSource="formatParameters(detailContent)"
            :columns="parameterColumns"
            :pagination="false"
            size="small"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'required'">
                <a-tag :color="record.required ? 'red' : 'default'">
                  {{ record.required ? '必填' : '选填' }}
                </a-tag>
              </template>
              <template v-if="column.key === 'type'">
                <a-tag color="blue">{{ record.type }}</a-tag>
              </template>
            </template>
          </a-table>
        </template>

        <!-- 请求体详情 -->
        <template v-if="detailType === 'request_body_list'">
          <div class="responses-content">
            <div class="response-item">
              <div class="response-schema">
                <pre class="json-content">{{ formatJson(detailContent) }}</pre>
              </div>
            </div>
          </div>
        </template>

        <!-- 响应详情 -->
        <template v-if="detailType === 'responses_list'">
          <div class="responses-content">
            <div v-for="response in formatResponses(detailContent)" :key="response.status" class="response-item">
              <div class="response-header">
                <a-tag :color="getStatusTagColor(response.status)">{{ response.status }}</a-tag>
                <span class="response-description">{{ response.description }}</span>
              </div>
              <div class="response-schema" v-if="response.schema">
                <pre class="json-content">{{ formatJson(response.schema) }}</pre>
              </div>
            </div>
          </div>
        </template>
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch, onUnmounted } from 'vue';
import { ApiOutlined, SettingOutlined, EyeOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import http from '@/utils/ai/http';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'API列表'
  },
  apiList: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:visible', 'close', 'embed', 'refresh']);

// 状态筛选
const statusFilter = ref([]);
const apiLoading = ref(false);
const statusOptions = computed(() => {
  const statusSet = new Set(props.apiList.map(item => item.status));
  const statusLabelMap = {
    'active': '已嵌入',
    'inactive': '非激活',
    'deprecated': '已废弃',
    'pending': '待审核'
  };
  return Array.from(statusSet).map(s => ({ value: s, label: statusLabelMap[s] || s }));
});

// 请求方法筛选
const methodFilter = ref([]);
const methodOptions = [
  { value: 'GET', label: 'GET' },
  { value: 'POST', label: 'POST' },
  { value: 'PUT', label: 'PUT' },
  { value: 'DELETE', label: 'DELETE' },
  { value: 'PATCH', label: 'PATCH' }
];

// 标签筛选
const tagFilter = ref([]);
const tagOptions = computed(() => {
  const tagsSet = new Set();
  props.apiList.forEach(item => {
    if (Array.isArray(item.tags)) {
      item.tags.forEach(tag => tagsSet.add(tag));
    }
  });
  return Array.from(tagsSet);
});

// 搜索
const apiNameSearch = ref('');

// 分页
const page = ref(1);
const pageSize = ref(10);

// 行选择
const selectedRowKeys = ref([]);
const rowSelection = {
  selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys;
  }
};

// 列设置
const columnSettingsVisible = ref(false);
const selectedColumns = ref(['id', 'summary', 'method', 'path', 'status', 'version', 'tags', 'parameters_list', 'request_body_list', 'responses_list']);
const allColumns = [
  { title: 'ID', key: 'id' },
  { title: '接口名称', key: 'summary', width: 150, ellipsis: true },
  { title: '状态', key: 'status' },
  { title: '参数', key: 'parameters_list' },
  { title: '请求体', key: 'request_body_list' },
  { title: '响应', key: 'responses_list' },
  { title: '来源文档', key: 'document_name' },
  { title: '请求方法', key: 'method' },
  { title: '接口路径', key: 'path' },
  { title: '标签', key: 'tags' },
];

// 表格列配置
const columns = [
  { title: 'ID', dataIndex: 'id', key: 'id', width: 60, ellipsis: true },
  { title: '标签', dataIndex: 'tags', key: 'tags', width: 80, ellipsis: true },
  { title: '接口名称', dataIndex: 'summary', key: 'summary', width: 150, ellipsis: true },
  { title: '请求方法', dataIndex: 'method', key: 'method', width: 100, ellipsis: true },
  { title: '接口路径', dataIndex: 'path', key: 'path', width: 200, ellipsis: true },
  { title: '状态', dataIndex: 'status', key: 'status', width: 80, ellipsis: true },
  { 
    title: '参数', 
    dataIndex: 'parameters_list', 
    key: 'parameters_list', 
    width: 150, 
    ellipsis: true,
    customRender: ({ text }) => {
      if (!text) return '-';
      const content = typeof text === 'string' ? text : JSON.stringify(text);
      return content.length > 50 ? content.substring(0, 50) + '...' : content;
    }
  },
  { 
    title: '请求体', 
    dataIndex: 'request_body_list', 
    key: 'request_body_list', 
    width: 150, 
    ellipsis: true,
    customRender: ({ text }) => {
      if (!text) return '-';
      const content = typeof text === 'string' ? text : JSON.stringify(text);
      return content.length > 50 ? content.substring(0, 50) + '...' : content;
    }
  },
  { 
    title: '响应', 
    dataIndex: 'responses_list', 
    key: 'responses_list', 
    width: 150, 
    ellipsis: true,
    customRender: ({ text }) => {
      if (!text) return '-';
      const content = typeof text === 'string' ? text : JSON.stringify(text);
      return content.length > 50 ? content.substring(0, 50) + '...' : content;
    }
  },
  { title: '来源文档', dataIndex: 'document_name', key: 'document_name', width: 80, ellipsis: true },
];

// 计算可见列
const visibleColumns = computed(() => {
  return columns.filter(col => selectedColumns.value.includes(col.key));
});

// API筛选后的列表
const filteredApiList = computed(() => {
  let list = props.apiList;
  
  if (statusFilter.value.length > 0) {
    list = list.filter(item => statusFilter.value.includes(item.status));
  }
  
  if (methodFilter.value.length > 0) {
    list = list.filter(item => methodFilter.value.includes(item.method));
  }

  if (tagFilter.value.length > 0) {
    list = list.filter(item => {
      return item.tags && tagFilter.value.every(filterTag => item.tags.includes(filterTag));
    });
  }
  
  if (apiNameSearch.value) {
    const searchText = apiNameSearch.value.toLowerCase().trim();
    list = list.filter(item => {
      const summary = (item.summary || '').toLowerCase();
      const path = (item.path || '').toLowerCase();
      const documentName = (item.document_name || '').toLowerCase();
      const tags = (item.tags || []).map(tag => tag.toLowerCase());
      
      return summary.includes(searchText) || 
             path.includes(searchText) || 
             documentName.includes(searchText) ||
             tags.some(tag => tag.includes(searchText));
    });
  }
  
  return list;
});

// 分页后的列表
const pagedList = computed(() => {
  const start = (page.value - 1) * pageSize.value;
  return filteredApiList.value.slice(start, start + pageSize.value);
});

// 总数
const total = computed(() => filteredApiList.value.length);

// 方法
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

const onStatusFilterChange = () => {
  page.value = 1;
};

const onMethodFilterChange = () => {
  page.value = 1;
};

const onTagFilterChange = () => {
  page.value = 1;
};

const onApiSearch = () => {
  page.value = 1;
};

const onPageChange = (p, size) => {
  page.value = p;
  pageSize.value = size;
};

const onPageSizeChange = (current, size) => {
  page.value = 1;
  pageSize.value = size;
};

const showColumnSettings = () => {
  columnSettingsVisible.value = true;
};

const handleColumnSettingsConfirm = () => {
  columnSettingsVisible.value = false;
};

const isIndeterminate = computed(() => {
  return selectedColumns.value.length > 0 && selectedColumns.value.length < allColumns.length;
});

const isAllSelected = computed(() => {
  return selectedColumns.value.length === allColumns.length;
});

const onCheckAllChange = (e) => {
  selectedColumns.value = e.target.checked ? allColumns.map(col => col.key) : [];
};

const handleEmbedSelected = () => {
  if (!selectedRowKeys.value.length) {
    message.warning('请选择要嵌入的接口');
    return;
  }
  emit('embed', selectedRowKeys.value);
};

// Method 标签颜色映射
const methodColorMap = {
  'GET': '#52c41a',     // 绿色
  'POST': '#1890ff',    // 蓝色
  'PUT': '#faad14',     // 黄色
  'DELETE': '#ff4d4f',  // 红色
  'PATCH': '#722ed1',   // 紫色
  'HEAD': '#13c2c2',    // 青色
  'OPTIONS': '#eb2f96'  // 粉色
};

// 获取 Method 标签样式
const getMethodTagStyle = (method) => {
  const color = methodColorMap[method?.toUpperCase()] || '#d9d9d9';
  return {
    backgroundColor: `${color}15`,
    color: color,
    border: `1px solid ${color}30`,
    padding: '2px 8px',
    borderRadius: '4px',
    fontSize: '12px',
    fontWeight: '500',
    display: 'inline-block',
    textAlign: 'center',
    minWidth: '60px'
  };
};

// 获取状态标签颜色
const getStatusColor = (status) => {
  const colorMap = {
    'active': 'success',    // 已嵌入 - 绿色
    'inactive': 'default',  // 未嵌入 - 灰色
    'deprecated': 'blue',   // 已废弃 - 蓝色
    'pending': 'blue'       // 待审核 - 蓝色
  };
  return colorMap[status] || 'default';
};

// 标签颜色映射
const tagColors = [
  '#1890ff', // 蓝色
  '#faad14', // 黄色
  '#13c2c2', // 青色
  '#eb2f96', // 粉色
  '#a0d911', // 浅绿
  '#2f54eb', // 极客蓝
  '#faad14', // 金色
  '#13c2c2', // 青色
  '#fa8c16', // 橙色
  '#a0d911', // 浅绿
  '#2f54eb', // 极客蓝
  '#1890ff', // 蓝色

];

// 标签颜色缓存
const tagColorCache = new Map();

// 获取标签颜色
const getTagColor = (tag) => {
  // 如果标签已经在缓存中，直接返回缓存的颜色
  if (tagColorCache.has(tag)) {
    return tagColorCache.get(tag);
  }

  // 为标签分配一个随机颜色
  const randomColor = tagColors[Math.floor(Math.random() * tagColors.length)];
  
  // 将颜色存入缓存
  tagColorCache.set(tag, randomColor);
  
  return randomColor;
};

// 在组件卸载时清除颜色缓存
onUnmounted(() => {
  tagColorCache.clear();
});

// 详情弹窗相关
const detailModalVisible = ref(false);
const detailModalTitle = ref('');
const detailContent = ref(null);
const detailType = ref('');

// 参数表格列配置
const parameterColumns = [
  { title: '参数名', dataIndex: 'name', key: 'name', width: 150 },
  { title: '位置', dataIndex: 'in', key: 'in', width: 100 },
  { title: '必填', dataIndex: 'required', key: 'required', width: 80 },
  { title: '描述', dataIndex: 'description', key: 'description' }
];

// 显示详情弹窗
const showDetailModal = (type, record) => {
  const titleMap = {
    'parameters_list': '参数详情',
    'request_body_list': '请求体详情',
    'responses_list': '响应详情'
  };
  
  detailModalTitle.value = titleMap[type] || '详情';
  // 根据不同类型使用不同的字段
  if (type === 'parameters_list') {
    detailContent.value = record.parameters;
  } else if (type === 'request_body_list') {
    detailContent.value = record.request_body;
  } else if (type === 'responses_list') {
    detailContent.value = record.responses;
  } else {
    detailContent.value = record[type];
  }
  detailType.value = type;
  detailModalVisible.value = true;
};

// 格式化参数数据
const formatParameters = (content) => {
  if (!content) return [];
  try {
    const params = typeof content === 'string' ? JSON.parse(content) : content;
    if (Array.isArray(params)) {
      return params.map(param => ({
        name: param.name || param.parameter_name,
        in: param.in || param.location,
        type: param.type || param.parameter_type,
        required: param.required || false,
        description: param.description || param.parameter_description || ''
      }));
    }
    return [{
      name: params.name || params.parameter_name,
      in: params.in || params.location,
      type: params.type || params.parameter_type,
      required: params.required || false,
      description: params.description || params.parameter_description || ''
    }];
  } catch (e) {
    return [];
  }
};

// 格式化响应数据
const formatResponses = (content) => {
  if (!content) return {};
  try {
    const responses = typeof content === 'string' ? JSON.parse(content) : content;
    return Object.entries(responses).map(([status, response]) => ({
      status,
      description: response.description || '',
      schema: response.schema || response.content || {}
    }));
  } catch (e) {
    return [];
  }
};

// 获取状态码标签颜色
const getStatusTagColor = (status) => {
  const statusCode = parseInt(status);
  if (statusCode >= 200 && statusCode < 300) return 'success';
  if (statusCode >= 300 && statusCode < 400) return 'warning';
  if (statusCode >= 400 && statusCode < 500) return 'error';
  if (statusCode >= 500) return 'volcano';
  return 'default';
};

// 获取预览文本
const getPreviewText = (content) => {
  if (!content) return '-';
  const text = typeof content === 'string' ? content : JSON.stringify(content);
  return text.length > 50 ? text.substring(0, 50) + '...' : text;
};

// 格式化JSON
const formatJson = (content) => {
  if (!content) return '';
  try {
    const json = typeof content === 'string' ? JSON.parse(content) : content;
    return JSON.stringify(json, null, 2);
  } catch (e) {
    return content;
  }
};

// 新增：用于Tooltip的JSON格式化，避免undefined显示
const formatJsonForTooltip = (content) => {
  if (content === undefined || content === null) return '';
  return formatJson(content);
};

// 添加防抖的搜索方法
const debouncedSearch = (() => {
  let timer = null;
  return (value) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      apiNameSearch.value = value;
      page.value = 1;
    }, 300);
  };
})();

// 批量删除 API
const handleBatchDeleteApi = async () => {
  if (!selectedRowKeys.value.length) {
    message.warning('请选择要删除的API');
    return;
  }

  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }

  try {
    // 新接口和参数格式
    const res = await http.delete(`/knowledge_api/${relationId}/delete_api_entities`, {
      data: { ids: selectedRowKeys.value }
    });

    if (res.data.code === 200) {
      message.success(res.data.message || '批量删除成功');
      selectedRowKeys.value = [];
      emit('refresh');
      // emit('close'); // 删除后不自动关闭弹窗
    } else {
      message.error(res.data.message || '批量删除失败');
    }
  } catch (error) {
    message.error('批量删除失败');
    console.error('批量删除失败:', error);
  }
};
</script>

<style scoped>
/* Method 标签样式 */
.method-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
  transition: all 0.3s;
}

.method-tag:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

/* 自定义列按钮样式 */
.column-settings-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.column-settings-btn :deep(.anticon) {
  display: flex;
  align-items: center;
  margin-right: 4px;
  font-size: 14px;
}

/* 自定义列弹窗样式 */
.column-settings-modal :deep(.ant-modal-content) {
  border-radius: 8px;
  overflow: hidden;
}

.column-settings-modal :deep(.ant-modal-header) {
  margin-bottom: 0;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.column-settings-modal :deep(.ant-modal-title) {
  font-size: 16px;
  font-weight: 500;
}

.column-settings-content {
  padding: 16px 24px;
}

.column-settings-header {
  padding-bottom: 12px;
  margin-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.column-settings-body {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 8px;
}

.column-settings-body::-webkit-scrollbar {
  width: 6px;
}

.column-settings-body::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.column-settings-body::-webkit-scrollbar-track {
  background-color: transparent;
}

.column-checkbox {
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s;
}

.column-checkbox:hover {
  background-color: #f5f5f5;
}

/* 美化复选框样式 */
.column-settings-modal :deep(.ant-checkbox-wrapper) {
  font-size: 14px;
}

.column-settings-modal :deep(.ant-checkbox) {
  top: 0;
}

.column-settings-modal :deep(.ant-checkbox-inner) {
  border-radius: 4px;
}

.column-settings-modal :deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.column-settings-modal :deep(.ant-checkbox-wrapper:hover .ant-checkbox-inner),
.column-settings-modal :deep(.ant-checkbox:hover .ant-checkbox-inner) {
  border-color: #1890ff;
}

/* 嵌入按钮样式 */
.embed-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.embed-btn :deep(.anticon) {
  display: flex;
  align-items: center;
  margin-right: 4px;
  font-size: 14px;
}

/* 标签容器样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
  min-height: 24px;
}

.api-tag {
  margin: 0;
  font-size: 12px;
  line-height: 20px;
  height: 22px;
  padding: 0 7px;
  border-radius: 4px;
  transition: all 0.3s;
}

.api-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.no-tags {
  color: #bfbfbf;
  font-size: 12px;
}

/* 内容预览样式 */
.content-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  width: 150px; /* 固定宽度 */
}

.preview-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px; /* 为查看按钮预留空间 */
}

.view-btn {
  padding: 0;
  height: 24px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  transition: all 0.3s;
}

.view-btn:hover {
  color: #40a9ff;
  transform: scale(1.1);
}

/* 详情弹窗样式 */
.detail-content {
  max-height: 60vh;
  overflow-y: auto;
  padding: 16px;
  background: #fafafa;
  border-radius: 4px;
}

/* 参数表格样式 */
.parameter-table {
  width: 100%;
  border-collapse: collapse;
}

/* 请求体样式 */
.schema-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.schema-item {
  background: #fff;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.schema-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #262626;
}

.schema-properties {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.property-item {
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background: #fafafa;
}

.property-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.property-name {
  font-weight: 500;
  color: #262626;
}

.property-description {
  font-size: 13px;
  color: #595959;
  margin-top: 4px;
}

/* 响应样式 */
.responses-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.response-item {
  background: #fff;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.response-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.response-description {
  color: #595959;
  font-size: 14px;
}

.response-schema {
  background: #fafafa;
  border-radius: 4px;
  padding: 12px;
  margin-top: 8px;
}

.json-content {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #333;
}

/* 美化滚动条 */
.detail-content::-webkit-scrollbar {
  width: 6px;
}

.detail-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.detail-content::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 批量删除按钮样式 */
.batch-delete-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.batch-delete-btn.ant-btn-danger {
  background-color: #ff4d4f; /* 红色背景 */
  border-color: #ff4d4f;   /* 红色边框 */
  color: #fff; /* 白色文本 */
}

.batch-delete-btn.ant-btn-danger:hover {
  background-color: #ff7875; /* 悬停时的红色 */
  border-color: #ff7875;   /* 悬停时的红色边框 */
  color: #fff;
}

.batch-delete-btn :deep(.anticon) {
  display: flex;
  align-items: center;
  margin-right: 4px;
  font-size: 14px;
}

.batch-delete-btn.ant-btn-danger :deep(.anticon) {
  color: #fff; /* 白色图标 */
}

/* 自定义列按钮样式 */
.column-settings-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.column-settings-btn :deep(.anticon) {
  display: flex;
  align-items: center;
  margin-right: 4px;
  font-size: 14px;
}
</style>

package com.qfnu.interceptor;

import com.qfnu.utils.JwtUtil;
import io.jsonwebtoken.Claims;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.servlet.HandlerInterceptor;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

public class TokenInterceptor implements HandlerInterceptor {

    private static final List<String> EXCLUDED_PATHS = Arrays.asList(
            "/AuthUser/login",
            "/AuthUser/oauth-complete-login", // OAuth登录接口
            "/register",
            "/swagger-ui/**",
            "/swagger-resources/**",
            "/v3/api-docs/**"
    );

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestUri = request.getRequestURI();
        String method = request.getMethod();

        System.out.println("=== TokenInterceptor 拦截请求 ===");
        System.out.println("请求URI: " + requestUri);
        System.out.println("请求方法: " + method);

        for (String path : EXCLUDED_PATHS) {
            if (isMatch(requestUri, path)) {
                System.out.println("匹配到排除路径: " + path + " -> 放行");
                return true; // 匹配到排除路径，直接放行
            }
        }

        System.out.println("未匹配到排除路径，检查Token...");
        // 获取请求头中的 Token
        String token = request.getHeader("Authorization");
        System.out.println("Authorization头: " + token);

        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
            System.out.println("提取的Token: " + (token.length() > 20 ? token.substring(0, 20) + "..." : token));

            // 验证 Token
            if (JwtUtil.validateToken(token)) {
                System.out.println("Token验证成功 -> 放行");
                // 解析 Token 获取用户信息
                Claims claims = JwtUtil.getClaimsFromToken(token);
                // 可以将用户信息存入请求属性，供后续控制器使用
                request.setAttribute("id", claims.get("id"));
                request.setAttribute("role", claims.get("role"));

                // 设置 Spring Security 的认证信息
                Authentication authentication = new UsernamePasswordAuthenticationToken(claims.getSubject(), null, null);
                SecurityContextHolder.getContext().setAuthentication(authentication);

                return true;
            } else {
                System.out.println("Token验证失败");
            }
        } else {
            System.out.println("没有找到有效的Authorization头");
        }

        // Token 验证失败，返回 401 状态码
        System.out.println("Token验证失败，返回401");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        return false;
    }

    private boolean isMatch(String requestUri, String pattern) {
        if (pattern.endsWith("/**")) {
            return requestUri.startsWith(pattern.substring(0, pattern.length() - 3));
        }
        return requestUri.equals(pattern);
    }
}
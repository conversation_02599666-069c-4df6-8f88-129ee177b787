package com.qfnu.converter;
import com.qfnu.model.po.SysFeaturePO;
import com.qfnu.model.dto.SysFeatureDTO;
import com.qfnu.model.vo.SysFeatureVO;
import com.qfnu.model.param.SysFeatureParam;

import com.qfnu.model.po.SysToolPO;
import com.qfnu.model.dto.SysToolDTO;
import com.qfnu.model.vo.SysToolVO;
import com.qfnu.model.param.SysToolParam;

import com.qfnu.model.po.SysReviewPO;
import com.qfnu.model.dto.SysReviewDTO;
import com.qfnu.model.vo.SysReviewVO;
import com.qfnu.model.param.SysReviewParam;

import com.qfnu.model.po.SysUserManagementPO;
import com.qfnu.model.dto.SysUserManagementDTO;
import com.qfnu.model.vo.SysUserManagementVO;
import com.qfnu.model.param.SysUserManagementParam;

import com.qfnu.model.po.SysUsersPO;
import com.qfnu.model.dto.SysUsersDTO;
import com.qfnu.model.vo.SysUsersVO;
import com.qfnu.model.param.SysUsersParam;

import com.qfnu.model.po.ZtProjectPO;
import com.qfnu.model.dto.ZtProjectDTO;
import com.qfnu.model.vo.ZtProjectVO;
import com.qfnu.model.param.ZtProjectParam;

import com.qfnu.model.po.ZtProjectPO;
import com.qfnu.model.dto.ZtProjectDTO;
import com.qfnu.model.vo.ZtProjectVO;
import com.qfnu.model.param.ZtProjectParam;

import com.qfnu.model.po.ZtProjectPO;
import com.qfnu.model.dto.ZtProjectDTO;
import com.qfnu.model.vo.ZtProjectVO;
import com.qfnu.model.param.ZtProjectParam;

import com.qfnu.model.po.SysUserManagementPO;
import com.qfnu.model.dto.SysUserManagementDTO;
import com.qfnu.model.vo.SysUserManagementVO;
import com.qfnu.model.param.SysUserManagementParam;

import com.qfnu.model.po.SysUsersPO;
import com.qfnu.model.dto.SysUsersDTO;
import com.qfnu.model.vo.SysUsersVO;
import com.qfnu.model.param.SysUsersParam;

import com.qfnu.model.po.SysUserManagementPO;
import com.qfnu.model.vo.SysUserManagementVO;
import com.qfnu.model.param.SysUserManagementParam;

import com.qfnu.model.po.SysUsersPO;

import com.qfnu.model.param.SysUsersParam;


import org.mapstruct.Mapper;

import java.util.List;

/**
 * mapstruct对象转换
 */
@Mapper(componentModel = "spring")
public interface BaseConverter {
    // ZtProject 转换方法
    ZtProjectPO convertToZtProjectPO(ZtProjectParam param);

    List<ZtProjectPO> convertToZtProjectPO(List<ZtProjectParam> paramList);

    ZtProjectVO convertToZtProjectVO(ZtProjectPO po);

    List<ZtProjectVO> convertToZtProjectVO(List<ZtProjectPO> poList);

    // SysUsers 转换方法
    SysUsersPO convertToSysUsersPO(SysUsersParam param);

    List<SysUsersPO> convertToSysUsersPO(List<SysUsersParam> paramList);

    SysUsersVO convertToSysUsersVO(SysUsersPO po);

    List<SysUsersVO> convertToSysUsersVO(List<SysUsersPO> poList);

    // SysUserManagement 转换方法
    SysUserManagementPO convertToSysUserManagementPO(SysUserManagementParam param);

    List<SysUserManagementPO> convertToSysUserManagementPO(List<SysUserManagementParam> paramList);

    SysUserManagementVO convertToSysUserManagementVO(SysUserManagementPO po);

    List<SysUserManagementVO> convertToSysUserManagementVO(List<SysUserManagementPO> poList);

    // SysReview 转换方法
    SysReviewPO convertToSysReviewPO(SysReviewParam param);

    List<SysReviewPO> convertToSysReviewPO(List<SysReviewParam> paramList);

    SysReviewVO convertToSysReviewVO(SysReviewPO po);

    List<SysReviewVO> convertToSysReviewVO(List<SysReviewPO> poList);
    
    // SysReview PO to DTO conversion
    SysReviewDTO convertToSysReviewDTO(SysReviewPO po);
    
    List<SysReviewDTO> convertToSysReviewDTO(List<SysReviewPO> poList);

    // SysTool 转换方法
    SysToolPO convertToSysToolPO(SysToolParam param);

    List<SysToolPO> convertToSysToolPO(List<SysToolParam> paramList);

    SysToolVO convertToSysToolVO(SysToolPO po);

    List<SysToolVO> convertToSysToolVO(List<SysToolPO> poList);

    // SysFeature 转换方法
    SysFeaturePO convertToSysFeaturePO(SysFeatureParam param);

    List<SysFeaturePO> convertToSysFeaturePO(List<SysFeatureParam> paramList);

    SysFeatureVO convertToSysFeatureVO(SysFeaturePO po);

    List<SysFeatureVO> convertToSysFeatureVO(List<SysFeaturePO> poList);
}












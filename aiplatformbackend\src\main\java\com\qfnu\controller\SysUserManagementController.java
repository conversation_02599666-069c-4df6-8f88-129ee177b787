package com.qfnu.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qfnu.common.ResponseVO;
import com.qfnu.common.annotation.RequiredPermission;
import com.qfnu.model.param.SysUserManagementQueryParam;
import com.qfnu.model.po.SysUserManagementPO;
import com.qfnu.service.SysUserManagementService;
import com.qfnu.model.param.SysUserManagementParam;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

@Api(tags = "SysUserManagement接口")
@RestController
@RequestMapping("/api/SysUserManagement")
public class SysUserManagementController {
    
    @Resource
    private SysUserManagementService sysUserManagementService;

    @RequiredPermission({"super,admin"})
    @ApiOperation("新增用户接口")
    @PostMapping("/save")
    public ResponseVO<Integer> save(@RequestBody SysUserManagementParam param) {
        int i = sysUserManagementService.save(param);
        if (i > 0) {
            return ResponseVO.success(200);
        } else if (i == -1) {
          return ResponseVO.validFail("用户已存在");
        }else {
            return ResponseVO.validFail("新增失败，稍后再试");
        }
    }

//    @RequiredPermission({"super,admin"})
//    @ApiOperation("批量新增")
//    @PostMapping("/saveBatch")
//    public ResponseVO<Integer> saveBatch(@RequestBody List<SysUserManagementParam> paramList) {
//        return ResponseVO.success(sysUserManagementService.saveBatch(paramList));
//    }

    @RequiredPermission({"super,admin"})
    @ApiOperation("编辑用户信息")
    @PostMapping("/update")
    public ResponseVO<Integer> update(@RequestBody SysUserManagementParam param) {
        return ResponseVO.success(sysUserManagementService.update(param),200);
    }

    @RequiredPermission({"super,admin"})
    @ApiOperation("删除用户")
    @PostMapping("/delete")
    public ResponseVO<Integer> delete(@RequestBody SysUserManagementParam param) {
        return ResponseVO.success(sysUserManagementService.delete(param));
    }
    
//    @ApiOperation("根据ID查询")
//    @PostMapping("/getById")
//    public ResponseVO<SysUserManagementVO> getById(@RequestBody SysUserManagementParam param) {
//        return ResponseVO.success(sysUserManagementService.getById(param.getManagementId()));
//    }
    
    @ApiOperation("列表分页筛选查询")
    @PostMapping("/list")
    public ResponseVO<IPage<SysUserManagementPO>> getList(@RequestBody SysUserManagementQueryParam param) {
        return ResponseVO.success(sysUserManagementService.getPageList(param),"获取成功",200);
    }

//    @RequiredPermission({"super,admin"})
//    @ApiOperation("批量新增或更新")
//    @PostMapping("/batchAddOrUpdate")
//    public ResponseVO<List<SysUserManagementVO>> batchAddOrUpdate(@RequestBody List<SysUserManagementParam> paramList) {
//        return ResponseVO.success(sysUserManagementService.batchAddOrUpdate(paramList),"操作成功",200);
//    }

}
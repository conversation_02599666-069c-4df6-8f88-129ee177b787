package ${package}.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import ${package}.util.common.BaseVO;
import java.io.Serializable;
${imports}

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "${voClassName}", description = "${className}视图对象")
public class ${voClassName} extends BaseVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
${fields}
} 
import React, { useState, useEffect } from 'react';
import {
  Modal,
  Table,
  Button,
  Select,
  Input,
  Tag,
  message,
  Pagination,
  Row,
  Col,
  Space
} from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';

const { Option } = Select;

const RelationConfirmDialog = ({ 
  visible, 
  onClose, 
  knowledgeBaseId,
  onConfirmed 
}) => {
  const [relations, setRelations] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [loading, setLoading] = useState(false);
  const [confirming, setConfirming] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [total, setTotal] = useState(0);
  const [statusFilter, setStatusFilter] = useState('');
  const [taskIdFilter, setTaskIdFilter] = useState('');

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      fixed: 'left',
      sorter: true,
    },
    {
      title: '任务ID',
      dataIndex: 'task_id',
      key: 'task_id',
      width: 120,
      sorter: true,
      render: (taskId) => {
        return taskId ? (
          <Tag color="blue" size="small">
            {taskId}
          </Tag>
        ) : (
          <span style={{ color: '#999' }}>-</span>
        );
      },
    },
    {
      title: '上游API',
      key: 'upstream_api',
      width: 220,
      ellipsis: true,
      render: (_, record) => {
        const api = record.upstream_api;
        if (!api) return <span style={{ color: '#999' }}>-</span>;
        
        return (
          <Space direction="vertical" size="small">
            <Tag color={getMethodColor(api.method)} size="small">
              {api.method}
            </Tag>
            <span style={{ fontSize: '12px', color: '#666' }}>
              {api.path}
            </span>
          </Space>
        );
      },
    },
    {
      title: '数据模型',
      key: 'data_model',
      width: 150,
      ellipsis: true,
      render: (_, record) => {
        const model = record.data_model;
        return model?.name ? (
          <Tag color="green" size="small">
            {model.name}
          </Tag>
        ) : (
          <span style={{ color: '#999' }}>-</span>
        );
      },
    },
    {
      title: '下游API',
      key: 'downstream_api',
      width: 220,
      ellipsis: true,
      render: (_, record) => {
        const api = record.downstream_api;
        if (!api) return <span style={{ color: '#999' }}>-</span>;
        
        return (
          <Space direction="vertical" size="small">
            <Tag color={getMethodColor(api.method)} size="small">
              {api.method}
            </Tag>
            <span style={{ fontSize: '12px', color: '#666' }}>
              {api.path}
            </span>
          </Space>
        );
      },
    },
    {
      title: '关系描述',
      dataIndex: 'relation_description',
      key: 'relation_description',
      ellipsis: true,
      render: (text) => text || '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const statusConfig = getStatusConfig(status);
        return (
          <Tag color={statusConfig.color} size="small">
            {statusConfig.text}
          </Tag>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 160,
      sorter: true,
      render: (time) => {
        return time ? new Date(time).toLocaleString('zh-CN') : '-';
      },
    },
  ];

  // 获取HTTP方法对应的颜色
  const getMethodColor = (method) => {
    const colorMap = {
      'GET': 'green',
      'POST': 'blue',
      'PUT': 'orange',
      'DELETE': 'red',
      'PATCH': 'purple'
    };
    return colorMap[method?.toUpperCase()] || 'default';
  };

  // 获取状态配置
  const getStatusConfig = (status) => {
    const configMap = {
      0: { color: 'orange', text: '待确认' },
      1: { color: 'green', text: '已确认' },
      2: { color: 'default', text: '已忽略' }
    };
    return configMap[status] || { color: 'default', text: '未知' };
  };

  // 加载关系列表
  const loadRelations = async () => {
    setLoading(true);
    try {
      let url = `/api_data_model_relations/${knowledgeBaseId}?page=${currentPage}&page_size=${pageSize}`;
      
      if (statusFilter) {
        url += `&status=${statusFilter}`;
      }
      
      const response = await fetch(url);
      const result = await response.json();
      
      if (result.code === 200) {
        let relationsList = result.data.list;
        
        // 如果有任务ID筛选，进行前端过滤
        if (taskIdFilter) {
          relationsList = relationsList.filter(item => 
            item.task_id && item.task_id.toLowerCase().includes(taskIdFilter.toLowerCase())
          );
        }
        
        setRelations(relationsList);
        setTotal(result.data.total);
      } else {
        message.error(result.message || '加载关系列表失败');
      }
    } catch (error) {
      console.error('加载关系列表失败:', error);
      message.error('加载关系列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理筛选变化
  const handleFilterChange = () => {
    setCurrentPage(1);
    loadRelations();
  };

  // 确认关系
  const confirmRelations = async () => {
    if (selectedRows.length === 0) {
      message.warning('请选择要确认的关系');
      return;
    }
    
    setConfirming(true);
    try {
      const relationIds = selectedRows.map(item => item.id);
      
      const response = await fetch(`/confirm_api_data_model_relations/${knowledgeBaseId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          relation_ids: relationIds
        })
      });
      
      const result = await response.json();
      
      if (result.code === 200) {
        const { confirmed_count, failed_relations } = result.data;
        
        if (failed_relations && failed_relations.length > 0) {
          message.warning(`成功确认 ${confirmed_count} 个关系，${failed_relations.length} 个关系确认失败`);
        } else {
          message.success(`成功确认 ${confirmed_count} 个关系`);
        }
        
        // 重新加载列表
        loadRelations();
        
        // 清空选择
        setSelectedRowKeys([]);
        setSelectedRows([]);
        
        // 触发确认完成回调
        onConfirmed && onConfirmed(result.data);
      } else {
        message.error(result.message || '确认关系失败');
      }
    } catch (error) {
      console.error('确认关系失败:', error);
      message.error('确认关系失败');
    } finally {
      setConfirming(false);
    }
  };

  // 表格行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys, rows) => {
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
    },
  };

  // 重置数据
  const resetData = () => {
    setRelations([]);
    setSelectedRowKeys([]);
    setSelectedRows([]);
    setCurrentPage(1);
    setStatusFilter('');
    setTaskIdFilter('');
  };

  // 监听visible变化
  useEffect(() => {
    if (visible) {
      loadRelations();
    } else {
      resetData();
    }
  }, [visible]);

  // 监听分页变化
  useEffect(() => {
    if (visible) {
      loadRelations();
    }
  }, [currentPage, pageSize]);

  return (
    <Modal
      title="关系确认"
      open={visible}
      onCancel={onClose}
      width="95%"
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          loading={confirming}
          disabled={selectedRows.length === 0}
          onClick={confirmRelations}
        >
          确认选中关系 ({selectedRows.length})
        </Button>,
      ]}
    >
      {/* 筛选条件 */}
      <div style={{ marginBottom: 16, padding: 16, backgroundColor: '#fafafa', borderRadius: 4 }}>
        <Row gutter={16}>
          <Col span={4}>
            <Select
              placeholder="选择状态"
              value={statusFilter}
              onChange={setStatusFilter}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="0">待确认</Option>
              <Option value="1">已确认</Option>
              <Option value="2">已忽略</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Input
              placeholder="输入任务ID筛选"
              value={taskIdFilter}
              onChange={(e) => setTaskIdFilter(e.target.value)}
              onPressEnter={handleFilterChange}
              allowClear
              suffix={
                <SearchOutlined 
                  style={{ cursor: 'pointer' }} 
                  onClick={handleFilterChange} 
                />
              }
            />
          </Col>
          <Col span={4}>
            <Button 
              type="primary" 
              icon={<ReloadOutlined />} 
              onClick={loadRelations}
            >
              刷新
            </Button>
          </Col>
          <Col span={12} style={{ textAlign: 'right', lineHeight: '32px' }}>
            <span style={{ color: '#666' }}>
              已选择 {selectedRows.length} 项
            </span>
          </Col>
        </Row>
      </div>

      {/* 关系列表表格 */}
      <Table
        columns={columns}
        dataSource={relations}
        rowSelection={rowSelection}
        loading={loading}
        pagination={false}
        scroll={{ x: 1200, y: 400 }}
        size="small"
        rowKey="id"
      />

      {/* 分页 */}
      <div style={{ marginTop: 16, textAlign: 'right' }}>
        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={total}
          showSizeChanger
          showQuickJumper
          showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`}
          onChange={(page, size) => {
            setCurrentPage(page);
            setPageSize(size);
          }}
          onShowSizeChange={(current, size) => {
            setCurrentPage(1);
            setPageSize(size);
          }}
        />
      </div>
    </Modal>
  );
};

export default RelationConfirmDialog;

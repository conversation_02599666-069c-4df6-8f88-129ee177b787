package ${package}.converter;

import ${package}.model.po.${className}PO;
import ${package}.model.dto.${className}DTO;
import ${package}.model.vo.${className}VO;
import ${package}.model.param.${className}Param;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper(componentModel = "spring")
public interface ${className}Converter {
    
    ${className}Converter INSTANCE = Mappers.getMapper(${className}Converter.class);
    
    ${className}PO paramToPo(${className}Param param);
    
    List<${className}PO> paramToPoList(List<${className}Param> paramList);
    
    ${className}VO poToVo(${className}PO po);
    
    List<${className}VO> poToVoList(List<${className}PO> poList);
} 
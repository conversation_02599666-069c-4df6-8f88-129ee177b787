#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量化API匹配使用示例
演示如何使用阿里百炼向量模型进行API匹配
"""

import os
import json
from vector_api_matcher import VectorAPIMatching


def load_api_data_from_json(file_path: str):
    """从JSON文件加载API数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载API数据失败: {str(e)}")
        return []


def test_vector_matching():
    """测试向量化匹配功能"""
    
    # 设置API密钥（请替换为您的实际密钥）
    # 方式1: 直接设置环境变量
    # os.environ['DASHSCOPE_API_KEY'] = 'your-api-key-here'
    
    # 方式2: 在代码中传入（不推荐在生产环境中使用）
    # api_key = 'your-api-key-here'
    
    # 测试用的API文档数据
    test_api_docs = [
        {
            "id": "1",
            "method": "POST",
            "path": "/openapi/v1.0/project/projects",
            "summary": "项目创建",
            "tags": ["项目管理"]
        },
        {
            "id": "2", 
            "method": "POST",
            "path": "/openapi/v1.0/project/projects/{projectId}/approve_rules",
            "summary": "审批规则创建",
            "tags": ["审批管理"]
        },
        {
            "id": "3",
            "method": "POST",
            "path": "/openapi/v1.0/project/projects/{projectId}/assets/{assetId}/accounts",
            "summary": "资产账号创建",
            "tags": ["资产管理"]
        },
        {
            "id": "4",
            "method": "POST",
            "path": "/openapi/v1.0/project/projects/{projectId}/password_plans:add_accounts",
            "summary": "关联改密计划账号",
            "tags": ["密码管理"]
        },
        {
            "id": "5",
            "method": "GET",
            "path": "/openapi/v1.0/project/projects",
            "summary": "项目列表查询",
            "tags": ["项目管理"]
        },
        {
            "id": "6",
            "method": "PUT",
            "path": "/openapi/v1.0/project/projects/{projectId}",
            "summary": "项目信息更新",
            "tags": ["项目管理"]
        },
        {
            "id": "7",
            "method": "DELETE",
            "path": "/openapi/v1.0/project/projects/{projectId}",
            "summary": "项目删除",
            "tags": ["项目管理"]
        }
    ]
    
    # 测试查询列表
    test_queries = [
        "创建项目",
        "创建审批规则", 
        "创建资产账号",
        "查询项目列表",
        "更新项目信息",
        "删除项目",
        "项目管理相关操作"
    ]
    
    try:
        # 初始化匹配器
        matcher = VectorAPIMatching()
        
        print("=== 向量化API匹配测试 ===\n")
        
        for i, query in enumerate(test_queries, 1):
            print(f"测试 {i}: {query}")
            print("-" * 50)
            
            # 执行匹配
            results = matcher.find_best_matching_apis(
                api_description=query,
                api_documents=test_api_docs,
                top_k=3,
                similarity_threshold=0.6
            )
            
            if results:
                print(f"找到 {len(results)} 个匹配结果:")
                for j, result in enumerate(results, 1):
                    api_data = result['api_data']
                    score = result['similarity_score']
                    print(f"  {j}. [{api_data['method']}] {api_data['path']}")
                    print(f"     描述: {api_data['summary']}")
                    print(f"     相似度: {score:.4f}")
            else:
                print("未找到匹配的API")
            
            print("\n")
        
        # 演示批量匹配
        print("=== 批量匹配演示 ===")
        batch_results = {}
        
        for query in test_queries[:3]:  # 只测试前3个查询
            results = matcher.find_best_matching_apis(
                api_description=query,
                api_documents=test_api_docs,
                top_k=1,
                similarity_threshold=0.7
            )
            
            if results:
                best_match = results[0]
                batch_results[query] = {
                    "best_match": {
                        "method": best_match['api_data']['method'],
                        "path": best_match['api_data']['path'],
                        "summary": best_match['api_data']['summary'],
                        "similarity": best_match['similarity_score']
                    }
                }
            else:
                batch_results[query] = {"best_match": None}
        
        print("批量匹配结果:")
        print(json.dumps(batch_results, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        print("请检查:")
        print("1. 是否设置了正确的DASHSCOPE_API_KEY环境变量")
        print("2. 网络连接是否正常")
        print("3. API密钥是否有效")


def compare_with_traditional_matching():
    """对比传统匹配方法和向量化匹配方法"""
    
    def traditional_keyword_match(query: str, api_docs: list) -> list:
        """简单的关键词匹配方法"""
        results = []
        query_lower = query.lower()
        
        for api_doc in api_docs:
            summary_lower = api_doc.get('summary', '').lower()
            path_lower = api_doc.get('path', '').lower()
            
            score = 0
            if query_lower in summary_lower:
                score += 2
            if query_lower in path_lower:
                score += 1
            
            # 简单的关键词匹配
            query_words = query_lower.split()
            for word in query_words:
                if word in summary_lower:
                    score += 1
                if word in path_lower:
                    score += 0.5
            
            if score > 0:
                results.append({
                    'api_data': api_doc,
                    'score': score
                })
        
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:3]
    
    # 测试数据
    test_api_docs = [
        {"method": "POST", "path": "/projects", "summary": "项目创建"},
        {"method": "POST", "path": "/projects/{id}/rules", "summary": "审批规则创建"},
        {"method": "GET", "path": "/projects", "summary": "项目列表查询"},
        {"method": "POST", "path": "/accounts", "summary": "账号创建"}
    ]
    
    query = "创建项目"
    
    print("=== 传统匹配 vs 向量化匹配对比 ===\n")
    
    # 传统匹配
    print("传统关键词匹配结果:")
    traditional_results = traditional_keyword_match(query, test_api_docs)
    for i, result in enumerate(traditional_results, 1):
        api = result['api_data']
        print(f"  {i}. {api['summary']} (得分: {result['score']})")
    
    print()
    
    # 向量化匹配
    try:
        matcher = VectorAPIMatching()
        print("向量化匹配结果:")
        vector_results = matcher.find_best_matching_apis(
            api_description=query,
            api_documents=test_api_docs,
            top_k=3,
            similarity_threshold=0.5
        )
        
        for i, result in enumerate(vector_results, 1):
            api = result['api_data']
            print(f"  {i}. {api['summary']} (相似度: {result['similarity_score']:.4f})")
            
    except Exception as e:
        print(f"向量化匹配失败: {str(e)}")


if __name__ == "__main__":
    # 运行测试
    test_vector_matching()
    
    print("\n" + "="*60 + "\n")
    
    # 运行对比测试
    compare_with_traditional_matching()

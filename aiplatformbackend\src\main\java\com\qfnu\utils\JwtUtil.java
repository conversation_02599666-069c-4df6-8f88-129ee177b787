package com.qfnu.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class JwtUtil {

    // 密钥，可根据实际情况修改
    private static final String SECRET_KEY = "5fDVKshbZFvcx/yiNe+1948FKthnI/PpE3yCyVyacHw=";
    // 正常访问令牌过期时间，单位为毫秒，这里设置为 1 小时
    private static final long NORMAL_ACCESS_TOKEN_EXPIRATION_TIME = 1000 * 60 * 60;
    // 记住密码时访问令牌过期时间，单位为毫秒，这里设置为 7 天
    private static final long REMEMBER_ME_ACCESS_TOKEN_EXPIRATION_TIME = 1000 * 60 * 60 * 24 * 7;
    // 刷新令牌过期时间，单位为毫秒，这里设置为 7 天
    private static final long REFRESH_TOKEN_EXPIRATION_TIME = 1000 * 60 * 60 * 24 * 7;

    /**
     * 生成访问令牌
     * @param id 用户 ID
     * @param username 用户名称
     * @param role 用户角色
     * @param rememberMe 是否记住密码
     * @return 访问令牌
     */
    public static String generateAccessToken(String id, String username, String role, Boolean rememberMe) {
        long expirationTime = rememberMe ? REMEMBER_ME_ACCESS_TOKEN_EXPIRATION_TIME : NORMAL_ACCESS_TOKEN_EXPIRATION_TIME;
        return generateToken(id, username, role, expirationTime);
    }

    /**
     * 生成刷新令牌
     * @param id 用户 ID
     * @param username 用户名称
     * @param role 用户角色
     * @return 刷新令牌
     */
    public static String generateRefreshToken(String id, String username, String role) {
        return generateToken(id, username, role, REFRESH_TOKEN_EXPIRATION_TIME);
    }

    private static String generateToken(String id, String username, String role, long expirationTime) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("id", id);
        claims.put("role", role);
        claims.put("username", username);
        // 添加时间戳
        claims.put("timestamp", System.currentTimeMillis());

        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + expirationTime))
                .signWith(SignatureAlgorithm.HS256, SECRET_KEY)
                .compact();
    }

    /**
     * 验证令牌
     * @param token 令牌
     * @return 是否验证通过
     */
    public static boolean validateToken(String token) {
        try {
            Jwts.parser().setSigningKey(SECRET_KEY).parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 解析令牌获取 Claims
     * @param token 令牌
     * @return Claims
     */
    public static Claims getClaimsFromToken(String token) {
        return Jwts.parser().setSigningKey(SECRET_KEY).parseClaimsJws(token).getBody();
    }

    /**
     * 无感刷新访问令牌
     * @param refreshToken 刷新令牌
     * @param rememberMe 是否记住密码
     * @return 新的访问令牌
     */
    public static String refreshAccessToken(String refreshToken, Boolean rememberMe) {
        if (validateToken(refreshToken)) {
            Claims claims = getClaimsFromToken(refreshToken);
            String id = (String) claims.get("id");
            String username = (String) claims.get("username");
            String role = (String) claims.get("role");
            return generateAccessToken(id, username, role, rememberMe);
        }
        return null;
    }
}
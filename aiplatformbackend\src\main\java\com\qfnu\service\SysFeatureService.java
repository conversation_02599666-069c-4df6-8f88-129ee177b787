package com.qfnu.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qfnu.converter.SysFeatureConverter;
import com.qfnu.mapper.primary.SysReviewMapper;
import com.qfnu.model.dto.SysReviewDTO;
import com.qfnu.model.po.SysFeaturePO;
import com.qfnu.model.vo.SysFeatureVO;
import com.qfnu.model.param.SysFeatureParam;
import com.qfnu.mapper.primary.SysFeatureMapper;
import com.qfnu.converter.BaseConverter;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

@Slf4j
@Service
public class SysFeatureService extends ServiceImpl<SysFeatureMapper, SysFeaturePO> {

    @Resource
    private SysFeatureMapper sysFeatureMapper;
    
    @Resource
    private BaseConverter converter;

    @Resource
    private SysFeatureConverter featureConverter;
    @Autowired
    private SysReviewMapper sysReviewMapper;

    @Transactional(rollbackFor = Exception.class)
    public int save(SysFeatureParam param,HttpServletRequest request) {

        SysFeaturePO sysFeaturePO = new SysFeaturePO();

        sysFeaturePO.setIsDeleted("0");
        sysFeaturePO.setCreateDatetime(new Date());
        sysFeaturePO.setGroupResponsible(param.getGroupResponsible());
        sysFeaturePO.setPersonResponsible(param.getPersonResponsible());
        sysFeaturePO.setLevel(param.getLevel());
        sysFeaturePO.setModuleName(param.getModuleName());
        sysFeaturePO.setParentId(param.getParentId());
        sysFeaturePO.setProductId(param.getProductId());
        sysFeaturePO.setCreator((String) request.getAttribute("id"));
        sysFeaturePO.setFeatureId(UUID.randomUUID().toString());

        return sysFeatureMapper.insert(sysFeaturePO);
    }
    
    @Transactional(rollbackFor = Exception.class)
    public int saveBatch(List<SysFeatureParam> paramList) {
        List<SysFeaturePO> poList = converter.convertToSysFeaturePO(paramList);
        return saveBatch(poList) ? poList.size() : 0;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public int myUpdate(SysFeatureParam param, HttpServletRequest request) {

        param.setUpdateDatetime(new Date());
        param.setModifier((String) request.getAttribute("id"));
        return sysFeatureMapper.myUpdate(param) ? 1 : 0;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public int myDelete(SysFeatureParam param) {
        param.setIsDeleted("1");
        return sysFeatureMapper.myUpdate(param) ? 1 : 0;
    }
    
    public SysFeatureVO getById(Long id) {
        SysFeaturePO po = super.getById(id);
        if (po == null) {
            return null;
        }
        return converter.convertToSysFeatureVO(po);
    }
    
    public Page<SysFeatureVO> getList(SysFeatureParam param) {

        // 创建分页对象
        int current = param.getPageNum() != null ? param.getPageNum() : 1;
        int size = param.getPageSize() != null ? param.getPageSize() : 10;
        int offset = (current - 1) * size;
        
        Page<SysFeatureVO> page = new Page<>(current, size);
        
        // 添加日志输出查询参数
        log.info("分页查询参数: pageNum={}, pageSize={}, offset={}, product_id={}, module_name={}, level={}, person_responsible={}, group_responsible={}",
                current, size, offset, param.getProductId(),
                param.getModuleName(), param.getLevel(), param.getPersonResponsible(),param.getGroupResponsible());

        // 设置offset和size到param对象
        param.setOffset(Long.valueOf(offset));
        param.setPageSize(size);
        
        IPage<SysFeatureVO> dtoPage = sysFeatureMapper.getPageList(page, param);

        Integer count = sysFeatureMapper.getPageListCount(page,param);

        page.setTotal(count);
        page.setRecords(dtoPage.getRecords());

        return page;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public List<SysFeatureVO> batchAddOrUpdate(List<SysFeatureParam> paramList) {
        List<SysFeaturePO> poList = converter.convertToSysFeaturePO(paramList);
        saveOrUpdateBatch(poList);
        return converter.convertToSysFeatureVO(poList);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<SysFeaturePO> getTree(SysFeatureParam param) {
        // 获取所有特性列表
        List<SysFeaturePO> allFeatures = sysFeatureMapper.getFeaturesByProductId(param);

        // 构建树形结构
        List<SysFeaturePO> treeFeatures = buildTree(allFeatures);

        // 转换为VO返回
        return treeFeatures;
    }
    
    private List<SysFeaturePO> buildTree(List<SysFeaturePO> allFeatures) {
        // 存储根节点
        List<SysFeaturePO> rootFeatures = new ArrayList<>();
        
        // 使用Map存储所有节点，方便查找
        Map<String, SysFeaturePO> featureMap = new HashMap<>();
        for (SysFeaturePO feature : allFeatures) {
            feature.setChildren(new ArrayList<>());
            featureMap.put(feature.getFeatureId(), feature);
        }
        // 构建树形结构
        for (SysFeaturePO feature : allFeatures) {
            Long parentId = feature.getParentId();
            if (parentId == null) {
                // 没有父节点，作为根节点
                rootFeatures.add(feature);
            } else {
                // 有父节点，添加到父节点的children中
                SysFeaturePO parent = featureMap.get(String.valueOf(parentId));
                if (parent != null) {
                    parent.getChildren().add(feature);
                } else {
                    // 如果找不到父节点，作为根节点
                    rootFeatures.add(feature);
                }
            }
        }
        
        return rootFeatures;
    }
}
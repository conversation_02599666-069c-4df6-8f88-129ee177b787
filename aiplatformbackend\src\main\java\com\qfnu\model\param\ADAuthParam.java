package com.qfnu.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode
@ApiModel(value = "OAuthAuthParam", description = "OAuth2.0认证参数对象")
public class ADAuthParam {

    @ApiModelProperty(value = "授权码", required = true)
    private String code;

    @ApiModelProperty(value = "状态参数", required = true)
    private String state;

    @ApiModelProperty(value = "错误码")
    private String error;

    @ApiModelProperty(value = "错误描述")
    private String errorDescription;
}

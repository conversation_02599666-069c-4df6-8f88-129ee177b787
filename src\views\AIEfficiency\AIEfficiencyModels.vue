<template>
  <div class="ai-models">
    <div class="models-container">
      <AIModelCard
        v-for="model in models"
        :key="model.id"
        :modelData="model"
        @detailsClick="showDialog(model)"
        @apiClick="showAPIDialog(model)"
      />
    </div>
    <AIModelDialog
      v-model:visible="dialogVisible"
      :modelData="selectedModel"
    />
    <AIModelAPIDialog
      v-model:visible="apiDialogVisible"
      :modelData="selectedModel"
    />
  </div>
</template>

<script lang="ts" setup>
import AIModelCard from '@/components/AIModelCard.vue'
import AIModelDialog from './AIModelDialog.vue'
import AIModelAPIDialog from './AIModelAPIDialog.vue'
import { ref } from 'vue'

const dialogVisible = ref(false)
const apiDialogVisible = ref(false)
const selectedModel = ref({})

const showDialog = (model: any) => {
  selectedModel.value = model
  dialogVisible.value = true
}

const showAPIDialog = (model: any) => {
  selectedModel.value = model
  apiDialogVisible.value = true
}

const models = ref([
  {
    id: 1,
    name: 'qwen2.5:7b',
    description: '本地搭建，自主可控，数据安全',
    type: '语言模型',
    url: 'http://***********:11434/v1',
    creator: '陈泉有',
    group: '其他',
    tags: ['NLP', '数据安全'],
    createTime: '2023-12-01 17:30:00'
  },
  {
    id: 2,
    name: 'qwq:latest',
    description: '本地搭建，自主可控，数据安全',
    type: '语言模型',
    url: 'http://************:11434/v1',
    creator: '邱宇',
    group: 'TGFW测试',
    tags: ['NLP', '数据安全'],
    createTime: '2023-12-01 17:30:00'
  },
  {
    id: 3,
    name: 'deepseek-r1:32b',
    description: '本地搭建，自主可控，数据安全',
    type: '语言模型',
    url: 'http://************:11434/v1',
    creator: '邱宇',
    group: 'TGFW测试',
    tags: ['NLP', '数据安全'],
    createTime: '2023-12-01 17:30:00'
  },
  {
    id: 4,
    name: 'hengnao',
    description: '本地搭建，自主可控，数据安全',
    type: '语言模型',
    url: 'https://www.das-ai.com/open/api/chat',
    creator: '邱宇',
    group: 'TGFW测试',
    tags: ['NLP', '数据安全'],
    createTime: '2023-12-01 17:30:00'
  },

])
</script>

<style scoped>
.ai-models {
  padding: 24px;
}

.models-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 24px;
}

h1 {
  color: #1890ff;
  margin-bottom: 24px;
}
</style>
package com.qfnu.controller;

import com.qfnu.service.IndexService;
import com.qfnu.model.vo.DashboardStatsVO;
import com.qfnu.model.vo.ToolDistributionVO;
import com.qfnu.model.vo.TopRankingsVO;
import com.qfnu.common.ResponseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "首页接口")
@RestController
@RequestMapping("/api/index")
public class IndexController {

    @Resource
    private IndexService indexService;

    @ApiOperation("获取首页统计数据")
    @GetMapping("/stats")
    public ResponseVO<DashboardStatsVO> getStats() {
        return ResponseVO.success(indexService.getDashboardStats(),200);
    }

    @ApiOperation("获取排行榜数据")
    @GetMapping("/rankings")
    public ResponseVO<TopRankingsVO> getRankings() {
        return ResponseVO.success(indexService.getTopRankings(),200);
    }

    @ApiOperation("获取工具类别分布数据")
    @GetMapping("/distribution")
    public ResponseVO<ToolDistributionVO> getDistribution() {
        return ResponseVO.success(indexService.getToolDistribution(),200);
    }
}
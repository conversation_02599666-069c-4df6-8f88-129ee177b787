import { message } from "ant-design-vue";
import { h } from "vue";
import IconFont from "../iconFont/index";

message.config({ maxCount: 1 });

type MessageType = "success" | "info" | "warning" | "error" | "loading";

const getContent = (type: string, content: string) => {
  const renderIcon = h(IconFont, {
    type: type,
    style: {
      margin: "0 0 0 12px",
      color: "black",
      fontSize: "14px",
      top: 0,
    },
    onClick: () => {
      message.destroy();
    },
  });
  return h(
    "span",
    {
      style: {
        display: "inline-flex",
        justifyContent: "space-between",
        alignItems: "center",
      },
    },
    [content, renderIcon]
  );
};

interface IMessageType {
  (options: any): void;
  success: (options: any, time?: number) => void;
  info: (options: any, time?: number) => void;
  warning: (options: any, time?: number) => void;
  error: (options: any, time?: number) => void;
  loading: (options: any, time?: number) => void;
  destroy: () => void;
}

const IMessage = function(options: any) {
  const content = options.content;
  options.content = () => getContent(options.closeType, content);
  const type = options.type as MessageType;
  if (typeof (message as any)[type] === "function") {
    (message as any)[type](options);
  } else {
    (message as any).info(options);
  }
} as IMessageType;

["success", "info", "warning", "error", "loading"].forEach((type) => {
  (IMessage as any)[type] = (options: any, time?: number) => {
    if (typeof options === "object") {
      options.type = type;
      options.closeType = options.closeType ?? "icon-guanbi";
    } else {
      options = {
        content: options,
        duration: typeof time === "number" ? time : 3,
        closeType: "icon-guanbi",
        type,
      };
    }
    return IMessage(options);
  };
});

IMessage.destroy = function () {
  message.destroy();
};

export default IMessage; 
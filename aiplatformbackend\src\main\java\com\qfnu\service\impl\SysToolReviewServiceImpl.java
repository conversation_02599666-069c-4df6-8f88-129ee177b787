package com.qfnu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qfnu.common.enums.DeleteStatusEnum;
import com.qfnu.common.enums.PublishStatusEnum;
import com.qfnu.common.enums.ReviewStatusEnum;
import com.qfnu.converter.SysReviewMapper;
import com.qfnu.mapper.primary.SysToolMapper;
import com.qfnu.mapper.primary.SysToolReviewMapper;
import com.qfnu.model.param.SysReviewParam;
import com.qfnu.model.po.SysReviewPO;
import com.qfnu.model.po.SysToolPO;
import com.qfnu.model.vo.SysReviewVO;
import com.qfnu.service.SysToolReviewService;
import com.qfnu.model.dto.SysReviewDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class SysToolReviewServiceImpl implements SysToolReviewService {

    /**
     * 将字符串类型的审核状态转换为整数类型
     * @param status 字符串类型的状态
     * @return 整数类型的状态
     */
    private Integer convertApprovalStatus(String status) {
        try {
            return Integer.parseInt(status);
        } catch (NumberFormatException e) {
            return ReviewStatusEnum.PENDING.getCode();
        }
    }

    @Resource
    private SysToolReviewMapper sysToolReviewMapper;

    @Resource
    private SysReviewMapper reviewConverter;
    @Autowired
    private SysToolMapper sysToolMapper;


    @Override
    public String submitToolReview(SysReviewParam param) {
        return "";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reviewTool(SysReviewParam param) {
        // 更新审核记录状态
        SysReviewPO reviewPO = new SysReviewPO();
        reviewPO.setReviewId(param.getReviewId());
        reviewPO.setApprovalStatus(String.valueOf(param.getApprovalStatus()));
        reviewPO.setApprovalTime(new Date());
        reviewPO.setRelatedId(param.getRelatedId());
        reviewPO.setReviewer(param.getReviewer());
        sysToolReviewMapper.updateReviewStatus(reviewPO);

        // 根据审核结果处理工具状态
        Integer approvalStatus = convertApprovalStatus(String.valueOf(param.getApprovalStatus()));
        if (ReviewStatusEnum.APPROVED.getCode().equals(approvalStatus)) {
            // 审核通过，更新工具状态为已上架
            sysToolReviewMapper.updateToolStatus(param.getRelatedId(), String.valueOf(ReviewStatusEnum.APPROVED.getCode()));
            sysToolReviewMapper.updatePublish(param.getRelatedId(), String.valueOf(PublishStatusEnum.PUBLISHED.getCode()));
        } else if (ReviewStatusEnum.REJECTED.getCode().equals(approvalStatus)) {
            // 审核拒绝，删除工具
            sysToolReviewMapper.updateToolStatus(param.getRelatedId(), String.valueOf(ReviewStatusEnum.REJECTED.getCode()));
//            sysToolReviewMapper.updateToolDeleteStatus(param.getRelatedId(), String.valueOf(DeleteStatusEnum.DELETED.getCode()));
        }

        return true;
    }

    @Override
    public IPage<SysReviewVO> getReviewPage(SysReviewParam param) {
        IPage<SysReviewDTO> page = new Page<>(param.getPageNum(), param.getPageSize());
        IPage<SysReviewDTO> dtoPage = sysToolReviewMapper.selectReviewPage(page, param);
        return dtoPage.convert(reviewConverter::toVO);
    }

    @Override
    public SysReviewVO getReviewDetail(String reviewId) {
        SysReviewDTO reviewDTO = sysToolReviewMapper.selectReviewDetail(reviewId);
        return reviewConverter.toVO(reviewDTO);
    }
}